# AIRelief后端语音消息功能完整技术文档

## 1. 架构概览

### 1.1 系统架构图

```
前端层（用户界面）
├── 聊天页面 (ChatPage.vue)
├── 输入组件 (ChatInput.vue)  
├── 消息组件 (MessageItem.vue)
├── WebSocket客户端 (websocketClient.ts)
└── 录音适配器 (universalRecorder.ts)

后端层（业务处理）
├── WebSocket服务 (websocket_chat_service.py)
├── AI服务集成 (external_ai_service.py)
├── 语音识别服务 (voice_recognition_service.py)
├── 数据库模型 (models/airelief.py)
└── API接口 (api/v1/airelief/chat.py)

外部服务层
├── AI对话服务 (airelief.cn:8997)
├── 语音识别API (火山引擎)
└── 微信录音API (微信JS-SDK)
```

### 1.2 数据流向

```
用户录音 → 前端编码 → WebSocket传输 → 语音识别 → AI处理 → 响应返回
```

## 2. 核心文件详细分析

### 2.1 认证模块 (`/app/api/v1/airelief/auth.py`)

#### 主要功能
- 微信网页授权登录
- JWT Token生成和验证
- 微信JS-SDK配置生成
- 用户信息管理

#### 关键接口

**微信授权回调**
```python
@router.get("/wechat-callback")
async def wechat_auth_callback(code: str, state: str = None)
```
- 处理微信授权回调
- 获取用户信息并创建/更新用户记录
- 生成JWT Token
- 重定向到前端页面

**微信JS-SDK配置**
```python
@router.get("/wechat-jsapi-config")
async def get_wechat_jsapi_config(url: str)
```
- 生成微信JS-SDK配置参数
- 支持录音相关API权限
- 返回签名和配置信息

#### 用户数据转换
```python
def convert_user_to_response(user) -> AIReliefUserInfo:
    return AIReliefUserInfo(
        id=user.id,
        user_id=user.user_id,
        wechat_openid=user.wechat_openid,
        nickname=user.nickname,
        # ... 其他字段
    )
```

### 2.2 聊天API模块 (`/app/api/v1/airelief/chat.py`)

#### 主要功能
- WebSocket连接管理
- 聊天会话和消息查询
- 语音识别服务状态查询
- 数据统计和导出

#### 关键接口

**WebSocket连接端点**
```python
@router.websocket("/ws/{user_id}")
async def websocket_chat_endpoint(websocket: WebSocket, user_id: str)
```
- 建立WebSocket连接
- 自动创建用户（如果不存在）
- 委托给WebSocket服务处理

**获取会话消息历史**
```python
@router.get("/sessions/{session_id}/messages")
async def get_session_messages(session_id: str, page: int = 1, page_size: int = 50)
```
- 分页查询会话消息
- 支持语音消息的完整信息返回
- 包含音频URL、识别文本等

**语音识别配置查询**
```python
@router.get("/voice-recognition/config")
async def get_voice_recognition_config(is_wechat_env: bool = False)
```
- 返回当前语音识别引擎状态
- 提供推荐的识别模式
- 支持环境自适应配置

### 2.3 语音识别服务 (`/app/services/voice_recognition_service.py`)

#### 架构设计
- 支持多种识别引擎：火山引擎、微信、禁用
- 支持多种识别模式：服务器端、客户端、混合
- 支持流式和非流式识别

#### 核心类和方法

**VoiceRecognitionService 主服务类**
```python
class VoiceRecognitionService:
    def __init__(self):
        self.engine = getattr(settings, 'VOICE_RECOGNITION_ENGINE', 'volcano')
        self.mode = getattr(settings, 'VOICE_RECOGNITION_MODE', 'hybrid')
        self.fallback_enabled = getattr(settings, 'VOICE_RECOGNITION_FALLBACK', True)
```

**音频识别方法**
```python
async def recognize_audio(self, audio_data: str, audio_format: str = "wav",
                         client_recognition: str = None, is_wechat_env: bool = False)
```
- 支持base64编码的音频数据
- 优先使用客户端识别结果（如果可用）
- 自动降级到服务器端识别

**流式识别会话**
```python
async def create_streaming_session(self, on_result: Callable, on_error: Callable)
```
- 创建火山引擎流式识别会话
- 支持实时音频流处理
- 提供结果回调和错误处理

#### 火山引擎集成

**VolcanoStreamingSession 流式识别类**
- 基于WebSocket的实时语音识别
- 支持火山引擎v2 API协议
- 实现二进制协议和GZIP压缩

**协议实现**
```python
def generate_header(version=PROTOCOL_VERSION, message_type=CLIENT_FULL_REQUEST, ...):
    # 生成火山引擎协议头
    
def parse_response(res):
    # 解析火山引擎响应
```

### 2.4 WebSocket聊天服务 (`/app/services/websocket_chat_service.py`)

#### 连接管理
```python
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        self.user_sessions: Dict[str, str] = {}
        self.streaming_sessions: Dict[str, Any] = {}
```

#### 消息处理流程

**语音消息处理**
```python
async def _handle_audio_message(self, user_id: str, session_id: str, message_data: dict):
    # 1. 提取音频数据和参数
    # 2. 调用语音识别服务
    # 3. 保存语音消息到数据库
    # 4. 发送AI回复
```

**流式语音处理**
```python
async def _handle_streaming_start(self, user_id: str, session_id: str, message_data: dict):
    # 创建流式识别会话
    
async def _handle_streaming_audio(self, user_id: str, session_id: str, message_data: dict):
    # 处理音频流数据
    
async def _handle_streaming_end(self, user_id: str, session_id: str, message_data: dict):
    # 结束流式识别并处理最终结果
```

#### 音频数据处理
- 支持WebM到PCM格式转换
- 音频缓冲区管理
- 完整音频数据保存

## 3. 数据库设计

### 3.1 用户表 (AIReliefUser)
```python
class AIReliefUser(BaseModel, TimestampMixin):
    user_id = fields.CharField(max_length=16, unique=True)  # 短ID
    wechat_openid = fields.CharField(max_length=64, unique=True)
    nickname = fields.CharField(max_length=100)
    avatar = fields.TextField(null=True)
    # 微信相关字段...
    # 会员时长相关字段...
```

### 3.2 聊天会话表 (ChatSession)
```python
class ChatSession(BaseModel, TimestampMixin):
    session_id = fields.CharField(max_length=64, unique=True)
    user = fields.ForeignKeyField("models.AIReliefUser")
    title = fields.CharField(max_length=200, null=True)
    message_count = fields.IntField(default=0)
    status = fields.CharField(max_length=20, default="active")  # active/ended
```

### 3.3 聊天消息表 (ChatMessage)
```python
class ChatMessage(BaseModel, TimestampMixin):
    message_id = fields.CharField(max_length=64, unique=True)
    session = fields.ForeignKeyField("models.ChatSession")
    user = fields.ForeignKeyField("models.AIReliefUser")
    content = fields.TextField()  # 文本内容或识别文本
    message_type = fields.CharField(max_length=10)  # text/audio
    role = fields.CharField(max_length=20)  # user/assistant
    
    # 语音消息字段
    audio_url = fields.TextField(null=True)  # 音频URL (data URL格式)
    audio_duration = fields.IntField(null=True)  # 时长(秒)
    audio_file_size = fields.IntField(null=True)  # 文件大小(字节)
    
    # 语音识别字段
    transcription_text = fields.TextField(null=True)  # 识别文本
    transcription_confidence = fields.FloatField(null=True)  # 置信度
    transcription_status = fields.CharField(max_length=20, default="pending")
    
    # AI相关字段
    external_session_id = fields.CharField(max_length=128, null=True)
    tokens_used = fields.IntField(default=0)
    processing_time = fields.FloatField(null=True)
```

## 4. WebSocket通信协议

### 4.1 连接建立
```
WebSocket URL: /api/v1/airelief/chat/ws/{user_id}
```

### 4.2 消息格式

**文本消息**
```json
{
  "type": "text",
  "content": "用户输入的文本"
}
```

**语音消息**
```json
{
  "type": "audio",
  "data": {
    "audio_data": "base64编码的音频数据",
    "duration": 5,
    "audio_format": "wav",
    "client_recognition": "客户端识别结果(可选)",
    "is_wechat_env": true
  }
}
```

**流式语音消息**
```json
// 开始流式识别
{
  "type": "streaming_start"
}

// 发送音频流
{
  "type": "streaming_audio", 
  "data": {
    "audio_data": "base64编码的音频片段"
  }
}

// 结束流式识别
{
  "type": "streaming_end"
}
```

### 4.3 服务器响应

**连接成功**
```json
{
  "type": "connected",
  "data": {
    "message": "连接成功",
    "session_id": "chat_xxx"
  }
}
```

**AI消息**
```json
{
  "type": "ai_message",
  "data": {
    "message_id": "msg_xxx",
    "content": "AI回复内容",
    "role": "assistant",
    "timestamp": "2024-01-01T00:00:00"
  }
}
```

**语音消息确认**
```json
{
  "type": "voice_message_sent",
  "data": {
    "message_id": "voice_xxx",
    "transcription": "识别的文本",
    "confidence": 0.95,
    "audioDuration": 5,
    "audioUrl": "data:audio/webm;base64,..."
  }
}
```

**流式识别结果**
```json
{
  "type": "streaming_result",
  "data": {
    "type": "partial_result",  // 或 "final_result"
    "text": "识别的文本",
    "is_final": false,
    "confidence": 0.85
  }
}
```

## 5. 语音识别流程

### 5.1 非流式识别流程
```
1. 用户录音完成
2. 前端发送完整音频数据
3. 后端调用语音识别服务
4. 保存语音消息到数据库
5. 调用AI服务获取回复
6. 返回AI回复给前端
```

### 5.2 流式识别流程
```
1. 用户开始录音，发送 streaming_start
2. 创建流式识别会话
3. 实时发送音频片段 streaming_audio
4. 接收部分识别结果并转发给前端
5. 用户结束录音，发送 streaming_end
6. 获取最终识别结果
7. 保存完整语音消息
8. 调用AI服务并返回回复
```

### 5.3 识别引擎配置

**火山引擎配置**
```python
# 环境变量
VOICE_RECOGNITION_ENGINE=volcano
VOLCENGINE_APP_ID=your_app_id
VOLCENGINE_ACCESS_KEY=your_access_key  
VOLCENGINE_SECRET_KEY=your_secret_key
VOLCENGINE_CLUSTER=volcengine_streaming_common

# 识别参数
{
    "language": "zh-CN",
    "format": "wav", 
    "sample_rate": 16000,
    "encoding": "linear16",
    "enable_words": True,
    "enable_punctuation": True
}
```

**微信引擎配置**
```python
VOICE_RECOGNITION_ENGINE=wechat
VOICE_RECOGNITION_MODE=client_only
```

## 6. 语音数据存储机制

### 6.1 存储方式
- **格式**: Data URL格式 (`data:audio/webm;base64,{base64_data}`)
- **位置**: 数据库字段 `audio_url`
- **优势**: 前端可直接播放，无需额外文件服务器

### 6.2 存储字段
```python
# ChatMessage表中的语音相关字段
audio_url: str           # Data URL格式的音频数据
audio_duration: int      # 音频时长(秒)
audio_file_size: int     # 文件大小(字节)
transcription_text: str  # 识别文本
transcription_confidence: float  # 识别置信度
```

### 6.3 流式音频处理
```python
# 音频缓冲区管理
self.streaming_audio_buffers: Dict[str, Dict] = {
    user_id: {
        'chunks': [bytes],      # 音频片段列表
        'start_time': float,    # 开始时间
        'total_duration': float # 总时长
    }
}

# 合并音频数据
def _get_merged_audio(self, user_id: str) -> tuple[bytes, float]:
    buffer = self.streaming_audio_buffers[user_id]
    merged_audio = b"".join(buffer['chunks'])
    duration = time.time() - buffer['start_time']
    return merged_audio, duration
```

## 7. 错误处理机制

### 7.1 WebSocket层错误处理
```python
async def _send_error_message(self, user_id: str, session_id: str, error: str):
    await self.manager.send_personal_message({
        "type": "error",
        "data": {
            "message": error,
            "timestamp": datetime.now().isoformat()
        }
    }, user_id, session_id)
```

### 7.2 语音识别错误处理
- 引擎配置检查
- 音频格式验证
- 识别超时处理
- 降级机制（客户端→服务器端）

### 7.3 连接异常处理
- WebSocket断开自动清理
- 流式会话资源释放
- 音频缓冲区清理
- 会话状态更新

## 8. 配置说明

### 8.1 环境变量配置
```bash
# 语音识别引擎配置
VOICE_RECOGNITION_ENGINE=volcano  # volcano/wechat/disabled
VOICE_RECOGNITION_MODE=hybrid     # server/client_only/hybrid
VOICE_RECOGNITION_FALLBACK=true   # 是否启用降级

# 火山引擎配置
VOLCENGINE_APP_ID=your_app_id
VOLCENGINE_ACCESS_KEY=your_token
VOLCENGINE_SECRET_KEY=your_secret_key
VOLCENGINE_CLUSTER=volcengine_streaming_common

# 微信配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
WECHAT_TOKEN=your_wechat_token

# 外部AI服务配置
EXTERNAL_AI_BASE_URL=https://www.airelief.cn:8997
EXTERNAL_AI_API_KEY=your_ai_api_key
```

### 8.2 前端配置
```typescript
// src/config/env.ts
export const appConfig = {
  apiBaseUrl: 'https://www.airelief.cn:8997',
  wsBaseUrl: 'wss://www.airelief.cn:8997',
  voice: {
    maxDuration: 60,    // 最大录音时长
    minDuration: 1,     // 最小录音时长
    sampleRate: 16000,  // 采样率
    channels: 1         // 声道数
  }
}
```

## 9. 客户端集成指南

### 9.1 认证流程
```typescript
// 1. 获取微信授权URL
const authResponse = await api.get('/api/v1/airelief/auth/wechat-auth-url')
window.location.href = authResponse.data.auth_url

// 2. 授权回调处理（自动跳转）
// URL: /AIrelief/auth/callback?token={jwt_token}&state={state}

// 3. 保存Token并初始化用户信息
localStorage.setItem('token', token)
```

### 9.2 WebSocket连接
```typescript
// 建立WebSocket连接
const ws = new WebSocket(`${wsBaseUrl}/api/v1/airelief/chat/ws/${userId}`)

ws.onopen = () => {
  console.log('WebSocket连接已建立')
}

ws.onmessage = (event) => {
  const message = JSON.parse(event.data)
  handleWebSocketMessage(message)
}
```

### 9.3 语音消息发送
```typescript
// 发送语音消息
const sendVoiceMessage = (audioBlob: Blob, duration: number) => {
  const reader = new FileReader()
  reader.onload = () => {
    const base64Data = reader.result.split(',')[1]
    ws.send(JSON.stringify({
      type: 'audio',
      data: {
        audio_data: base64Data,
        duration: duration,
        audio_format: 'webm',
        is_wechat_env: isWechatEnv()
      }
    }))
  }
  reader.readAsDataURL(audioBlob)
}
```

### 9.4 流式语音识别
```typescript
// 开始流式识别
const startStreaming = () => {
  ws.send(JSON.stringify({ type: 'streaming_start' }))
}

// 发送音频流
const sendAudioChunk = (audioChunk: Blob) => {
  const reader = new FileReader()
  reader.onload = () => {
    const base64Data = reader.result.split(',')[1]
    ws.send(JSON.stringify({
      type: 'streaming_audio',
      data: { audio_data: base64Data }
    }))
  }
  reader.readAsDataURL(audioChunk)
}

// 结束流式识别
const endStreaming = () => {
  ws.send(JSON.stringify({ type: 'streaming_end' }))
}
```

## 10. 性能优化建议

### 10.1 音频处理优化
- 使用合适的音频编码格式
- 控制音频质量和文件大小
- 实现音频压缩

### 10.2 数据库优化
- 为常用查询字段添加索引
- 实现消息分页加载
- 考虑音频数据分离存储

### 10.3 WebSocket优化
- 实现连接池管理
- 添加心跳检测机制
- 优化消息序列化

### 10.4 缓存策略
- 语音识别结果缓存
- 用户会话信息缓存
- AI回复内容缓存
