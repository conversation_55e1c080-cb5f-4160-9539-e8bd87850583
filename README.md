# 项目构建说明

## 构建命令

### 基本构建命令

```bash
# 生产环境构建
npm run build

# 开发环境构建（保留调试信息）
npm run build:dev

# 生产环境构建（带类型检查）
npm run build:with-check

# 开发环境构建（带类型检查）
npm run build:dev-with-check
```

### 开发调试命令

```bash
# 启动开发服务器
npm run dev

# 预览构建结果
npm run preview
```

## 构建配置特性

### 🚀 性能优化

- **代码分割**：自动将第三方库分离
  - `vendor`: Vue 核心库 (vue, vue-router, pinia)
  - `vant`: UI 组件库
- **压缩优化**：使用 esbuild 快速压缩
- **资源优化**：自动处理静态资源

### 🛠️ 开发体验

- **环境区分**：
  - 生产环境：关闭源码映射，最小化体积
  - 开发环境：保留源码映射，便于调试
- **自动导入**：
  - Vant 组件自动导入
  - Composables 和 Stores 自动导入

### 📁 目录处理

- **自动忽略**：文档目录 (`docs/`) 不会被打包
- **别名支持**：`@` 指向 `src` 目录
- **TypeScript 支持**：完整的类型检查

## 构建输出

构建完成后，产物将输出到 `dist/` 目录：

```
dist/
├── index.html          # 入口页面
├── assets/            # 静态资源
│   ├── *.css         # 样式文件
│   ├── *.js          # JavaScript 文件
│   └── *.png|svg     # 图片资源
└── ...
```

## 环境变量

项目支持以下环境变量：

- `VITE_API_BASE_URL`: API 基础地址
- `VITE_ENABLE_MOCK`: 是否启用 Mock 数据
- `VITE_WX_APP_ID`: 微信应用 ID
- `VITE_WX_AUTH_REDIRECT_URL`: 微信授权回调地址

## 部署建议

### 生产部署
1. 运行 `npm run build`
2. 将 `dist/` 目录内容部署到服务器
3. 配置服务器支持 SPA 路由

### 测试部署
1. 运行 `npm run build:dev` 
2. 使用 `npm run preview` 本地预览
3. 或部署到测试环境进行验证

## 故障排除

### 构建失败
- 检查 Node.js 版本 (推荐 16+)
- 清理依赖：`rm -rf node_modules && npm install`
- 检查 TypeScript 错误：`npm run build:with-check`

### 体积过大
- 检查是否有不必要的依赖导入
- 使用构建分析：添加 `--analyze` 标志

---

*最后更新：$(date)*
