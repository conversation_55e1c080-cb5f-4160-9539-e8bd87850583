from fastapi import APIRouter
from .auth import router as auth_router
from .user import router as user_router
from .payment import router as payment_router
from .chat import router as chat_router
from .coupon import router as coupon_router
from .agreement import router as agreement_router
from .upload import router as upload_router

airelief_router = APIRouter()

# 不需要管理员权限的接口（面向C端用户）
airelief_router.include_router(auth_router, prefix="/auth", tags=["AI-Relief认证"])
airelief_router.include_router(user_router, prefix="/user", tags=["AI-Relief用户"])
airelief_router.include_router(payment_router, prefix="/payment", tags=["AI-Relief支付"])
airelief_router.include_router(chat_router, prefix="/chat", tags=["AI-Relief聊天"])
airelief_router.include_router(coupon_router, prefix="", tags=["AI-Relief兑换码"])
airelief_router.include_router(agreement_router, prefix="", tags=["AI-Relief协议管理"])
airelief_router.include_router(upload_router, prefix="", tags=["AI-Relief文件上传"])

__all__ = ["airelief_router"]