"""
协议管理接口
包括管理端和用户端的所有协议相关功能
"""
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from tortoise.exceptions import DoesNotExist

from app.schemas.base import Success, SuccessExtra
from app.schemas.airelief import (
    AgreementInfo, CreateAgreementRequest, UpdateAgreementRequest,
    AgreementListRequest, AgreementListResponse, PublicAgreementResponse
)
from app.models.airelief import Agreement
# from app.core.dependency import DependPermission  # 暂时注释掉

router = APIRouter()


# ===== 工具函数 =====

def agreement_to_dict(agreement: Agreement) -> dict:
    """将协议对象转换为字典"""
    return {
        "id": agreement.id,
        "type": agreement.type,
        "title": agreement.title,
        "content": agreement.content,
        "version": agreement.version,
        "status": agreement.status,
        "effective_date": agreement.effective_date.isoformat() if agreement.effective_date else None,
        "created_by": agreement.created_by_id,
        "created_at": agreement.created_at.isoformat() if agreement.created_at else None,
        "updated_at": agreement.updated_at.isoformat() if agreement.updated_at else None
    }


# ===== 管理端接口 =====

@router.post("/agreement/create", summary="创建协议（管理端）")
async def create_agreement(
    request: CreateAgreementRequest
    # _: str = DependPermission  # 暂时注释掉权限检查
):
    """创建协议"""
    try:
        # 验证协议类型
        valid_types = [Agreement.TYPE_USER_AGREEMENT, Agreement.TYPE_PRIVACY_POLICY, 
                      Agreement.TYPE_PAYMENT_AGREEMENT, Agreement.TYPE_DISCLAIMER]
        if request.type not in valid_types:
            raise HTTPException(status_code=400, detail=f"无效的协议类型，支持的类型：{', '.join(valid_types)}")
        
        # 验证协议状态
        valid_statuses = [Agreement.STATUS_ACTIVE, Agreement.STATUS_DRAFT, Agreement.STATUS_DEPRECATED]
        if request.status not in valid_statuses:
            raise HTTPException(status_code=400, detail=f"无效的协议状态，支持的状态：{', '.join(valid_statuses)}")
        
        # 创建协议
        agreement_data = {
            "type": request.type,
            "title": request.title,
            "content": request.content,
            "version": request.version,
            "status": request.status,
            "effective_date": request.effective_date,
        }
        
        agreement = await Agreement.create(**agreement_data)
        return Success(data=agreement_to_dict(agreement))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建协议失败: {str(e)}")


@router.get("/agreement/list", summary="获取协议列表（管理端）")
async def get_agreements(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    type: Optional[str] = Query(None, description="协议类型筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词")
    # _: str = DependPermission  # 暂时注释掉权限检查用于测试
):
    """获取协议列表（分页、筛选）"""
    try:
        # 构建查询条件
        query = Agreement.all()
        
        if type:
            query = query.filter(type=type)
        
        if status:
            query = query.filter(status=status)
        
        if search:
            query = query.filter(title__icontains=search)
        
        # 获取总数
        total = await query.count()
        
        # 分页查询
        agreements = await query.order_by("-created_at").offset((page-1)*page_size).limit(page_size)
        
        # 转换数据格式
        data = [agreement_to_dict(agreement) for agreement in agreements]
        
        return SuccessExtra(
            data=data,
            total=total,
            page=page,
            page_size=page_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取协议列表失败: {str(e)}")


@router.get("/agreement/get", summary="获取单个协议详情（管理端）")
async def get_agreement(
    agreement_id: int = Query(..., description="协议ID")
    # _: str = DependPermission  # 暂时注释掉权限检查
):
    """获取协议详细信息"""
    try:
        agreement = await Agreement.get_or_none(id=agreement_id)
        if not agreement:
            raise HTTPException(status_code=404, detail="协议不存在")
        
        return Success(data=agreement_to_dict(agreement))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取协议详情失败: {str(e)}")


@router.post("/agreement/update", summary="更新协议（管理端）")
async def update_agreement(
    request: UpdateAgreementRequest
    # _: str = DependPermission  # 暂时注释掉权限检查
):
    """更新协议信息"""
    try:
        agreement = await Agreement.get_or_none(id=request.id)
        if not agreement:
            raise HTTPException(status_code=404, detail="协议不存在")
        
        # 更新字段
        update_data = {}
        if request.type is not None:
            valid_types = [Agreement.TYPE_USER_AGREEMENT, Agreement.TYPE_PRIVACY_POLICY, 
                          Agreement.TYPE_PAYMENT_AGREEMENT, Agreement.TYPE_DISCLAIMER]
            if request.type not in valid_types:
                raise HTTPException(status_code=400, detail=f"无效的协议类型，支持的类型：{', '.join(valid_types)}")
            update_data["type"] = request.type
            
        if request.title is not None:
            update_data["title"] = request.title
            
        if request.content is not None:
            update_data["content"] = request.content
            
        if request.version is not None:
            update_data["version"] = request.version
            
        if request.status is not None:
            valid_statuses = [Agreement.STATUS_ACTIVE, Agreement.STATUS_DRAFT, Agreement.STATUS_DEPRECATED]
            if request.status not in valid_statuses:
                raise HTTPException(status_code=400, detail=f"无效的协议状态，支持的状态：{', '.join(valid_statuses)}")
            update_data["status"] = request.status
            
        if request.effective_date is not None:
            update_data["effective_date"] = request.effective_date
        
        # 执行更新
        if update_data:
            await agreement.update_from_dict(update_data)
            await agreement.save()
        
        return Success(data=agreement_to_dict(agreement))
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新协议失败: {str(e)}")


@router.delete("/agreement/delete", summary="删除协议（管理端）")
async def delete_agreement(
    agreement_id: int = Query(..., description="协议ID")
    # _: str = DependPermission  # 暂时注释掉权限检查
):
    """删除协议"""
    try:
        agreement = await Agreement.get_or_none(id=agreement_id)
        if not agreement:
            raise HTTPException(status_code=404, detail="协议不存在")
        
        await agreement.delete()
        
        return Success(data={"message": "协议删除成功"})
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除协议失败: {str(e)}")


# ===== 公开接口（用户端） =====

@router.get("/agreement/public/{agreement_type}", summary="获取公开协议内容")
async def get_public_agreement(agreement_type: str):
    """
    获取公开协议内容（用户端）
    支持的协议类型：user_agreement, privacy_policy, payment_agreement, disclaimer
    """
    try:
        # 验证协议类型
        valid_types = [Agreement.TYPE_USER_AGREEMENT, Agreement.TYPE_PRIVACY_POLICY, 
                      Agreement.TYPE_PAYMENT_AGREEMENT, Agreement.TYPE_DISCLAIMER]
        if agreement_type not in valid_types:
            raise HTTPException(status_code=400, detail=f"无效的协议类型，支持的类型：{', '.join(valid_types)}")
        
        # 获取生效中的协议
        agreement = await Agreement.filter(
            type=agreement_type, 
            status=Agreement.STATUS_ACTIVE
        ).order_by("-created_at").first()
        
        if not agreement:
            raise HTTPException(status_code=404, detail="协议不存在或未生效")
        
        # 返回公开信息（不包含管理字段）
        public_data = {
            "type": agreement.type,
            "title": agreement.title,
            "content": agreement.content,
            "version": agreement.version,
            "effective_date": agreement.effective_date.isoformat() if agreement.effective_date else None,
            "updated_at": agreement.updated_at.isoformat() if agreement.updated_at else None
        }
        
        return Success(data=public_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取协议内容失败: {str(e)}")


@router.get("/agreement/types", summary="获取协议类型列表")
async def get_agreement_types():
    """获取支持的协议类型列表"""
    types = [
        {"value": Agreement.TYPE_USER_AGREEMENT, "label": "用户协议"},
        {"value": Agreement.TYPE_PRIVACY_POLICY, "label": "隐私政策"},
        {"value": Agreement.TYPE_PAYMENT_AGREEMENT, "label": "充值协议"},
        {"value": Agreement.TYPE_DISCLAIMER, "label": "免责声明"},
    ]
    
    return Success(data=types)


@router.get("/agreement/statuses", summary="获取协议状态列表")
async def get_agreement_statuses():
    """获取支持的协议状态列表"""
    statuses = [
        {"value": Agreement.STATUS_ACTIVE, "label": "生效中"},
        {"value": Agreement.STATUS_DRAFT, "label": "草稿"},
        {"value": Agreement.STATUS_DEPRECATED, "label": "已废弃"},
    ]
    
    return Success(data=statuses)
