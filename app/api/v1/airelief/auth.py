from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
import hashlib
from fastapi import APIRouter, HTTPException, Query, Request, Depends
from fastapi.responses import RedirectResponse, PlainTextResponse
from loguru import logger
import uuid

from app.schemas.base import Success
from app.schemas.airelief import AuthUrlResponse, WeChatLoginRequest, AIReliefUserInfo
from app.models.airelief import AIReliefUser, WeChatAuthLog
from app.utils.jwt_utils import create_access_token
from app.utils.wechat import wechat_auth
from app.settings.config import settings
from app.utils.ip_location import get_ip_location  # 导入IP地理位置查询工具

router = APIRouter()

@router.get("/MP_verify_iQMHfWsivcn1naON.txt", summary="微信域名验证文件")
async def wechat_domain_verify():
    """微信公众平台域名验证文件"""
    return PlainTextResponse(content="iQMHfWsivcn1naON")

def convert_user_to_response(user) -> AIReliefUserInfo:
    """将数据库用户对象转换为API响应格式"""
    return AIReliefUserInfo(
        id=user.id,
        user_id=user.user_id,
        wechat_openid=user.wechat_openid,
        wechat_unionid=user.wechat_unionid,
        nickname=user.nickname,
        avatar=user.avatar,
        phone=user.phone,
        gender=user.gender,
        birthday=user.birthday.isoformat() if user.birthday else None,
        wechat_province=user.wechat_province,
        wechat_city=user.wechat_city,
        wechat_country=user.wechat_country,
        wechat_privilege=user.wechat_privilege or [],
        is_wechat_subscribed=user.is_wechat_subscribed,
        isNewUser=user.is_new_user,
        duration=user.remaining_duration,  # 使用实时计算的剩余时长
        totalDuration=user.total_duration,
        consumedDuration=user.consumed_duration,  # 使用计算属性
        companionDays=user.companion_days,  # 使用计算属性
        expiryDate=user.expiry_date.isoformat() if user.expiry_date else None,
        ipLocation=user.ip_location,
        is_active=user.is_active,
        last_login=user.last_login.isoformat() if user.last_login else None,
        created_at=user.created_at.isoformat(),
        updated_at=user.updated_at.isoformat()
    )

@router.get("/wechat-verify", summary="微信服务器验证")
async def wechat_server_verify(
    signature: str = Query(..., description="微信加密签名"),
    timestamp: str = Query(..., description="时间戳"),
    nonce: str = Query(..., description="随机数"),
    echostr: str = Query(..., description="随机字符串")
):
    """
    微信服务器验证接口

    校验signature签名：将token、timestamp、nonce三个参数字典序排序后拼接进行sha1加密，
    与微信传来的signature对比验证请求来源，验证成功则返回echostr完成接入。
    """
    logger.info(f"收到微信服务器验证请求: signature={signature}, timestamp={timestamp}, nonce={nonce}")

    try:
        # 将token、timestamp、nonce三个参数进行字典序排序
        tmp_arr = [settings.WECHAT_TOKEN, timestamp, nonce]
        tmp_arr.sort()

        # 将三个参数字符串拼接成一个字符串进行sha1加密
        tmp_str = ''.join(tmp_arr)
        encrypted_str = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()

        # 校验签名
        if encrypted_str == signature:
            logger.info("微信服务器验证成功")
            return PlainTextResponse(content=echostr)
        else:
            logger.warning("微信服务器验证失败：签名校验不通过")
            raise HTTPException(status_code=403, detail="签名校验失败")

    except Exception as e:
        logger.error(f"校验微信签名失败: {e}")
        raise HTTPException(status_code=500, detail="签名校验异常")

@router.get("/wx", summary="微信消息处理接口(GET验证)")
@router.post("/wx", summary="微信消息处理接口(POST消息)")
async def wechat_message_handler(
    request: Request,
    signature: str = Query(..., description="微信加密签名"),
    timestamp: str = Query(..., description="时间戳"), 
    nonce: str = Query(..., description="随机数"),
    echostr: Optional[str] = Query(None, description="随机字符串")
):
    """
    微信消息处理接口 
    
    GET请求：微信服务器验证
    POST请求：接收和处理微信消息
    """
    if request.method == "GET":
        # GET请求：服务器验证
        if not echostr:
            raise HTTPException(status_code=400, detail="缺少echostr参数")
            
        logger.info(f"收到微信服务器验证请求(/wx): signature={signature}, timestamp={timestamp}, nonce={nonce}")

        try:
            # 将token、timestamp、nonce三个参数进行字典序排序
            tmp_arr = [settings.WECHAT_TOKEN, timestamp, nonce]
            tmp_arr.sort()

            # 将三个参数字符串拼接成一个字符串进行sha1加密
            tmp_str = ''.join(tmp_arr)
            encrypted_str = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()

            # 校验签名
            if encrypted_str == signature:
                logger.info("微信服务器验证成功(/wx)")
                return PlainTextResponse(content=echostr)
            else:
                logger.warning("微信服务器验证失败：签名校验不通过(/wx)")
                raise HTTPException(status_code=403, detail="签名校验失败")

        except Exception as e:
            logger.error(f"校验微信签名失败(/wx): {e}")
            raise HTTPException(status_code=500, detail="签名校验异常")
    
    elif request.method == "POST":
        # POST请求：处理微信消息
        try:
            # 验证签名
            tmp_arr = [settings.WECHAT_TOKEN, timestamp, nonce]
            tmp_arr.sort()
            tmp_str = ''.join(tmp_arr)
            encrypted_str = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()
            
            if encrypted_str != signature:
                logger.warning("微信消息签名验证失败")
                raise HTTPException(status_code=403, detail="签名校验失败")
            
            # 获取微信发送的XML数据
            xml_data = await request.body()
            logger.info(f"收到微信消息: {xml_data}")
            
            # TODO: 这里可以添加XML解析和消息处理逻辑
            # 参考微信文档进行消息解析和回复
            
            # 暂时返回空字符串（微信要求返回空字符串表示不回复）
            return PlainTextResponse(content="success")
            
        except Exception as e:
            logger.error(f"处理微信消息失败: {e}")
            return PlainTextResponse(content="fail")

@router.get("/get-access-token", summary="获取微信access_token")
async def get_wechat_access_token():
    """
    获取微信公众号access_token并打印到控制台

    用于调试和测试微信API调用
    """
    try:
        # 使用微信公众号的AppID和AppSecret获取access_token
        import httpx

        url = "https://api.weixin.qq.com/cgi-bin/token"
        params = {
            "grant_type": "client_credential",
            "appid": settings.WECHAT_APP_ID,
            "secret": settings.WECHAT_APP_SECRET
        }

        logger.info(f"正在获取access_token，AppID: {settings.WECHAT_APP_ID}")

        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            result = response.json()

        if "access_token" in result:
            logger.info(f"成功获取access_token: {result['access_token'][:20]}...")
        else:
            logger.error(f"获取access_token失败: {result}")

        return Success(data=result)

    except Exception as e:
        error_msg = f"获取access_token异常: {e}"
        logger.error(error_msg)
        raise HTTPException(status_code=500, detail="获取access_token失败")

# 微信授权相关接口
@router.get("/wechat-auth-url", response_model=AuthUrlResponse, summary="获取微信授权URL")
async def get_wechat_auth_url(
    scope: str = Query(settings.WECHAT_AUTH_SCOPE, description="授权作用域"),
    state: Optional[str] = Query(None, description="状态参数")
):
    """获取微信网页授权URL"""
    try:
        # 如果回调页提示scope参数错误，scope可以改为snsapi_base试试（能获取openid，无法获取用户信息）
        auth_url = wechat_auth.get_auth_url(scope=scope, state=state)
        return AuthUrlResponse(auth_url=auth_url, scope=scope, state=state)
    except Exception as e:
        logger.error(f"生成微信授权URL失败: {e}")
        raise HTTPException(status_code=500, detail="生成授权URL失败")


@router.get("/wechat-callback", summary="微信授权回调")
async def wechat_auth_callback(
    request: Request,
    code: str = Query(..., description="授权码"),
    state: Optional[str] = Query(None, description="状态参数")
):
    """
    微信授权回调处理

    - **code**: 微信授权码
    - **state**: 状态参数
    """
    # 记录请求信息（用于日志）
    client_ip = request.client.host
    user_agent = request.headers.get("user-agent", "")
    logger.info(f"微信授权回调: IP={client_ip}, UA={user_agent[:50]}...")

    try:
        print(f"\n{'='*60}")
        print(f"🔄 开始处理微信授权回调")
        print(f"📱 授权码: {code}")
        print(f"🏷️  状态: {state}")
        print(f"🌐 客户端IP: {client_ip}")
        print(f"🔧 User-Agent: {user_agent[:50]}...")
        print(f"{'='*60}")

        logger.info(f"开始处理微信授权回调，code: {code[:10]}...")

        # 通过code获取用户授权access_token
        print("1️⃣ 正在获取用户授权access_token...")
        token_info = await wechat_auth.get_access_token(code)

        if not token_info:
            error_msg = "获取用户授权access_token失败，可能是授权码已过期或无效"
            print(f"❌ {error_msg}")
            logger.error(error_msg)

            # 记录失败日志
            await WeChatAuthLog.create(
                user=None,
                wechat_openid="unknown",
                auth_type="callback",
                auth_scope="unknown",
                ip_address=client_ip,
                user_agent=user_agent,
                success=False,
                error_message=error_msg
            )

            # 跳转到错误页面，提示用户重新授权
            error_message = "授权已过期，请重新授权"
            error_url = f"{settings.FRONTEND_BASE_URL}/AIrelief/auth/error?message={error_message}"
            print(f"🔄 跳转到错误页面: {error_url}")
            return RedirectResponse(url=error_url)

        openid = token_info.get("openid")
        access_token = token_info.get("access_token")

        print(f"✅ 获取access_token成功!")
        print(f"📱 OpenID: {openid}")
        print(f"🔑 Access Token: {access_token[:20] if access_token else 'None'}...")

        if not openid:
            error_msg = "无法获取用户openid"
            print(f"❌ {error_msg}")
            logger.error(f"{error_msg}, token_info: {token_info}")
            raise HTTPException(status_code=400, detail=error_msg)

        logger.info(f"成功获取用户授权信息，openid: {openid}")

        # 获取用户详细信息（仅在snsapi_userinfo授权时可用）
        user_info = None
        auth_scope = token_info.get("scope", settings.WECHAT_AUTH_SCOPE)

        if auth_scope == "snsapi_userinfo":
            # 只有在用户信息授权时才尝试获取详细信息
            user_info = await wechat_auth.get_user_info(access_token, openid)
            if user_info:
                logger.info(f"成功获取用户详细信息: {user_info.get('nickname', '未知')}")
            else:
                logger.warning("获取用户详细信息失败")
        else:
            logger.info(f"当前授权范围为 {auth_scope}，无法获取用户详细信息，将使用默认信息")

        # 获取IP地理位置（注意：request.client是Address对象，有host属性）
        client_ip = request.client.host if hasattr(request, 'client') and request.client else '127.0.0.1'
        ip_location = await get_ip_location(client_ip)
        logger.info(f"微信登录IP地理位置查询结果: {client_ip} -> {ip_location}")

        # 查找或创建用户
        user = await AIReliefUser.get_or_none(wechat_openid=openid)

        if not user:
            # 新用户注册
            logger.info(f"创建新用户，openid: {openid}")

            # 准备用户数据，确保有默认值
            # 为测试号环境生成友好的默认昵称
            default_nickname = f"用户{openid[-6:]}" if len(openid) >= 6 else "微信用户"

            user_data = {
                "wechat_openid": openid,
                "wechat_unionid": user_info.get("unionid") if user_info else None,
                "wechat_access_token": access_token,
                "wechat_refresh_token": token_info.get("refresh_token"),
                "wechat_token_expires_in": token_info.get("expires_in", 7200),
                "wechat_token_expires_at": datetime.now().replace(microsecond=0) +
                    timedelta(seconds=token_info.get("expires_in", 7200)),
                "nickname": user_info.get("nickname") if user_info else default_nickname,
                "avatar": user_info.get("headimgurl") if user_info else None,
                "gender": user_info.get("sex", 0) if user_info else 0,
                "wechat_province": user_info.get("province") if user_info else None,
                "wechat_city": user_info.get("city") if user_info else None,
                "wechat_country": user_info.get("country") if user_info else None,
                "wechat_privilege": user_info.get("privilege", []) if user_info else [],
                "is_new_user": True,
                "last_login": datetime.now(),
                "ip_location": ip_location  # 设置IP地理位置
            }

            user = await AIReliefUser.create(**user_data)
            logger.info(f"新用户创建成功，ID: {user.id}, 昵称: {user.nickname}, 地理位置: {ip_location}")
        else:
            # 检查用户是否处于解绑状态（access_token为空且expires_in为0）
            was_unbound = (not user.wechat_access_token and user.wechat_token_expires_in == 0)
            
            # 更新授权信息
            user.wechat_access_token = access_token
            user.wechat_refresh_token = token_info.get("refresh_token")
            user.wechat_token_expires_in = token_info.get("expires_in", 7200)
            user.wechat_token_expires_at = datetime.now().replace(microsecond=0) + \
                timedelta(seconds=token_info.get("expires_in", 7200))

            # 如果用户之前解绑过微信，重新获取并更新用户信息
            if was_unbound and user_info:
                logger.info(f"用户重新绑定微信，更新用户信息: openid={openid}")
                user.wechat_unionid = user_info.get("unionid")
                user.nickname = user_info.get("nickname") or user.nickname
                user.avatar = user_info.get("headimgurl") or user.avatar
                # 转换微信性别值：微信API 1=男性,2=女性,0=未知 -> 我们的系统 0=未知,1=女性,2=男性
                wechat_gender = user_info.get("sex", 0)
                if wechat_gender == 1:  # 微信男性 -> 我们的男性
                    user.gender = 2
                elif wechat_gender == 2:  # 微信女性 -> 我们的女性
                    user.gender = 1
                else:  # 微信未知 -> 我们的未知
                    user.gender = 0
                user.wechat_province = user_info.get("province")
                user.wechat_city = user_info.get("city")
                user.wechat_country = user_info.get("country")
                user.wechat_privilege = user_info.get("privilege", [])
            elif not was_unbound and user_info:
                # 正常的信息更新（非解绑重新绑定的情况）
                user.wechat_unionid = user_info.get("unionid") or user.wechat_unionid
                user.nickname = user_info.get("nickname") or user.nickname
                user.avatar = user_info.get("headimgurl") or user.avatar
                # 转换微信性别值
                wechat_gender = user_info.get("sex", 0)
                if wechat_gender == 1:  # 微信男性 -> 我们的男性
                    user.gender = 2
                elif wechat_gender == 2:  # 微信女性 -> 我们的女性
                    user.gender = 1
                else:  # 微信未知 -> 我们的未知
                    user.gender = 0
                user.wechat_province = user_info.get("province") or user.wechat_province
                user.wechat_city = user_info.get("city") or user.wechat_city
                user.wechat_country = user_info.get("country") or user.wechat_country
                user.wechat_privilege = user_info.get("privilege", user.wechat_privilege)

            user.last_login = datetime.now()
            user.ip_location = ip_location  # 更新IP地理位置
            await user.save()
            
            status_msg = "重新绑定" if was_unbound else "信息更新"
            logger.info(f"用户{status_msg}成功，ID: {user.id}, 昵称: {user.nickname}, 地理位置: {ip_location}")

        # 记录授权日志
        await WeChatAuthLog.create(
            user=user,
            wechat_openid=openid,
            auth_type="callback",
            auth_scope=token_info.get("scope", "unknown"),
            ip_address=client_ip,
            user_agent=user_agent,
            success=True
        )

        print(f"🎉 用户处理完成，准备跳转到前端")

        # 生成JWT token
        print("6️⃣ 生成JWT token...")
        from app.schemas.login import JWTPayload

        # 创建JWT payload
        jwt_payload = JWTPayload(
            user_id=user.id,
            username=user.nickname or f"用户{user.id}",
            is_superuser=False,
            exp=datetime.now() + timedelta(days=7)  # 7天过期
        )
        token = create_access_token(data=jwt_payload)
        print(f"✅ JWT token生成成功: {token[:50]}...")

        # 跳转到前端页面，携带token
        frontend_url = f"{settings.FRONTEND_BASE_URL}/AIrelief/auth/callback?token={token}&state={state}"
        print(f"🔄 跳转到前端: {frontend_url}")
        return RedirectResponse(url=frontend_url)

    except HTTPException as he:
        # HTTP异常直接抛出
        raise he
    except Exception as e:
        # 记录详细的异常信息
        import traceback
        error_detail = traceback.format_exc()
        logger.error(f"微信授权回调处理失败: {e}")
        logger.error(f"详细错误信息: {error_detail}")

        # 记录异常日志
        await WeChatAuthLog.create(
            user=None,
            wechat_openid="unknown",
            auth_type="callback",
            auth_scope="unknown",
            ip_address=client_ip,
            user_agent=user_agent,
            success=False,
            error_message=f"{str(e)} | {error_detail[:500]}"  # 限制错误信息长度
        )

        # 根据错误类型提供不同的用户友好提示
        if "code been used" in str(e):
            error_message = "授权码已使用，请重新授权"
        elif "invalid code" in str(e):
            error_message = "授权码无效，请重新授权"
        elif "timeout" in str(e).lower():
            error_message = "网络超时，请稍后重试"
        else:
            error_message = "授权失败，请重新尝试"

        # 跳转到错误页面
        error_url = f"{settings.FRONTEND_BASE_URL}/AIrelief/auth/error?message={error_message}"
        return RedirectResponse(url=error_url)

@router.post("/wechat-login", summary="微信登录")
async def wechat_login(request: WeChatLoginRequest):
    """微信授权登录（通过code直接登录）"""
    try:
        # 通过code获取access_token
        token_info = await wechat_auth.get_access_token(request.code)
        if not token_info:
            raise HTTPException(status_code=400, detail="无效的授权码")

        openid = token_info.get("openid")
        access_token = token_info.get("access_token")

        # 获取用户信息
        user_info = await wechat_auth.get_user_info(access_token, openid)

        # 获取IP地理位置（注意：request.client是Address对象，有host属性）
        client_ip = request.client.host if hasattr(request, 'client') and request.client else '127.0.0.1'
        ip_location = await get_ip_location(client_ip)
        logger.info(f"微信登录IP地理位置查询结果: {client_ip} -> {ip_location}")

        # 查找或创建用户
        user = await AIReliefUser.get_or_none(wechat_openid=openid)

        if not user:
            # 新用户注册
            # 转换微信性别值：微信API 1=男性,2=女性,0=未知 -> 我们的系统 0=未知,1=女性,2=男性
            wechat_gender = user_info.get("sex", 0) if user_info else 0
            if wechat_gender == 1:  # 微信男性 -> 我们的男性
                gender = 2
            elif wechat_gender == 2:  # 微信女性 -> 我们的女性
                gender = 1
            else:  # 微信未知 -> 我们的未知
                gender = 0

            user = await AIReliefUser.create(
                wechat_openid=openid,
                wechat_unionid=user_info.get("unionid") if user_info else None,
                wechat_access_token=access_token,
                wechat_refresh_token=token_info.get("refresh_token"),
                wechat_token_expires_in=token_info.get("expires_in", 7200),
                wechat_token_expires_at=datetime.now().replace(microsecond=0) +
                    timedelta(seconds=token_info.get("expires_in", 7200)),
                nickname=user_info.get("nickname", "微信用户") if user_info else "微信用户",
                avatar=user_info.get("headimgurl") if user_info else None,
                gender=gender,
                wechat_province=user_info.get("province") if user_info else None,
                wechat_city=user_info.get("city") if user_info else None,
                wechat_country=user_info.get("country") if user_info else None,
                wechat_privilege=user_info.get("privilege", []) if user_info else [],
                is_new_user=True,
                last_login=datetime.now(),
                ip_location=ip_location  # 设置IP地理位置
            )
            logger.info(f"新用户创建成功，openid: {openid}, 地理位置: {ip_location}")
        else:
            # 检查用户是否处于解绑状态
            was_unbound = (not user.wechat_access_token and user.wechat_token_expires_in == 0)
            
            # 更新授权信息
            user.wechat_access_token = access_token
            user.wechat_refresh_token = token_info.get("refresh_token")
            user.wechat_token_expires_in = token_info.get("expires_in", 7200)
            user.wechat_token_expires_at = datetime.now().replace(microsecond=0) + \
                timedelta(seconds=token_info.get("expires_in", 7200))

            # 如果用户之前解绑过微信，重新获取并更新用户信息
            if was_unbound and user_info:
                logger.info(f"用户重新绑定微信，更新用户信息: openid={openid}")
                user.wechat_unionid = user_info.get("unionid")
                user.nickname = user_info.get("nickname") or user.nickname
                user.avatar = user_info.get("headimgurl") or user.avatar
                wechat_gender = user_info.get("sex", 0)
                if wechat_gender == 1:  
                    user.gender = 2
                elif wechat_gender == 2: 
                    user.gender = 1
                else:
                    user.gender = 0
                user.wechat_province = user_info.get("province")
                user.wechat_city = user_info.get("city")
                user.wechat_country = user_info.get("country")
                user.wechat_privilege = user_info.get("privilege", [])
            elif not was_unbound and user_info:
                # 正常的信息更新
                user.wechat_unionid = user_info.get("unionid") or user.wechat_unionid
                user.nickname = user_info.get("nickname") or user.nickname
                user.avatar = user_info.get("headimgurl") or user.avatar
                wechat_gender = user_info.get("sex", 0)
                if wechat_gender == 1:  
                    user.gender = 2
                elif wechat_gender == 2: 
                    user.gender = 1
                else:
                    user.gender = 0
                user.wechat_province = user_info.get("province") or user.wechat_province
                user.wechat_city = user_info.get("city") or user.wechat_city
                user.wechat_country = user_info.get("country") or user.wechat_country
                user.wechat_privilege = user_info.get("privilege", user.wechat_privilege)

            user.last_login = datetime.now()
            user.ip_location = ip_location  # 更新IP地理位置
            await user.save()
            
            status_msg = "重新绑定" if was_unbound else "信息更新"
            logger.info(f"用户{status_msg}成功，openid: {openid}, 地理位置: {ip_location}")

        # 生成JWT token
        token = create_access_token({"user_id": user.id, "user_type": "airelief"})

        # 返回用户信息（使用统一的用户信息格式）
        user_info = convert_user_to_response(user)

        return Success(data={
            "token": token,
            "userInfo": user_info
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"微信登录失败: {e}")
        raise HTTPException(status_code=500, detail="登录失败")

@router.get("/user/{user_id}", response_model=AIReliefUserInfo, summary="获取用户信息")
async def get_user_info(user_id: int):
    """
    根据用户ID获取用户信息

    - **user_id**: 用户ID
    """
    try:
        user = await AIReliefUser.get_or_none(id=user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        return convert_user_to_response(user)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户信息失败")

@router.get("/agreement-status", summary="获取协议状态")
async def get_agreement_status():
    """获取用户协议状态"""
    return Success(data={
        "serviceAgreement": True,
        "privacyPolicy": True,
        "version": "1.0.0"
    })

@router.post("/logout", summary="退出登录")
async def logout():
    """退出登录"""
    return Success(data={"success": True})

@router.get("/wechat-jsapi-config", summary="获取微信JS-SDK配置")
async def get_wechat_jsapi_config(url: str):
    """
    获取微信JS-SDK配置参数
    用于在前端初始化微信JS-SDK，特别是支付功能
    """
    try:
        import time
        import random
        import string
        import hashlib
        import httpx
        
        # 1. 获取微信access_token
        access_token_url = "https://api.weixin.qq.com/cgi-bin/token"
        access_token_params = {
            "grant_type": "client_credential",
            "appid": settings.WECHAT_APP_ID,
            "secret": settings.WECHAT_APP_SECRET
        }
        
        logger.info(f"正在获取微信access_token，AppId: {settings.WECHAT_APP_ID}")
        
        async with httpx.AsyncClient() as client:
            response = await client.get(access_token_url, params=access_token_params)
            token_result = response.json()
        
        if "access_token" not in token_result:
            logger.error(f"获取access_token失败: {token_result}")
            raise HTTPException(status_code=500, detail="获取微信access_token失败")
            
        access_token = token_result["access_token"]
        logger.info(f"成功获取access_token: {access_token[:10]}...")
        
        # 2. 获取jsapi_ticket
        ticket_url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket"
        ticket_params = {
            "access_token": access_token,
            "type": "jsapi"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(ticket_url, params=ticket_params)
            ticket_result = response.json()
        
        if ticket_result.get("errcode") != 0 or "ticket" not in ticket_result:
            logger.error(f"获取jsapi_ticket失败: {ticket_result}")
            raise HTTPException(status_code=500, detail="获取微信jsapi_ticket失败")
            
        jsapi_ticket = ticket_result["ticket"]
        logger.info(f"成功获取jsapi_ticket: {jsapi_ticket[:10]}...")
        
        # 3. 生成签名
        timestamp = str(int(time.time()))
        noncestr = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
        
        # 处理URL，确保不包含hash部分
        url = url.split('#')[0]
        
        # 按照微信JS-SDK要求拼接签名字符串并进行SHA1加密
        sign_list = [
            f"jsapi_ticket={jsapi_ticket}",
            f"noncestr={noncestr}",
            f"timestamp={timestamp}",
            f"url={url}"
        ]
        sign_string = "&".join(sorted(sign_list))
        signature = hashlib.sha1(sign_string.encode()).hexdigest()
        
        logger.info(f"生成JS-SDK签名: url={url}, signature={signature}")
        
        # 4. 返回配置
        config = {
            "appId": settings.WECHAT_APP_ID,  # 使用公众号AppID
            "timestamp": timestamp,
            "nonceStr": noncestr,
            "signature": signature,
            "jsApiList": [
                "chooseWXPay",
                "requestPayment",
                "checkJsApi",
                # 录音相关API
                "startRecord",
                "stopRecord",
                "onVoiceRecordEnd",
                "playVoice",
                "pauseVoice",
                "stopVoice",
                "onVoicePlayEnd",
                "uploadVoice",
                "downloadVoice",
                "translateVoice"
            ]
        }
        
        logger.info(f"提供JS-SDK配置，AppId: {settings.WECHAT_APP_ID}")
        
        return Success(data=config)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取微信JS-SDK配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取微信JS-SDK配置失败: {str(e)}")


