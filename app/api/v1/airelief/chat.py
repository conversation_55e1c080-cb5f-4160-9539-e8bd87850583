from fastapi import APIRouter, WebSocket, HTTPException, Query
from fastapi.responses import StreamingResponse
from typing import Optional
import logging
import json
import io
from datetime import datetime

from app.schemas.base import Success
from app.models.airelief import AIReliefUser, ChatSession, ChatMessage
from app.services.websocket_chat_service import websocket_chat_service
from app.services.external_ai_service import ExternalAIService
from app.services.voice_recognition_service import VoiceRecognitionService

logger = logging.getLogger(__name__)
router = APIRouter()

# 服务实例
ai_service = ExternalAIService()
voice_service = VoiceRecognitionService()


@router.websocket("/ws/{user_id}")
async def websocket_chat_endpoint(websocket: WebSocket, user_id: str):
    """
    WebSocket聊天端点
    建立实时聊天连接
    """
    logger.error(f"🔌🔌🔌 WebSocket端点被调用，用户ID: {user_id}")

    # 打印请求信息
    # logger.error(f"📋 WebSocket请求信息: headers={dict(websocket.headers)}")

    try:
        # 首先接受WebSocket连接
        await websocket.accept()

        # 验证用户是否存在，如果不存在则创建
        user = await AIReliefUser.filter(user_id=user_id).first()
        if not user:
            logger.info(f"用户不存在，创建新用户: {user_id}")
            # 创建新用户（支持测试环境和微信环境）
            # 为测试用户生成测试用的wechat_openid
            test_openid = f"test_openid_{user_id}"
            user = await AIReliefUser.create(
                user_id=user_id,
                wechat_openid=test_openid,
                nickname=f"测试用户_{user_id[:8]}",
                source="test" if user_id.startswith("test_") else "wechat",
                is_active=True
            )
            logger.info(f"已创建新用户: {user.user_id}, openid: {test_openid}")

        # 处理WebSocket连接
        await websocket_chat_service.handle_websocket(websocket, user_id)

    except Exception as e:
        logger.error(f"WebSocket连接处理异常: {e}")
        try:
            # 发送错误消息给客户端
            await websocket.send_json({
                "type": "error",
                "data": {
                    "message": "连接异常",
                    "error": str(e)
                }
            })
            await websocket.close(code=4000, reason="服务器内部错误")
        except:
            pass


@router.get("/sessions", summary="获取聊天会话列表")
async def get_chat_sessions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    id: Optional[int] = Query(None, description="ID筛选"),
    user_id: Optional[str] = Query(None, description="用户ID筛选"),
    nickname: Optional[str] = Query(None, description="昵称筛选"),
    phone: Optional[str] = Query(None, description="手机号筛选"),
    status: Optional[str] = Query(None, description="会话状态筛选"),
    date_range: Optional[str] = Query(None, description="时间范围筛选，格式：start_time,end_time")
):
    """获取聊天会话列表（管理后台使用）"""
    try:
        # 构建查询条件
        query = ChatSession.all()

        # ID筛选
        if id:
            query = query.filter(id=id)

        # 用户ID筛选
        if user_id:
            user = await AIReliefUser.filter(user_id=user_id).first()
            if user:
                query = query.filter(user=user)

        # 昵称筛选
        if nickname:
            users = await AIReliefUser.filter(nickname__icontains=nickname).all()
            if users:
                query = query.filter(user__in=users)

        # 手机号筛选
        if phone:
            users = await AIReliefUser.filter(phone__icontains=phone).all()
            if users:
                query = query.filter(user__in=users)

        # 状态筛选
        if status:
            query = query.filter(status=status)

        # 时间范围筛选
        if date_range:
            try:
                start_time, end_time = date_range.split(',')
                from datetime import datetime
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                query = query.filter(created_at__gte=start_dt, created_at__lte=end_dt)
            except (ValueError, AttributeError):
                pass  # 忽略无效的时间范围格式

        # 分页查询，按创建时间倒序
        total = await query.count()
        sessions = await query.prefetch_related('user').order_by('-created_at').offset((page - 1) * page_size).limit(page_size)

        # 转换为响应格式
        session_list = []
        for session in sessions:
            session_data = {
                "id": session.id,
                "session_id": session.session_id,
                "user_id": session.user.user_id,
                "nickname": session.user.nickname,
                "phone": getattr(session.user, 'phone', None),
                "title": session.title,
                "message_count": session.message_count,
                # "duration_consumed": session.duration_consumed or 0,  # 暂时注释，业务逻辑未完善
                "status": session.status,
                "ended_at": session.ended_at.isoformat() if session.ended_at else None,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat()
            }

            # 获取消息统计
            text_count = await ChatMessage.filter(session=session, message_type="text").count()
            audio_count = await ChatMessage.filter(session=session, message_type="audio").count()
            session_data["text_message_count"] = text_count
            session_data["audio_message_count"] = audio_count

            # 获取最后一条消息
            last_message = await ChatMessage.filter(session=session).order_by('-created_at').first()
            if last_message:
                # 处理消息内容显示
                if last_message.message_type == "audio":
                    content = last_message.transcription_text or "语音消息"
                else:
                    content = last_message.content

                session_data["last_message"] = content[:50] + "..." if len(content) > 50 else content
                session_data["last_message_type"] = last_message.message_type
                session_data["last_message_time"] = last_message.created_at.isoformat()
            else:
                session_data["last_message"] = None
                session_data["last_message_type"] = None
                session_data["last_message_time"] = None

            session_list.append(session_data)

        return Success(data={
            "sessions": session_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        })

    except Exception as e:
        logger.error(f"获取聊天会话列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取会话列表失败")


@router.get("/sessions/{session_id}/messages", summary="获取会话消息历史")
async def get_session_messages(
    session_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=100, description="每页数量")
):
    """获取指定会话的消息历史"""
    try:
        # 查找会话
        session = await ChatSession.filter(session_id=session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        # 分页查询消息
        total = await ChatMessage.filter(session=session).count()
        messages = await ChatMessage.filter(session=session).order_by('created_at').offset((page - 1) * page_size).limit(page_size)

        # 转换为响应格式
        message_list = []
        for message in messages:
            message_data = {
                "id": message.id,
                "message_id": message.message_id,
                "content": message.content,
                "message_type": message.message_type,
                "role": message.role,
                "audio_url": message.audio_url,
                "audio_duration": message.audio_duration,
                "transcription_text": message.transcription_text,
                "transcription_confidence": message.transcription_confidence,
                "tokens_used": message.tokens_used,
                "processing_time": message.processing_time,
                "created_at": message.created_at.isoformat()
            }
            message_list.append(message_data)

        return Success(data={
            "messages": message_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "session_info": {
                "session_id": session.session_id,
                "title": session.title,
                "status": session.status,
                "message_count": session.message_count
            }
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取会话消息失败: {e}")
        raise HTTPException(status_code=500, detail="获取消息历史失败")


@router.get("/message-history/{user_id}", summary="获取用户消息历史")
async def get_user_message_history(
    user_id: str,
    limit: int = Query(10, ge=1, le=100, description="限制数量")
):
    """获取用户的消息历史（最近的消息）"""
    try:
        # 查找用户
        user = await AIReliefUser.filter(user_id=user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 获取用户的所有会话，按创建时间倒序
        sessions = await ChatSession.filter(user=user).order_by('-created_at').limit(10)

        if not sessions:
            return Success(data={
                "messages": [],
                "total_count": 0
            })

        # 收集所有消息
        all_messages = []
        for session in sessions:
            # 获取每个会话的消息
            messages = await ChatMessage.filter(session=session).order_by('-created_at').all()

            for message in messages:
                message_data = {
                    "id": message.id,
                    "message_id": message.message_id,
                    "content": message.content,
                    "message_type": message.message_type,
                    "role": message.role,
                    "audio_url": message.audio_url,
                    "audio_duration": message.audio_duration,
                    "transcription": message.transcription_text,
                    "transcription_confidence": message.transcription_confidence,
                    "tokens_used": message.tokens_used,
                    "processing_time": message.processing_time,
                    "created_at": message.created_at.isoformat(),
                    "timestamp": message.created_at.isoformat(),
                    "session_id": session.session_id,
                    "session_ended": session.status == "ended"
                }
                all_messages.append(message_data)

        # 按时间排序，取最新的limit条
        all_messages.sort(key=lambda x: x['created_at'], reverse=True)
        recent_messages = all_messages[:limit]

        return Success(data={
            "messages": recent_messages,
            "total_count": len(recent_messages)
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户消息历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取消息历史失败")


@router.get("/session-history/{user_id}", summary="获取用户会话历史")
async def get_user_session_history(
    user_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=50, description="每页数量")
):
    """获取用户的会话历史"""
    try:
        # 查找用户
        user = await AIReliefUser.filter(user_id=user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 分页查询用户的会话
        total = await ChatSession.filter(user=user).count()
        sessions = await ChatSession.filter(user=user).order_by('-created_at').offset((page - 1) * page_size).limit(page_size)

        # 转换为响应格式
        session_list = []
        for session in sessions:
            # 获取最后一条消息
            last_message = await ChatMessage.filter(session=session).order_by('-created_at').first()

            session_data = {
                "id": session.id,
                "session_id": session.session_id,
                "title": session.title,
                "message_count": session.message_count,
                "status": session.status,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "ended_at": session.ended_at.isoformat() if session.ended_at else None,
                "last_message": {
                    "content": last_message.content if last_message else None,
                    "message_type": last_message.message_type if last_message else None,
                    "created_at": last_message.created_at.isoformat() if last_message else None
                } if last_message else None
            }
            session_list.append(session_data)

        return Success(data={
            "sessions": session_list,
            "total": total,
            "page": page,
            "page_size": page_size
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户会话历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取会话历史失败")


@router.get("/test-websocket-route", summary="测试WebSocket路由是否可用")
async def test_websocket_route():
    """测试WebSocket路由是否正确注册"""
    try:
        return Success(data={
            "websocket_endpoint": "/api/v1/airelief/chat/ws/{user_id}",
            "message": "WebSocket路由已正确注册",
            "timestamp": datetime.now().isoformat(),
            "server_info": {
                "protocol_support": ["ws", "wss"],
                "connection_requirements": [
                    "用户ID必须存在或将自动创建",
                    "支持微信环境连接",
                    "需要正确的WebSocket握手"
                ]
            }
        })
    except Exception as e:
        logger.error(f"测试WebSocket路由失败: {e}")
        return Success(data={
            "success": False,
            "message": f"路由测试失败: {str(e)}"
        })


@router.get("/test-ai-connection", summary="测试外部AI服务连接")
async def test_ai_connection():
    """测试外部AI服务连接状态"""
    try:
        result = await ai_service.test_connection()
        return Success(data=result)
    except Exception as e:
        logger.error(f"测试AI连接失败: {e}")
        return Success(data={
            "success": False,
            "message": f"连接测试失败: {str(e)}"
        })


@router.get("/voice-service-status", summary="获取语音识别服务状态")
async def get_voice_service_status():
    """获取语音识别服务状态"""
    try:
        status = voice_service.get_service_status()
        return Success(data=status)
    except Exception as e:
        logger.error(f"获取语音服务状态失败: {e}")
        return Success(data={
            "configured": False,
            "error": str(e)
        })


@router.get("/voice-recognition/config", summary="获取语音识别配置")
async def get_voice_recognition_config(is_wechat_env: bool = Query(False, description="是否为微信环境")):
    """获取语音识别配置和推荐模式"""
    try:
        # 获取服务状态
        service_status = voice_service.get_service_status()

        # 获取推荐模式
        recommended_mode = voice_service.get_recommended_mode(is_wechat_env)

        return Success(data={
            "service_status": service_status,
            "recommended_mode": recommended_mode,
            "is_wechat_env": is_wechat_env
        })
    except Exception as e:
        logger.error(f"获取语音识别配置失败: {e}")
        return Success(data={
            "service_status": {
                "engine": "error",
                "status": "error",
                "message": str(e)
            },
            "recommended_mode": {
                "mode": "error",
                "reason": str(e)
            }
        })




@router.post("/fix/sync-message-count", summary="修复会话消息计数")
async def fix_sync_message_count():
    """同步会话表的message_count字段与实际消息数量"""
    try:
        sessions = await ChatSession.all()
        fixed_sessions = []

        for session in sessions:
            # 统计实际消息数量
            actual_count = await ChatMessage.filter(session=session).count()
            stored_count = session.message_count

            # 如果不一致，则更新
            if actual_count != stored_count:
                session.message_count = actual_count
                await session.save()

                fixed_sessions.append({
                    "session_id": session.session_id,
                    "old_count": stored_count,
                    "new_count": actual_count,
                    "difference": actual_count - stored_count
                })

        return Success(data={
            "total_sessions": len(sessions),
            "fixed_sessions_count": len(fixed_sessions),
            "fixed_sessions": fixed_sessions,
            "message": f"已修复 {len(fixed_sessions)} 个会话的消息计数"
        })

    except Exception as e:
        logger.error(f"修复消息计数失败: {e}")
        raise HTTPException(status_code=500, detail="修复失败")


@router.get("/debug/message-count", summary="调试消息数量统计")
async def debug_message_count():
    """调试消息数量统计，帮助分析数据不一致问题"""
    try:
        # 方法1：直接统计所有消息
        total_messages_direct = await ChatMessage.all().count()

        # 方法2：按会话统计消息总数
        sessions = await ChatSession.all()
        total_messages_by_session = 0
        session_details = []

        for session in sessions:
            session_message_count = await ChatMessage.filter(session=session).count()
            total_messages_by_session += session_message_count

            session_details.append({
                "session_id": session.session_id,
                "stored_message_count": session.message_count,  # 会话表中存储的数量
                "actual_message_count": session_message_count,   # 实际消息表中的数量
                "status": session.status
            })

        # 方法3：统计会话表中的message_count字段总和
        total_stored_count = sum(session.message_count for session in sessions)

        return Success(data={
            "total_messages_direct": total_messages_direct,
            "total_messages_by_session": total_messages_by_session,
            "total_stored_count": total_stored_count,
            "session_count": len(sessions),
            "session_details": session_details,
            "analysis": {
                "direct_vs_session": total_messages_direct - total_messages_by_session,
                "stored_vs_actual": total_stored_count - total_messages_by_session
            }
        })

    except Exception as e:
        logger.error(f"调试消息统计失败: {e}")
        raise HTTPException(status_code=500, detail="调试失败")


@router.get("/statistics", summary="获取聊天数据统计")
async def get_chat_statistics():
    """获取聊天数据统计信息"""
    try:
        # 统计总会话数
        total_sessions = await ChatSession.all().count()

        # 统计活跃会话数
        active_sessions = await ChatSession.filter(status="active").count()

        # 统计总消息数（只统计有效会话的消息）
        valid_sessions = await ChatSession.all()
        total_messages = 0
        audio_messages = 0

        for session in valid_sessions:
            session_message_count = await ChatMessage.filter(session=session).count()
            session_audio_count = await ChatMessage.filter(session=session, message_type="audio").count()
            total_messages += session_message_count
            audio_messages += session_audio_count

        # 计算平均会话时长 - 暂时注释，业务逻辑未完善
        # sessions_with_duration = await ChatSession.filter(duration_consumed__gt=0).all()
        # avg_session_duration = 0
        # if sessions_with_duration:
        #     total_duration = sum(session.duration_consumed for session in sessions_with_duration)
        #     avg_session_duration = total_duration / len(sessions_with_duration)
        avg_session_duration = 0  # 临时设为0

        # 计算平均每会话消息数
        avg_messages_per_session = 0
        if total_sessions > 0:
            avg_messages_per_session = total_messages / total_sessions

        return Success(data={
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_messages": total_messages,
            "audio_messages": audio_messages,
            "text_messages": total_messages - audio_messages,
            "avg_session_duration": avg_session_duration,
            "avg_messages_per_session": avg_messages_per_session,
            "audio_message_ratio": audio_messages / total_messages if total_messages > 0 else 0,
            "active_session_ratio": active_sessions / total_sessions if total_sessions > 0 else 0
        })

    except Exception as e:
        logger.error(f"获取聊天统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计数据失败")


@router.get("/sessions/{session_id}/export", summary="导出聊天记录")
async def export_chat_history(session_id: str):
    """导出指定会话的聊天记录为JSON文件"""
    try:
        # 查找会话
        session = await ChatSession.filter(session_id=session_id).prefetch_related('user').first()
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        # 获取所有消息
        messages = await ChatMessage.filter(session=session).order_by('created_at').all()

        # 构建导出数据
        export_data = {
            "session_info": {
                "session_id": session.session_id,
                "user_id": session.user.user_id,
                "nickname": session.user.nickname,
                "title": session.title,
                "status": session.status,
                "message_count": session.message_count,
                # "duration_consumed": session.duration_consumed,  # 暂时注释，业务逻辑未完善
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "ended_at": session.ended_at.isoformat() if session.ended_at else None
            },
            "messages": []
        }

        for message in messages:
            message_data = {
                "message_id": message.message_id,
                "content": message.content,
                "message_type": message.message_type,
                "role": message.role,
                "created_at": message.created_at.isoformat()
            }

            # 添加语音消息特有字段
            if message.message_type == "audio":
                message_data.update({
                    "audio_url": message.audio_url,
                    "audio_duration": message.audio_duration,
                    "transcription_text": message.transcription_text,
                    "transcription_confidence": message.transcription_confidence
                })

            # 添加AI消息特有字段
            if message.role == "assistant":
                message_data.update({
                    "tokens_used": message.tokens_used,
                    "processing_time": message.processing_time,
                    "external_session_id": message.external_session_id,
                    "external_message_id": message.external_message_id
                })

            export_data["messages"].append(message_data)

        # 创建JSON文件流
        json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
        json_bytes = json_str.encode('utf-8')

        # 返回文件下载响应
        return StreamingResponse(
            io.BytesIO(json_bytes),
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename=chat_{session_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出聊天记录失败: {e}")
        raise HTTPException(status_code=500, detail="导出失败")


@router.get("/voice-recognition/status", summary="获取语音识别服务状态")
async def get_voice_recognition_status():
    """获取语音识别服务状态"""
    try:
        status = voice_service.get_service_status()
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"获取语音识别状态失败: {e}")
        return {
            "success": False,
            "error": f"获取状态失败: {str(e)}"
        }


@router.put("/sessions/{session_id}/end", summary="结束会话")
async def end_chat_session(session_id: str):
    """手动结束指定的聊天会话"""
    try:
        # 查找会话
        session = await ChatSession.filter(session_id=session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        if session.status != "active":
            raise HTTPException(status_code=400, detail="会话已经结束")

        # 更新会话状态
        session.status = "ended"
        session.ended_at = datetime.now()
        await session.save()

        # 如果有WebSocket连接，通知客户端会话结束
        # 这里可以通过websocket_chat_service来处理
        try:
            await websocket_chat_service.end_session(session_id)
        except Exception as ws_error:
            logger.warning(f"通知WebSocket会话结束失败: {ws_error}")

        return Success(data={
            "session_id": session_id,
            "status": "ended",
            "ended_at": session.ended_at.isoformat()
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"结束会话失败: {e}")
        raise HTTPException(status_code=500, detail="结束会话失败")


@router.delete("/sessions/{session_id}", summary="删除会话")
async def delete_chat_session(session_id: str):
    """删除指定的聊天会话及其所有消息"""
    try:
        # 查找会话
        session = await ChatSession.filter(session_id=session_id).first()
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        # 如果会话还在进行中，先结束它
        if session.status == "active":
            try:
                await websocket_chat_service.end_session(session_id)
            except Exception as ws_error:
                logger.warning(f"结束活跃会话失败: {ws_error}")

        # 删除所有相关消息
        await ChatMessage.filter(session=session).delete()

        # 删除会话
        await session.delete()

        return Success(data={
            "session_id": session_id,
            "deleted": True
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除会话失败: {e}")
        raise HTTPException(status_code=500, detail="删除会话失败")
