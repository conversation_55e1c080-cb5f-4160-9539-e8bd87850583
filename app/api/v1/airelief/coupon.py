"""
兑换码管理接口
包括管理端和用户端的所有兑换码相关功能
"""
from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from tortoise.exceptions import DoesNotExist

from app.schemas.base import Success, SuccessExtra
from app.schemas.airelief import (
    CreateCouponRequest, BatchCreateCouponRequest,
    UpdateCouponRequest, ValidateCouponRequest, UseCouponRequest
)
from app.models.airelief import Coupon, CouponUsage, AIReliefUser
from app.utils.coupon_generator import generate_coupon_code, generate_batch_coupon_codes

router = APIRouter()


# ===== 工具函数 =====

def coupon_to_dict(coupon: Coupon) -> dict:
    """将兑换码模型转换为字典"""
    return {
        "id": coupon.id,
        "code": coupon.code,
        "type": coupon.type,
        "value": float(coupon.value),
        "max_uses": coupon.max_uses,
        "used_count": coupon.used_count,
        "valid_from": coupon.valid_from.isoformat() if coupon.valid_from else None,
        "valid_until": coupon.valid_until.isoformat() if coupon.valid_until else None,
        "is_active": coupon.is_active,
        "created_by": coupon.created_by_id,
        "created_at": coupon.created_at.isoformat() if coupon.created_at else None,
        "updated_at": coupon.updated_at.isoformat() if coupon.updated_at else None,
    }


async def validate_coupon_code(code: str) -> tuple[bool, str, Optional[Coupon]]:
    """
    验证兑换码有效性
    
    Returns:
        (是否有效, 消息, 兑换码对象)
    """
    try:
        coupon = await Coupon.get(code=code)
    except DoesNotExist:
        return False, "兑换码不存在", None
    
    # 检查是否激活
    if not coupon.is_active:
        return False, "兑换码已被禁用", coupon
    
    # 检查有效期
    now = datetime.now()
    
    # 统一处理时区，将所有datetime转换为naive datetime进行比较
    valid_from = coupon.valid_from.replace(tzinfo=None) if coupon.valid_from.tzinfo else coupon.valid_from
    valid_until = coupon.valid_until.replace(tzinfo=None) if coupon.valid_until.tzinfo else coupon.valid_until
    
    if now < valid_from:
        return False, "兑换码尚未生效", coupon
    
    if now > valid_until:
        return False, "兑换码已过期", coupon
    
    # 检查使用次数
    if coupon.used_count >= coupon.max_uses:
        return False, "兑换码已达到最大使用次数", coupon
    
    return True, "兑换码有效", coupon


# ===== 管理端接口 =====

@router.post("/coupon/create", summary="创建兑换码（管理端）")
async def create_coupon(request: CreateCouponRequest):
    """创建单个兑换码"""
    try:
        # 生成或验证兑换码
        if request.code:
            # 使用自定义兑换码
            if await Coupon.filter(code=request.code).exists():
                raise HTTPException(status_code=400, detail="兑换码已存在")
            code = request.code
        else:
            # 自动生成兑换码
            try:
                code = await generate_coupon_code(
                    length=request.code_length,
                    prefix=request.code_prefix,
                    use_separator=request.use_separator
                )
            except ValueError as e:
                raise HTTPException(status_code=400, detail=f"生成兑换码失败: {str(e)}")
        
        # 验证日期格式
        try:
            valid_from = datetime.fromisoformat(str(request.valid_from).replace('Z', '+00:00'))
            valid_until = datetime.fromisoformat(str(request.valid_until).replace('Z', '+00:00'))
        except (ValueError, TypeError) as e:
            raise HTTPException(status_code=400, detail=f"日期格式错误: {str(e)}")
        
        # 验证日期逻辑
        if valid_until <= valid_from:
            raise HTTPException(status_code=400, detail="有效期结束时间必须晚于开始时间")
        
        # 验证结束时间不能是过去时间（可选，根据业务需求）
        # now = datetime.now()
        # if valid_until <= now:
        #     raise HTTPException(status_code=400, detail="有效期结束时间不能是过去时间")
        
        # 创建兑换码
        try:
            coupon_data = {
                "code": code,
                "type": request.type,
                "value": request.value,
                "max_uses": request.max_uses,
                "valid_from": valid_from,
                "valid_until": valid_until,
            }
            
            coupon = await Coupon.create(**coupon_data)
            return Success(data=coupon_to_dict(coupon))
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"创建兑换码数据失败: {str(e)}")
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"创建兑换码时发生错误: {str(e)}")  # 添加日志
        raise HTTPException(status_code=500, detail=f"创建兑换码失败: {str(e)}")


@router.post("/coupon/batch", summary="批量创建兑换码（管理端）")
async def batch_create_coupons(request: BatchCreateCouponRequest):
    """批量创建兑换码"""
    try:
        # 验证日期逻辑
        if request.valid_until <= request.valid_from:
            raise HTTPException(status_code=400, detail="有效期结束时间必须晚于开始时间")
        
        # 生成兑换码列表
        codes = await generate_batch_coupon_codes(
            count=request.count,
            length=request.code_length,
            prefix=request.code_prefix,
            use_separator=request.use_separator
        )
        
        # 批量创建兑换码
        coupon_data_list = []
        for code in codes:
            coupon_data_list.append({
                "code": code,
                "type": request.type,
                "value": request.value,
                "max_uses": request.max_uses,
                "valid_from": request.valid_from,
                "valid_until": request.valid_until,
            })
        
        # 使用批量创建
        coupons = []
        for data in coupon_data_list:
            coupon = await Coupon.create(**data)
            coupons.append(coupon_to_dict(coupon))
        
        return Success(data={
            "count": len(coupons),
            "coupons": coupons
        })
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量创建兑换码失败: {str(e)}")


@router.get("/coupon/list", summary="获取兑换码列表（管理端）")
async def get_coupons(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    type: Optional[str] = Query(None, description="类型筛选"),
    is_active: Optional[bool] = Query(None, description="状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    created_by: Optional[int] = Query(None, description="创建者筛选")
):
    """获取兑换码列表（分页、筛选）"""
    try:
        # 构建查询条件
        query = Coupon.all()
        
        if type:
            query = query.filter(type=type)
        
        if is_active is not None:
            query = query.filter(is_active=is_active)
        
        if search:
            query = query.filter(code__icontains=search)
            
        if created_by:
            query = query.filter(created_by_id=created_by)
        
        try:
            # 获取总数
            total = await query.count()
            
            # 分页查询
            offset = (page - 1) * page_size
            coupons = await query.order_by("-created_at").offset(offset).limit(page_size)
            
            # 转换数据
            items = []
            for coupon in coupons:
                try:
                    items.append(coupon_to_dict(coupon))
                except Exception as e:
                    print(f"转换兑换码数据失败: {str(e)}")  # 添加日志
                    continue
            
            return SuccessExtra(
                msg="获取兑换码列表成功",
                data=items,
                total=total,
                page=page,
                page_size=page_size
            )
            
        except Exception as e:
            print(f"查询兑换码列表失败: {str(e)}")  # 添加日志
            raise HTTPException(status_code=500, detail=f"查询兑换码列表失败: {str(e)}")
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取兑换码列表时发生错误: {str(e)}")  # 添加日志
        raise HTTPException(status_code=500, detail=f"获取兑换码列表失败: {str(e)}")


@router.get("/coupon/get/{coupon_id}", summary="获取兑换码详情（管理端）")
async def get_coupon(coupon_id: int):
    """获取兑换码详情"""
    try:
        coupon = await Coupon.get(id=coupon_id)
        return Success(data=coupon_to_dict(coupon))
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="兑换码不存在")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取兑换码详情失败: {str(e)}")


@router.post("/coupon/update", summary="更新兑换码（管理端）")
async def update_coupon(request: UpdateCouponRequest):
    """更新兑换码信息"""
    try:
        coupon = await Coupon.get(id=request.id)
        
        # 验证日期逻辑（如果同时更新了开始和结束时间）
        valid_from = request.valid_from if request.valid_from is not None else coupon.valid_from
        valid_until = request.valid_until if request.valid_until is not None else coupon.valid_until
        
        if valid_until <= valid_from:
            raise HTTPException(status_code=400, detail="有效期结束时间必须晚于开始时间")
        
        # 更新字段
        update_data = request.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(coupon, field):
                setattr(coupon, field, value)
        
        await coupon.save()
        return Success(data=coupon_to_dict(coupon))
        
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="兑换码不存在")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新兑换码失败: {str(e)}")


@router.delete("/coupon/delete", summary="删除兑换码（管理端）")
async def delete_coupon(coupon_id: int):
    """删除兑换码"""
    try:
        coupon = await Coupon.get(id=coupon_id)
        await coupon.delete()
        return Success(data={"id": coupon_id, "message": "删除成功"})
    except DoesNotExist:
        raise HTTPException(status_code=404, detail="兑换码不存在")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除兑换码失败: {str(e)}")


@router.get("/coupon/usage-records", summary="获取兑换码使用记录（管理端）")
async def get_coupon_usage_records(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    coupon_id: Optional[int] = Query(None, description="兑换码ID筛选"),
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间")
):
    """获取兑换码使用记录列表"""
    try:
        # 构建查询条件
        query = CouponUsage.all().select_related('coupon', 'user')
        
        if coupon_id:
            query = query.filter(coupon_id=coupon_id)
            
        if user_id:
            query = query.filter(user_id=user_id)
            
        if start_date:
            query = query.filter(used_at__gte=start_date)
            
        if end_date:
            query = query.filter(used_at__lte=end_date)
        
        try:
            # 获取总数
            total = await query.count()
            
            # 分页查询
            offset = (page - 1) * page_size
            records = await query.order_by("-used_at").offset(offset).limit(page_size)
            
            # 转换数据
            items = []
            for record in records:
                try:
                    items.append({
                        "id": record.id,
                        "coupon_code": record.coupon.code,
                        "user_id": record.user.user_id,
                        "user_nickname": record.user.nickname,
                        "order_id": record.order.order_id if record.order else None,
                        "used_at": record.used_at.isoformat() if record.used_at else None
                    })
                except Exception as e:
                    print(f"转换使用记录数据失败: {str(e)}")  # 添加日志
                    continue
            
            return SuccessExtra(
                msg="获取使用记录成功",
                data=items,
                total=total,
                page=page,
                page_size=page_size
            )
            
        except Exception as e:
            print(f"查询使用记录失败: {str(e)}")  # 添加日志
            raise HTTPException(status_code=500, detail=f"查询使用记录失败: {str(e)}")
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"获取使用记录失败: {str(e)}")  # 添加日志
        raise HTTPException(status_code=500, detail=f"获取使用记录失败: {str(e)}")


# ===== 用户端接口 =====

@router.post("/coupon/validate", summary="验证兑换码（用户端）")
async def validate_coupon(request: ValidateCouponRequest):
    """验证兑换码有效性"""
    try:
        is_valid, message, coupon = await validate_coupon_code(request.code)

        result = {
            "valid": is_valid,
            "message": message,
            "discount": None,
            "expiry": None,
            "coupon_info": None
        }

        if coupon:
            if coupon.type == "discount":
                result["discount"] = float(coupon.value)

            result["expiry"] = coupon.valid_until.isoformat()

            if is_valid:
                result["coupon_info"] = coupon_to_dict(coupon)

        return Success(data=result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"验证兑换码失败: {str(e)}")


@router.post("/coupon/validate-for-payment", summary="验证优惠券用于支付（用户端）")
async def validate_coupon_for_payment(
    code: str,
    user_id: str,
    package_id: int
):
    """验证优惠券是否可用于指定套餐的支付"""
    try:
        # 获取用户和套餐信息
        try:
            user = await AIReliefUser.get(user_id=user_id)
            from app.models.airelief import PaymentPackage
            package = await PaymentPackage.get(id=package_id, is_active=True)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="用户或套餐不存在")

        # 验证优惠券
        is_valid, message, coupon = await validate_coupon_code(code)

        if not is_valid:
            return Success(data={
                "valid": False,
                "message": message,
                "discount_amount": 0,
                "final_amount": float(package.price),
                "coupon_info": None
            })

        # 检查用户是否已经使用过这个优惠券
        existing_usage = await CouponUsage.filter(
            coupon=coupon,
            user=user
        ).exists()

        if existing_usage:
            return Success(data={
                "valid": False,
                "message": "您已经使用过这个优惠券",
                "discount_amount": 0,
                "final_amount": float(package.price),
                "coupon_info": None
            })

        # 检查优惠券类型
        if coupon.type != "discount":
            return Success(data={
                "valid": False,
                "message": f"{coupon.type}类型的优惠券不能用于支付",
                "discount_amount": 0,
                "final_amount": float(package.price),
                "coupon_info": None
            })

        # 计算折扣
        discount_amount = min(float(coupon.value), float(package.price))
        final_amount = max(0.01, float(package.price) - discount_amount)  # 最低0.01元

        return Success(data={
            "valid": True,
            "message": "优惠券可用",
            "discount_amount": discount_amount,
            "final_amount": final_amount,
            "coupon_info": coupon_to_dict(coupon)
        })

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"验证优惠券失败: {str(e)}")


@router.post("/coupon/redeem", summary="使用兑换码（用户端）")
async def redeem_coupon(request: UseCouponRequest):
    """使用兑换码"""
    try:
        # 验证兑换码
        is_valid, message, coupon = await validate_coupon_code(request.code)
        
        if not is_valid:
            return Success(data={
                "success": False,
                "message": message,
                "coupon_info": None
            })
        
        # 验证用户
        try:
            user = await AIReliefUser.get(id=request.user_id)
        except DoesNotExist:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 检查用户是否已经使用过这个兑换码
        existing_usage = await CouponUsage.filter(
            coupon=coupon, 
            user=user
        ).exists()
        
        if existing_usage:
            return Success(data={
                "success": False,
                "message": "您已经使用过这个兑换码",
                "coupon_info": None
            })
        
        # 创建使用记录
        await CouponUsage.create(
            coupon=coupon,
            user=user
        )
        
        # 更新兑换码使用次数
        coupon.used_count += 1
        await coupon.save()
        
        # 根据兑换码类型处理用户账户
        if coupon.type == "discount":
            # 折扣类型的兑换码处理（可能需要在订单中使用）
            success_message = f"兑换成功！获得{coupon.value}元优惠"
        elif coupon.type == "duration":
            # 时长类型，直接增加用户时长
            duration_seconds = int(coupon.value)
            user.total_duration += duration_seconds

            # 更新会员到期时间（实时剩余时长基于此计算）
            from datetime import timedelta
            now = datetime.now()
            # 统一处理时区，将expiry_date转换为naive datetime进行比较
            if user.expiry_date:
                expiry_date = user.expiry_date.replace(tzinfo=None) if user.expiry_date.tzinfo else user.expiry_date
                if expiry_date > now:
                    # 如果当前会员未过期，在现有到期时间基础上延长
                    user.expiry_date = expiry_date + timedelta(seconds=duration_seconds)
                else:
                    # 如果当前会员已过期，从现在开始计算
                    user.expiry_date = now + timedelta(seconds=duration_seconds)
            else:
                # 如果没有到期时间，从现在开始计算
                user.expiry_date = now + timedelta(seconds=duration_seconds)

            await user.save()
            success_message = f"兑换成功！获得{duration_seconds}秒时长"
        else:
            success_message = "兑换成功！"
        
        return Success(data={
            "success": True,
            "message": success_message,
            "coupon_info": coupon_to_dict(coupon)
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"使用兑换码失败: {str(e)}")


 