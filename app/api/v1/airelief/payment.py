from fastapi import APIRouter, HTTPException, Request
from app.schemas.base import Success
from app.schemas.airelief import PaymentPackageInfo
from app.models.airelief import PaymentPackage, Order, AIReliefUser
from typing import Optional
from app.core.wechat_pay_service import WeChatPayService
from tortoise.expressions import Q
from datetime import datetime, timedelta

router = APIRouter()

# 初始化微信支付服务
from app.settings.config import settings
if settings.WECHAT_PAY_TEST_MODE:
    from app.core.mock_wechat_pay_service import MockWeChatPayService
    wechat_pay_service = MockWeChatPayService()
    print("🧪 使用Mock微信支付服务")
else:
    wechat_pay_service = WeChatPayService()

@router.get("/packages", summary="获取所有激活的支付套餐（前台）")
async def get_active_packages():
    """获取所有激活的支付套餐，按sort_order排序"""
    packages = await PaymentPackage.filter(is_active=True).order_by("sort_order")
    data = []
    for pkg in packages:
        package_data = {
            "id": pkg.id,
            "name": pkg.name,
            "duration": pkg.duration,
            "duration_seconds": pkg.duration_seconds,
            "price": float(pkg.price),
            "original_price": float(pkg.original_price) if pkg.original_price else None,
            "tag": pkg.tag,
            "is_active": pkg.is_active,
            "sort_order": pkg.sort_order,
            "created_at": pkg.created_at.isoformat() if pkg.created_at else None,
            "updated_at": pkg.updated_at.isoformat() if pkg.updated_at else None,
        }
        data.append(package_data)
    return Success(data=data)

# 后台管理接口
@router.get("/packages/all", summary="获取所有支付套餐（后台管理）")
async def get_all_packages():
    """获取所有支付套餐（不分页）"""
    packages = await PaymentPackage.all().order_by("sort_order")
    data = []
    for pkg in packages:
        package_data = {
            "id": pkg.id,
            "name": pkg.name,
            "duration": pkg.duration,
            "duration_seconds": pkg.duration_seconds,
            "price": float(pkg.price),
            "original_price": float(pkg.original_price) if pkg.original_price else None,
            "tag": pkg.tag,
            "is_active": pkg.is_active,
            "sort_order": pkg.sort_order,
            "created_at": pkg.created_at.isoformat() if pkg.created_at else None,
            "updated_at": pkg.updated_at.isoformat() if pkg.updated_at else None,
        }
        data.append(package_data)
    return Success(data=data)

@router.post("/packages", summary="新增支付套餐（后台管理）")
async def create_package(pkg: PaymentPackageInfo):
    """新增支付套餐"""
    pkg_dict = pkg.model_dump(exclude_unset=True, exclude={'id', 'created_at', 'updated_at'})
    obj = await PaymentPackage.create(**pkg_dict)
    package_data = {
        "id": obj.id,
        "name": obj.name,
        "duration": obj.duration,
        "duration_seconds": obj.duration_seconds,
        "price": float(obj.price),
        "original_price": float(obj.original_price) if obj.original_price else None,
        "tag": obj.tag,
        "is_active": obj.is_active,
        "sort_order": obj.sort_order,
        "created_at": obj.created_at.isoformat() if obj.created_at else None,
        "updated_at": obj.updated_at.isoformat() if obj.updated_at else None,
    }
    return Success(data=package_data)

@router.put("/packages/{id}", summary="更新支付套餐（后台管理）")
async def update_package(id: int, pkg: PaymentPackageInfo):
    """更新支付套餐"""
    obj = await PaymentPackage.get_or_none(id=id)
    if not obj:
        raise HTTPException(status_code=404, detail="套餐不存在")
    
    update_data = pkg.model_dump(exclude_unset=True, exclude={'id', 'created_at', 'updated_at'})
    for k, v in update_data.items():
        if hasattr(obj, k):
            setattr(obj, k, v)
    await obj.save()
    
    package_data = {
        "id": obj.id,
        "name": obj.name,
        "duration": obj.duration,
        "duration_seconds": obj.duration_seconds,
        "price": float(obj.price),
        "original_price": float(obj.original_price) if obj.original_price else None,
        "tag": obj.tag,
        "is_active": obj.is_active,
        "sort_order": obj.sort_order,
        "created_at": obj.created_at.isoformat() if obj.created_at else None,
        "updated_at": obj.updated_at.isoformat() if obj.updated_at else None,
    }
    return Success(data=package_data)

@router.delete("/packages/{id}", summary="删除支付套餐（后台管理）")
async def delete_package(id: int):
    """删除支付套餐"""
    obj = await PaymentPackage.get_or_none(id=id)
    if not obj:
        raise HTTPException(status_code=404, detail="套餐不存在")
    await obj.delete()
    return Success(data={"id": id, "message": "删除成功"})

@router.get("/orders", summary="获取用户订单列表")
async def get_user_orders(
    user_id: str,
    page: int = 1,
    size: int = 10
):
    """获取用户订单列表"""
    user = await AIReliefUser.get(user_id=user_id)

    # 获取总数
    total = await Order.filter(user=user).count()

    # 分页查询，包含套餐信息
    orders = await Order.filter(user=user).prefetch_related('package').order_by("-created_at").offset((page-1)*size).limit(size)

    data = []
    for order in orders:
        order_data = {
            "id": order.id,
            "order_id": order.order_id,
            "package_name": order.package_name,
            "amount": float(order.amount),
            "original_amount": float(order.original_amount),
            "discount_amount": float(order.discount_amount),
            "coupon_code": order.coupon_code,
            "status": order.status,
            "payment_method": order.payment_method,
            "wechat_transaction_id": order.wechat_transaction_id,
            "created_at": order.created_at.isoformat(),
            "pay_time": order.pay_time.isoformat() if order.pay_time else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None,
            # 添加套餐信息
            "package_duration": order.package.duration if order.package else None,
            "package_duration_seconds": order.package.duration_seconds if order.package else None
        }
        data.append(order_data)

    return Success(data={
        "items": data,
        "total": total,
        "page": page,
        "size": size,
        "pages": (total + size - 1) // size
    })

from pydantic import BaseModel

class CreateOrderRequest(BaseModel):
    """创建订单请求模型"""
    user_id: str
    package_id: int
    coupon_code: Optional[str] = None

@router.post("/create-order", summary="创建JSAPI支付订单")
async def create_wechat_order(request: CreateOrderRequest):
    """创建微信JSAPI支付订单"""
    try:
        result = await wechat_pay_service.create_jsapi_order(
            user_id=request.user_id,
            package_id=request.package_id,
            coupon_code=request.coupon_code
        )
        return Success(data=result)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/notify", summary="微信支付结果通知")
async def wechat_pay_notify(request: Request):
    """接收微信支付结果通知"""
    try:
        success = await wechat_pay_service.handle_pay_notify(
            headers=request.headers,
            body=await request.body()
        )
        
        if success:
            return {"code": "SUCCESS", "message": "OK"}
        else:
            return {"code": "FAIL", "message": "处理失败"}
    except Exception as e:
        return {"code": "FAIL", "message": str(e)}

class CouponValidateRequest(BaseModel):
    """优惠券验证请求模型"""
    coupon_code: str

@router.post("/coupons/validate", summary="验证优惠券")
async def validate_coupon(request: CouponValidateRequest):
    """验证优惠券代码是否有效"""
    try:
        from app.api.v1.airelief.coupon import validate_coupon_code
        
        is_valid, message, coupon = await validate_coupon_code(request.coupon_code)
        
        if is_valid and coupon:
            return Success(data={
                "valid": True,
                "message": message,
                "coupon": {
                    "code": coupon.code,
                    "type": coupon.type,
                    "value": float(coupon.value),
                    "min_amount": float(coupon.min_amount) if coupon.min_amount else None,
                }
            })
        else:
            return Success(data={
                "valid": False,
                "message": message,
                "coupon": None
            })
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# ===================== 后台管理订单接口 =====================

@router.get("/orders/statistics", summary="订单统计（后台管理）")
async def get_orders_statistics(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """获取订单统计信息（后台管理）"""
    from tortoise.functions import Sum

    # 默认统计最近30天
    if not start_date:
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
    if not end_date:
        end_date = datetime.now().isoformat()

    start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
    end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))

    # 基础统计
    base_query = Order.filter(created_at__gte=start_dt, created_at__lte=end_dt)

    total_orders = await base_query.count()
    paid_orders = await base_query.filter(status=Order.STATUS_PAID).count()
    pending_orders = await base_query.filter(status=Order.STATUS_PENDING).count()
    failed_orders = await base_query.filter(status=Order.STATUS_FAILED).count()
    cancelled_orders = await base_query.filter(status=Order.STATUS_CANCELLED).count()
    refunded_orders = await base_query.filter(status=Order.STATUS_REFUNDED).count()

    # 收入统计
    paid_orders_query = base_query.filter(status=Order.STATUS_PAID)

    # 计算总金额
    total_amount_result = await paid_orders_query.annotate(total=Sum('amount')).values('total')
    total_amount = total_amount_result[0]['total'] if total_amount_result and total_amount_result[0]['total'] else 0

    # 计算总优惠金额
    total_discount_result = await paid_orders_query.annotate(total=Sum('discount_amount')).values('total')
    total_discount = total_discount_result[0]['total'] if total_discount_result and total_discount_result[0]['total'] else 0

    # 支付成功率
    success_rate = (paid_orders / total_orders * 100) if total_orders > 0 else 0

    # 按天统计（最近7天）
    daily_stats = []
    for i in range(7):
        day_start = datetime.now() - timedelta(days=i)
        day_end = day_start + timedelta(days=1)

        daily_orders = await Order.filter(
            created_at__gte=day_start,
            created_at__lt=day_end
        ).count()

        daily_paid = await Order.filter(
            created_at__gte=day_start,
            created_at__lt=day_end,
            status=Order.STATUS_PAID
        ).count()

        daily_amount_result = await Order.filter(
            created_at__gte=day_start,
            created_at__lt=day_end,
            status=Order.STATUS_PAID
        ).annotate(total=Sum('amount')).values('total')

        daily_amount = daily_amount_result[0]['total'] if daily_amount_result and daily_amount_result[0]['total'] else 0

        daily_stats.append({
            "date": day_start.strftime("%Y-%m-%d"),
            "orders": daily_orders,
            "paid_orders": daily_paid,
            "amount": float(daily_amount)
        })

    return Success(data={
        "period": {
            "start_date": start_date,
            "end_date": end_date
        },
        "overview": {
            "total_orders": total_orders,
            "paid_orders": paid_orders,
            "pending_orders": pending_orders,
            "failed_orders": failed_orders,
            "cancelled_orders": cancelled_orders,
            "refunded_orders": refunded_orders,
            "success_rate": round(success_rate, 2),
            "total_amount": float(total_amount),
            "total_discount": float(total_discount)
        },
        "daily_stats": daily_stats
    })

@router.get("/orders/{order_id}/status", summary="查询订单支付状态")
async def query_order_status(order_id: str):
    """查询订单支付状态"""
    order = await Order.get_or_none(order_id=order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")

    return Success(data={
        "order_id": order.order_id,
        "status": order.status,
        "amount": float(order.amount),
        "pay_time": order.pay_time.isoformat() if order.pay_time else None
    })

@router.get("/orders/all", summary="获取所有订单（后台管理）")
async def get_all_orders(
    page: int = 1,
    size: int = 20,
    status: Optional[str] = None,
    payment_method: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    search: Optional[str] = None
):
    """
    获取所有订单列表（后台管理）
    支持按状态、支付方式、日期范围、关键词搜索筛选
    """
    # 构建查询条件
    query = Order.all()
    
    # 状态筛选
    if status:
        query = query.filter(status=status)
    
    # 支付方式筛选  
    if payment_method:
        query = query.filter(payment_method=payment_method)
    
    # 日期范围筛选
    if start_date:
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        query = query.filter(created_at__gte=start_dt)
    
    if end_date:
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        query = query.filter(created_at__lte=end_dt)
    
    # 关键词搜索（订单号、套餐名称）
    if search:
        query = query.filter(
            Q(order_id__icontains=search) | 
            Q(package_name__icontains=search) |
            Q(wechat_transaction_id__icontains=search)
        )
    
    # 获取总数
    total = await query.count()
    
    # 分页查询
    orders = await query.prefetch_related('user', 'package').order_by("-created_at").offset((page-1)*size).limit(size)
    
    data = []
    for order in orders:
        order_data = {
            "id": order.id,
            "order_id": order.order_id,
            "user_info": {
                "user_id": order.user.user_id,
                "nickname": order.user.nickname,
                "phone": order.user.phone,
                "wechat_openid": order.user.wechat_openid[:10] + "***" if order.user.wechat_openid else None  # 脱敏
            },
            "package_name": order.package_name,
            "amount": float(order.amount),
            "original_amount": float(order.original_amount),
            "discount_amount": float(order.discount_amount),
            "coupon_code": order.coupon_code,
            "status": order.status,
            "payment_method": order.payment_method,
            "wechat_transaction_id": order.wechat_transaction_id,
            "wechat_prepay_id": order.wechat_prepay_id,
            "wechat_bank_type": order.wechat_bank_type,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "pay_time": order.pay_time.isoformat() if order.pay_time else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None,
        }
        data.append(order_data)
    
    return Success(data={
        "items": data,
        "total": total,
        "page": page,
        "size": size,
        "pages": (total + size - 1) // size
    })

@router.get("/orders/{order_id}", summary="获取订单详情（后台管理）")
async def get_order_detail(order_id: str):
    """获取订单详细信息（后台管理）"""
    order = await Order.get_or_none(order_id=order_id).prefetch_related('user', 'package')
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 获取优惠券使用记录（如果有）
    coupon_usage = None
    if order.coupon_code:
        from app.models.airelief import CouponUsage
        coupon_usage = await CouponUsage.get_or_none(order=order).prefetch_related('coupon')
    
    order_data = {
        "id": order.id,
        "order_id": order.order_id,
        "user_info": {
            "user_id": order.user.user_id,
            "nickname": order.user.nickname,
            "phone": order.user.phone,
            "wechat_openid": order.user.wechat_openid,
            "avatar": order.user.avatar,
            "is_new_user": order.user.is_new_user,
            "duration": order.user.duration,
            "total_duration": order.user.total_duration,
            "expiry_date": order.user.expiry_date.isoformat() if order.user.expiry_date else None
        },
        "package_info": {
            "id": order.package.id,
            "name": order.package.name,
            "duration": order.package.duration,
            "duration_seconds": order.package.duration_seconds,
            "price": float(order.package.price),
            "original_price": float(order.package.original_price) if order.package.original_price else None
        },
        "order_details": {
            "package_name": order.package_name,
            "amount": float(order.amount),
            "original_amount": float(order.original_amount),
            "discount_amount": float(order.discount_amount),
            "coupon_code": order.coupon_code,
            "status": order.status,
            "payment_method": order.payment_method
        },
        "wechat_pay_info": {
            "transaction_id": order.wechat_transaction_id,
            "prepay_id": order.wechat_prepay_id,
            "trade_type": order.wechat_trade_type,
            "bank_type": order.wechat_bank_type,
            "cash_fee": order.wechat_cash_fee,
            "notify_url": order.wechat_notify_url,
            "extra_data": order.wechat_extra_data
        },
        "coupon_info": {
            "code": coupon_usage.coupon.code if coupon_usage else None,
            "type": coupon_usage.coupon.type if coupon_usage else None,
            "value": float(coupon_usage.coupon.value) if coupon_usage else None,
            "used_at": coupon_usage.used_at.isoformat() if coupon_usage else None
        } if coupon_usage else None,
        "timestamps": {
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "pay_time": order.pay_time.isoformat() if order.pay_time else None,
            "updated_at": order.updated_at.isoformat() if order.updated_at else None
        }
    }
    
    return Success(data=order_data)

@router.post("/orders/{order_id}/cancel", summary="取消订单（用户操作）")
async def cancel_order(order_id: str, reason: Optional[str] = None):
    """
    用户取消订单
    只能取消待支付状态的订单
    """
    order = await Order.get_or_none(order_id=order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")

    # 只能取消待支付的订单
    if order.status != Order.STATUS_PENDING:
        raise HTTPException(
            status_code=400,
            detail=f"只能取消待支付的订单，当前订单状态为：{order.status}"
        )

    old_status = order.status
    order.status = Order.STATUS_CANCELLED

    # 记录取消原因
    if not order.wechat_extra_data:
        order.wechat_extra_data = {}

    order.wechat_extra_data['status_changes'] = order.wechat_extra_data.get('status_changes', [])
    order.wechat_extra_data['status_changes'].append({
        "from_status": old_status,
        "to_status": Order.STATUS_CANCELLED,
        "reason": reason or "用户主动取消",
        "changed_at": datetime.now().isoformat(),
        "changed_by": "user"
    })

    await order.save()

    return Success(data={
        "order_id": order.order_id,
        "old_status": old_status,
        "new_status": Order.STATUS_CANCELLED,
        "message": "订单取消成功"
    })

@router.put("/orders/{order_id}/status", summary="更新订单状态（后台管理）")
async def update_order_status(order_id: str, new_status: str, reason: Optional[str] = None):
    """
    更新订单状态（后台管理）
    支持的状态转换：
    - pending -> cancelled (取消订单)
    - paid -> refunded (退款)
    """
    order = await Order.get_or_none(order_id=order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")

    # 验证状态转换的合法性
    valid_transitions = {
        Order.STATUS_PENDING: [Order.STATUS_CANCELLED],
        Order.STATUS_PAID: [Order.STATUS_REFUNDED]
    }

    if new_status not in valid_transitions.get(order.status, []):
        raise HTTPException(
            status_code=400,
            detail=f"不能将订单状态从 {order.status} 更改为 {new_status}"
        )

    old_status = order.status
    order.status = new_status

    # 如果是退款，需要扣除用户时长
    if new_status == Order.STATUS_REFUNDED and old_status == Order.STATUS_PAID:
        await _handle_refund_duration(order)

    # 记录状态变更原因
    if not order.wechat_extra_data:
        order.wechat_extra_data = {}

    order.wechat_extra_data['status_changes'] = order.wechat_extra_data.get('status_changes', [])
    order.wechat_extra_data['status_changes'].append({
        "from_status": old_status,
        "to_status": new_status,
        "reason": reason,
        "changed_at": datetime.now().isoformat(),
        "changed_by": "admin"  # 这里可以改为实际操作员信息
    })

    await order.save()

    return Success(data={
        "order_id": order.order_id,
        "old_status": old_status,
        "new_status": new_status,
        "message": "订单状态更新成功"
    })



async def _handle_refund_duration(order: Order):
    """处理退款时的用户时长扣除"""
    user = await order.user
    package = await order.package

    # 扣除总陪伴时长
    user.total_duration = max(0, user.total_duration - package.duration_seconds)

    # 更新会员到期时间（实时剩余时长基于此计算）
    if user.expiry_date:
        # 统一处理时区，将expiry_date转换为naive datetime进行计算
        expiry_date = user.expiry_date.replace(tzinfo=None) if user.expiry_date.tzinfo else user.expiry_date
        new_expiry = expiry_date - timedelta(seconds=package.duration_seconds)
        # 确保到期时间不早于当前时间
        now = datetime.now()
        user.expiry_date = max(now, new_expiry)

    await user.save()
