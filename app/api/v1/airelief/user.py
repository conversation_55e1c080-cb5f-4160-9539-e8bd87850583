from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, UploadFile, File
from pydantic import BaseModel
from loguru import logger
import uuid
from pathlib import Path

from app.schemas.base import Success, SuccessExtra
from app.schemas.airelief import AI<PERSON><PERSON>efUserInfo, UserUpdateRequest
from app.models.airelief import AIReliefUser
from app.core.dependency import DependPermission, DependAIReliefAuth
from app.utils.sms import sms_service

router = APIRouter()


def convert_user_to_response(user) -> AIReliefUserInfo:
    """将数据库用户对象转换为API响应格式"""
    return AIReliefUserInfo(
        id=user.id,
        user_id=user.user_id,
        wechat_openid=user.wechat_openid,
        wechat_unionid=user.wechat_unionid,
        nickname=user.nickname,
        avatar=user.avatar,
        phone=user.phone,
        gender=user.gender,
        birthday=user.birthday.isoformat() if user.birthday else None,
        wechat_province=user.wechat_province,
        wechat_city=user.wechat_city,
        wechat_country=user.wechat_country,
        wechat_privilege=user.wechat_privilege or [],
        is_wechat_subscribed=user.is_wechat_subscribed,
        isNewUser=user.is_new_user,
        duration=user.remaining_duration,  # 使用实时计算的剩余时长
        totalDuration=user.total_duration,
        consumedDuration=user.consumed_duration,  # 使用计算属性
        companionDays=user.companion_days,  # 使用计算属性
        expiryDate=user.expiry_date.isoformat() if user.expiry_date else None,
        ipLocation=user.ip_location,
        is_active=user.is_active,
        last_login=user.last_login.isoformat() if user.last_login else None,
        created_at=user.created_at.isoformat(),
        updated_at=user.updated_at.isoformat()
    )


# 请求模型
class SendSmsCodeRequest(BaseModel):
    """发送短信验证码请求"""
    phone: str


class BindPhoneRequest(BaseModel):
    """绑定手机号请求"""
    phone: str
    code: str


class BindWeChatRequest(BaseModel):
    """绑定微信请求"""
    wechat_code: str  # 微信授权码

@router.get("/list", summary="获取AI-Relief用户列表")
async def get_airelief_user_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    id: Optional[int] = Query(None, description="数据库ID"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    nickname: Optional[str] = Query(None, description="昵称"),
    phone: Optional[str] = Query(None, description="手机号"),
    _: str = DependPermission
):
    """获取AI-Relief用户列表（管理员接口）"""
    try:
        # 构建查询条件
        query = AIReliefUser.all()

        if id:
            query = query.filter(id=id)
        if user_id:
            query = query.filter(user_id__icontains=user_id)
        if nickname:
            query = query.filter(nickname__icontains=nickname)
        if phone:
            query = query.filter(phone__icontains=phone)

        # 分页查询
        total = await query.count()
        offset = (page - 1) * page_size
        users = await query.offset(offset).limit(page_size).order_by('-created_at')

        # 转换为响应格式
        user_list = []
        for user in users:
            # 使用统一的用户信息转换函数，确保日期字段正确序列化
            user_info = convert_user_to_response(user)
            # 转换为字典格式
            user_dict = user_info.model_dump()
            user_list.append(user_dict)

        return SuccessExtra(data=user_list, total=total, page=page, page_size=page_size)

    except Exception as e:
        logger.error(f"获取AI-Relief用户列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户列表失败")

@router.get("/get", summary="获取单个AI-Relief用户信息")
async def get_airelief_user_by_id(
    user_id: int = Query(..., description="用户ID"),
    _: str = DependPermission
):
    """根据ID获取AI-Relief用户信息（管理员接口）"""
    try:
        user = await AIReliefUser.get_or_none(id=user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 使用统一的用户信息转换函数，确保日期字段正确序列化
        user_info = convert_user_to_response(user)
        # 转换为字典格式
        user_dict = user_info.model_dump()

        return Success(data=user_dict)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI-Relief用户信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户信息失败")

@router.post("/update", summary="更新AI-Relief用户信息")
async def update_airelief_user(
    user_data: dict,
    _: str = DependPermission
):
    """更新AI-Relief用户信息（管理员接口）"""
    try:
        user_id = user_data.get('id')
        if not user_id:
            raise HTTPException(status_code=400, detail="用户ID不能为空")

        user = await AIReliefUser.get_or_none(id=user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 更新允许修改的字段
        update_fields = {}
        if 'nickname' in user_data:
            update_fields['nickname'] = user_data['nickname']
        if 'phone' in user_data:
            update_fields['phone'] = user_data['phone']
        if 'gender' in user_data:
            update_fields['gender'] = user_data['gender']
        if 'duration' in user_data:
            # 管理员手动设置剩余时长时，需要相应更新到期时间
            duration_seconds = int(user_data['duration'])
            if duration_seconds > 0:
                from datetime import timedelta
                update_fields['expiry_date'] = datetime.now() + timedelta(seconds=duration_seconds)
            else:
                update_fields['expiry_date'] = None
        if 'expiry_date' in user_data or 'expiryDate' in user_data:
            expiry_value = user_data.get('expiryDate') or user_data.get('expiry_date')
            if expiry_value:
                # 处理时间戳格式
                if isinstance(expiry_value, (int, float)):
                    update_fields['expiry_date'] = datetime.fromtimestamp(expiry_value / 1000)
                else:
                    update_fields['expiry_date'] = expiry_value
            else:
                update_fields['expiry_date'] = None
        if 'ip_location' in user_data or 'ipLocation' in user_data:
            update_fields['ip_location'] = user_data.get('ipLocation') or user_data.get('ip_location')
        if 'is_active' in user_data:
            update_fields['is_active'] = user_data['is_active']

        # 执行更新
        await user.update_from_dict(update_fields)
        await user.save()

        return Success(data={"message": "用户信息更新成功"})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新AI-Relief用户信息失败: {e}")
        raise HTTPException(status_code=500, detail="更新用户信息失败")

@router.delete("/delete", summary="删除AI-Relief用户")
async def delete_airelief_user(
    user_id: int = Query(..., description="用户ID"),
    _: str = DependPermission
):
    """删除AI-Relief用户（管理员接口）"""
    try:
        user = await AIReliefUser.get_or_none(id=user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        await user.delete()

        return Success(data={"message": "用户删除成功"})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除AI-Relief用户失败: {e}")
        raise HTTPException(status_code=500, detail="删除用户失败")

@router.get("/profile", summary="获取用户信息")
async def get_user_profile(current_user: AIReliefUser = DependAIReliefAuth):
    """获取用户个人信息"""
    try:
        # 转换为响应格式
        user_info = convert_user_to_response(current_user)

        logger.info(f"获取用户信息成功: user_id={current_user.user_id}, is_new_user={current_user.is_new_user}")

        return user_info

    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取用户信息失败")

@router.put("/profile", summary="更新用户信息")
async def update_user_profile(
    update_data: UserUpdateRequest,
    current_user: AIReliefUser = DependAIReliefAuth
):
    """更新用户个人信息"""
    try:
        # 构建更新字段字典，只更新提供的非空字段
        update_fields = {}

        if update_data.nickname is not None:
            # 验证昵称长度和内容
            nickname = update_data.nickname.strip()
            if not nickname:
                raise HTTPException(status_code=400, detail="昵称不能为空")
            if len(nickname) > 100:
                raise HTTPException(status_code=400, detail="昵称长度不能超过100个字符")
            update_fields['nickname'] = nickname

        if update_data.avatar is not None:
            # 验证头像URL格式（简单验证）
            avatar = update_data.avatar.strip()
            if avatar and not (avatar.startswith('http://') or avatar.startswith('https://')):
                raise HTTPException(status_code=400, detail="头像URL格式不正确")
            update_fields['avatar'] = avatar if avatar else None

        if update_data.phone is not None:
            # 验证手机号格式
            phone = update_data.phone.strip() if update_data.phone else None
            if phone:
                # 简单的手机号格式验证（中国大陆手机号）
                import re
                if not re.match(r'^1[3-9]\d{9}$', phone):
                    raise HTTPException(status_code=400, detail="手机号格式不正确")

                # 检查手机号是否已被其他用户使用
                existing_user = await AIReliefUser.get_or_none(phone=phone)
                if existing_user and existing_user.id != current_user.id:
                    raise HTTPException(status_code=400, detail="该手机号已被其他用户使用")

            update_fields['phone'] = phone

        if update_data.gender is not None:
            # 验证性别值
            if update_data.gender not in [0, 1, 2]:
                raise HTTPException(status_code=400, detail="性别值无效，必须为0(未知)、1(女性)或2(男性)")
            update_fields['gender'] = update_data.gender

        if update_data.birthday is not None:
            # 验证生日日期
            from datetime import date
            if isinstance(update_data.birthday, str):
                try:
                    # 尝试解析日期字符串
                    birthday_date = datetime.strptime(update_data.birthday, '%Y-%m-%d').date()
                except ValueError:
                    raise HTTPException(status_code=400, detail="生日格式不正确，请使用YYYY-MM-DD格式")
            else:
                birthday_date = update_data.birthday.date() if hasattr(update_data.birthday, 'date') else update_data.birthday

            # 验证生日不能是未来日期
            if birthday_date > date.today():
                raise HTTPException(status_code=400, detail="生日不能是未来日期")

            update_fields['birthday'] = birthday_date

        # 如果没有任何字段需要更新
        if not update_fields:
            raise HTTPException(status_code=400, detail="没有提供需要更新的字段")

        # 执行更新
        await current_user.update_from_dict(update_fields)
        await current_user.save()

        logger.info(f"用户信息更新成功: user_id={current_user.user_id}, updated_fields={list(update_fields.keys())}")

        # 返回更新后的用户信息
        updated_user_info = convert_user_to_response(current_user)

        return updated_user_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户信息失败: user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="更新用户信息失败")


@router.post("/send-sms-code", summary="发送短信验证码")
async def send_sms_code(
    request: SendSmsCodeRequest,
    current_user: AIReliefUser = DependAIReliefAuth
):
    """发送短信验证码"""
    try:
        # 验证手机号格式
        import re
        if not re.match(r'^1[3-9]\d{9}$', request.phone):
            raise HTTPException(status_code=400, detail="手机号格式不正确")

        # 检查手机号是否已被其他用户使用
        existing_user = await AIReliefUser.get_or_none(phone=request.phone)
        if existing_user and existing_user.id != current_user.id:
            raise HTTPException(status_code=400, detail="该手机号已被其他用户使用")

        # 发送短信验证码
        result = await sms_service.send_verification_code(request.phone)

        if result['success']:
            logger.info(f"验证码发送成功: phone={request.phone}, user_id={current_user.user_id}")
            return Success(data={
                "message": result['message'],
                "expires_in": result['data']['expires_in']
            })
        else:
            logger.error(f"验证码发送失败: phone={request.phone}, user_id={current_user.user_id}, error={result['message']}")
            raise HTTPException(status_code=400, detail=result['message'])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送短信验证码失败: phone={request.phone}, user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="发送验证码失败")


@router.post("/bind-phone", summary="绑定手机号")
async def bind_phone(
    request: BindPhoneRequest,
    current_user: AIReliefUser = DependAIReliefAuth
):
    """绑定手机号"""
    try:
        # 验证手机号格式
        import re
        if not re.match(r'^1[3-9]\d{9}$', request.phone):
            raise HTTPException(status_code=400, detail="手机号格式不正确")

        # 验证验证码格式（支持4位和6位验证码）
        if not re.match(r'^\d{4,6}$', request.code):
            raise HTTPException(status_code=400, detail="验证码格式不正确")

        # 验证短信验证码
        verification_result = sms_service.verify_code(request.phone, request.code)
        if not verification_result['valid']:
            raise HTTPException(status_code=400, detail=verification_result['message'])

        # 检查手机号是否已被其他用户使用
        existing_user = await AIReliefUser.get_or_none(phone=request.phone)
        if existing_user and existing_user.id != current_user.id:
            raise HTTPException(status_code=400, detail="该手机号已被其他用户使用")

        # 更新用户手机号
        current_user.phone = request.phone
        await current_user.save()

        logger.info(f"手机号绑定成功: phone={request.phone}, user_id={current_user.user_id}")

        return Success(data={"message": "手机号绑定成功"})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"绑定手机号失败: phone={request.phone}, user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="绑定手机号失败")


@router.post("/unbind-phone", summary="解绑手机号")
async def unbind_phone(current_user: AIReliefUser = DependAIReliefAuth):
    """解绑手机号"""
    try:
        if not current_user.phone:
            raise HTTPException(status_code=400, detail="当前用户未绑定手机号")

        old_phone = current_user.phone
        current_user.phone = None
        await current_user.save()

        logger.info(f"手机号解绑成功: old_phone={old_phone}, user_id={current_user.user_id}")

        return Success(data={"message": "手机号解绑成功"})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解绑手机号失败: user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="解绑手机号失败")


@router.post("/avatar", summary="上传头像")
async def upload_avatar(
    avatar: UploadFile = File(...),
    current_user: AIReliefUser = DependAIReliefAuth
):
    """上传用户头像"""
    try:
        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        if avatar.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="不支持的文件格式，请上传 JPEG、PNG、GIF 或 WebP 格式的图片")

        # 验证文件大小 (5MB)
        max_size = 5 * 1024 * 1024  # 5MB
        content = await avatar.read()
        if len(content) > max_size:
            raise HTTPException(status_code=413, detail="文件大小超出限制，最大支持5MB")

        # 生成唯一文件名
        file_extension = avatar.filename.split('.')[-1] if avatar.filename else 'jpg'
        unique_filename = f"{uuid.uuid4()}.{file_extension}"

        # 创建上传目录
        upload_dir = Path("uploads/avatars")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 保存文件
        file_path = upload_dir / unique_filename
        with open(file_path, "wb") as f:
            f.write(content)

        # 生成访问URL (这里需要根据实际部署环境调整)
        avatar_url = f"/uploads/avatars/{unique_filename}"

        # 更新用户头像
        current_user.avatar = avatar_url
        await current_user.save()

        logger.info(f"头像上传成功: user_id={current_user.user_id}, avatar_url={avatar_url}")

        return Success(data={"avatarUrl": avatar_url})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传头像失败: user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="上传头像失败")


@router.post("/deactivate", summary="注销账号")
async def deactivate_account(current_user: AIReliefUser = DependAIReliefAuth):
    """注销用户账号"""
    try:
        # 将用户状态设置为非激活
        current_user.is_active = False
        await current_user.save()

        logger.info(f"账号注销成功: user_id={current_user.user_id}")

        return Success(data={"message": "账号注销成功"})

    except Exception as e:
        logger.error(f"注销账号失败: user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="注销账号失败")


@router.post("/activate-trial", summary="激活新用户试用")
async def activate_trial(current_user: AIReliefUser = DependAIReliefAuth):
    """激活新用户3天免费试用"""
    try:
        # 验证是否为新用户
        if not current_user.is_new_user:
            raise HTTPException(status_code=400, detail="仅限新用户激活试用")

        # 计算3天试用期
        from datetime import timedelta
        trial_duration_seconds = 3 * 24 * 60 * 60  # 3天的秒数
        now = datetime.now()

        # 更新用户信息
        current_user.total_duration = (current_user.total_duration or 0) + trial_duration_seconds

        # 设置会员到期时间（实时剩余时长基于此计算）
        # 统一处理时区，将expiry_date转换为naive datetime进行比较
        if current_user.expiry_date:
            expiry_date = current_user.expiry_date.replace(tzinfo=None) if current_user.expiry_date.tzinfo else current_user.expiry_date
            if expiry_date > now:
                # 如果当前会员未过期，在现有到期时间基础上延长
                current_user.expiry_date = expiry_date + timedelta(seconds=trial_duration_seconds)
            else:
                # 如果当前会员已过期，从现在开始计算
                current_user.expiry_date = now + timedelta(seconds=trial_duration_seconds)
        else:
            # 如果没有到期时间，从现在开始计算
            current_user.expiry_date = now + timedelta(seconds=trial_duration_seconds)

        current_user.is_new_user = False  # 激活后不再是新用户

        await current_user.save()

        logger.info(f"新用户试用激活成功: user_id={current_user.user_id}, trial_duration=3天")

        # 返回更新后的用户信息
        updated_user_info = convert_user_to_response(current_user)

        # 返回符合前端期望的格式
        return {
            "success": True,
            "message": "恭喜您！成功获得3天免费试用",
            "userInfo": updated_user_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"激活新用户试用失败: user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="激活试用失败")


@router.post("/bind-wechat", summary="绑定微信")
async def bind_wechat(
    request: BindWeChatRequest,
    current_user: AIReliefUser = DependAIReliefAuth
):
    """绑定微信账号"""
    try:
        # TODO: 这里应该实现微信绑定逻辑
        # 1. 通过微信授权码获取用户信息
        # 2. 验证微信账号是否已被其他用户绑定
        # 3. 更新当前用户的微信信息

        logger.info(f"微信绑定请求: user_id={current_user.user_id}, wechat_code={request.wechat_code}")

        # 目前返回开发中的提示
        return Success(data={"message": "微信绑定功能开发中，敬请期待"})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"绑定微信失败: user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="绑定微信失败")


@router.post("/unbind-wechat", summary="解绑微信")
async def unbind_wechat(current_user: AIReliefUser = DependAIReliefAuth):
    """解绑微信账号"""
    try:
        # 检查是否绑定了微信
        if not current_user.wechat_openid:
            raise HTTPException(status_code=400, detail="当前用户未绑定微信账号")

        # 保存原始微信信息用于日志
        old_openid = current_user.wechat_openid
        old_nickname = current_user.nickname

        # 清除微信授权令牌和用户详细信息，但保留wechat_openid作为身份标识
        # 注意：由于系统基于微信登录，wechat_openid是用户身份的核心，不应删除
        current_user.wechat_access_token = None
        current_user.wechat_refresh_token = None
        current_user.wechat_token_expires_in = 0  # 设置为0而不是None，因为该字段不允许为空
        current_user.wechat_token_expires_at = None
        current_user.wechat_unionid = None
        current_user.wechat_province = None
        current_user.wechat_city = None
        current_user.wechat_country = None
        current_user.wechat_privilege = []
        current_user.is_wechat_subscribed = False
        current_user.wechat_subscribe_time = None
        current_user.wechat_unsubscribe_time = None
        
        # 清除从微信获取的用户信息，恢复为默认状态
        current_user.nickname = f"用户{current_user.user_id}"  # 使用默认昵称
        current_user.avatar = None  # 清除头像
        current_user.gender = 0  # 重置性别为未知
        
        # 保存用户信息
        await current_user.save()

        logger.info(f"微信授权信息清除成功: user_id={current_user.user_id}, openid={old_openid}, nickname={old_nickname}")

        return Success(data={"message": "微信授权信息已清除，用户身份保留"})

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除微信授权信息失败: user_id={current_user.user_id}, error={e}")
        raise HTTPException(status_code=500, detail="清除微信授权信息失败")
