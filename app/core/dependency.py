from typing import Optional

import jwt
from fastapi import Depends, Header, HTTPException, Request

from app.core.ctx import CTX_USER_ID
from app.models import Role, User
from app.models.airelief import AIReliefUser
from app.settings import settings


class AuthControl:
    @classmethod
    async def is_authed(cls, token: str = Header(..., description="token验证")) -> Optional["User"]:
        try:
            if token == "dev":
                user = await User.filter().first()
                user_id = user.id
            else:
                decode_data = jwt.decode(token, settings.SECRET_KEY, algorithms=settings.JWT_ALGORITHM)
                user_id = decode_data.get("user_id")
            user = await User.filter(id=user_id).first()
            if not user:
                raise HTTPException(status_code=401, detail="Authentication failed")
            CTX_USER_ID.set(int(user_id))
            return user
        except jwt.DecodeError:
            raise HTTPException(status_code=401, detail="无效的Token")
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="登录已过期")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"{repr(e)}")


class PermissionControl:
    @classmethod
    async def has_permission(cls, request: Request, current_user: User = Depends(AuthControl.is_authed)) -> None:
        if current_user.is_superuser:
            return
        method = request.method
        path = request.url.path
        roles: list[Role] = await current_user.roles
        if not roles:
            raise HTTPException(status_code=403, detail="The user is not bound to a role")
        apis = [await role.apis for role in roles]
        permission_apis = list(set((api.method, api.path) for api in sum(apis, [])))
        # path = "/api/v1/auth/userinfo"
        # method = "GET"
        if (method, path) not in permission_apis:
            raise HTTPException(status_code=403, detail=f"Permission denied method:{method} path:{path}")


class AIReliefAuthControl:
    """AIRelief用户认证控制"""

    @classmethod
    async def is_authed(cls, authorization: str = Header(..., alias="Authorization")) -> Optional["AIReliefUser"]:
        """验证AIRelief用户token"""
        try:
            # 从Authorization header中提取token
            if not authorization.startswith("Bearer "):
                raise HTTPException(status_code=401, detail="Invalid authorization header format")

            token = authorization.replace("Bearer ", "")

            if token == "dev":
                # 开发模式，返回第一个用户
                user = await AIReliefUser.filter().first()
                if not user:
                    raise HTTPException(status_code=401, detail="No AIRelief users found")
                return user
            else:
                # 解析JWT token
                decode_data = jwt.decode(token, settings.SECRET_KEY, algorithms=settings.JWT_ALGORITHM)
                user_id = decode_data.get("user_id")

                if not user_id:
                    raise HTTPException(status_code=401, detail="Invalid token: missing user_id")

                # 查找AIRelief用户
                user = await AIReliefUser.get_or_none(id=user_id)
                if not user:
                    raise HTTPException(status_code=401, detail="User not found")

                # 检查用户是否激活
                if not user.is_active:
                    raise HTTPException(status_code=401, detail="User is inactive")

                return user

        except jwt.DecodeError:
            raise HTTPException(status_code=401, detail="无效的Token")
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="登录已过期")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Authentication error: {repr(e)}")


DependAuth = Depends(AuthControl.is_authed)
DependPermission = Depends(PermissionControl.has_permission)
DependAIReliefAuth = Depends(AIReliefAuthControl.is_authed)
