import time
import uuid
from decimal import Decimal
from datetime import datetime, timedelta
from app.models.airelief import Order, AIReliefUser, PaymentPackage, Coupon, CouponUsage
from app.settings.config import settings


class MockWeChatPayService:
    """Mock微信支付服务，用于测试环境"""
    
    def __init__(self):
        self.is_configured = True
        self.test_mode = True
        print("🧪 使用Mock微信支付服务（测试模式）")
    
    async def create_jsapi_order(self, user_id: str, package_id: int, coupon_code: str = None):
        """Mock创建JSAPI支付订单"""
        print(f"🧪 Mock创建支付订单: user_id={user_id}, package_id={package_id}, coupon_code={coupon_code}")
        
        # 1. 获取用户和套餐信息
        try:
            user = await AIReliefUser.get(user_id=user_id)
        except Exception as e:
            raise Exception(f"用户不存在: {user_id}")
        
        try:
            package = await PaymentPackage.get(id=package_id, is_active=True)
        except Exception as e:
            raise Exception(f"套餐不存在或未激活: {package_id}")
        
        # 2. 计算价格(处理优惠券逻辑)
        final_amount, discount_amount, coupon = await self._calculate_price(package, coupon_code, user)
        
        # 3. 生成订单号
        order_id = self._generate_order_id()
        
        # 4. 创建本地订单记录
        order = await Order.create(
            order_id=order_id,
            user=user,
            package=package,
            package_name=package.name,
            amount=final_amount,
            original_amount=package.price,
            coupon_code=coupon_code,
            discount_amount=discount_amount,
            status=Order.STATUS_PENDING,
            payment_method="wechat",
            wechat_trade_type="JSAPI",
            wechat_notify_url=settings.WECHAT_PAY_NOTIFY_URL,
            wechat_prepay_id=f"mock_prepay_{uuid.uuid4().hex[:16]}"
        )
        
        # 5. 生成Mock支付参数
        pay_params = self._generate_mock_pay_params(order.wechat_prepay_id)
        
        print(f"✅ Mock订单创建成功: {order_id}")
        
        return {
            "order_id": order_id,
            "pay_params": pay_params
        }
    
    def _generate_mock_pay_params(self, prepay_id: str):
        """生成Mock前端调起支付的参数"""
        timestamp = str(int(time.time()))
        nonce_str = str(uuid.uuid4()).replace('-', '').upper()
        package = f"prepay_id={prepay_id}"
        
        return {
            "appId": settings.WECHAT_PAY_APPID,
            "timeStamp": timestamp,
            "nonceStr": nonce_str,
            "package": package,
            "signType": "RSA",
            "paySign": f"mock_pay_sign_{timestamp}"
        }
    
    async def handle_pay_notify(self, headers, body):
        """Mock处理支付结果通知"""
        print("🧪 Mock处理支付回调")
        
        # 在Mock模式下，我们可以通过请求体中的订单号来模拟成功支付
        try:
            import json
            if isinstance(body, bytes):
                body = body.decode('utf-8')
            
            # 尝试解析请求体获取订单信息
            if isinstance(body, str):
                try:
                    data = json.loads(body)
                    out_trade_no = data.get('out_trade_no')
                except:
                    # 如果解析失败，可能是测试时直接传入的订单号
                    out_trade_no = body
            else:
                out_trade_no = None
            
            if out_trade_no:
                return await self._mock_payment_success(out_trade_no)
            else:
                print("❌ Mock回调: 无法获取订单号")
                return False
                
        except Exception as e:
            print(f"❌ Mock回调处理异常: {str(e)}")
            return False
    
    async def _mock_payment_success(self, order_id: str):
        """Mock支付成功处理"""
        try:
            order = await Order.get(order_id=order_id)
            
            if order.status != Order.STATUS_PENDING:
                print(f"⚠️ 订单 {order_id} 状态不是待支付，当前状态: {order.status}")
                return False
            
            # 更新订单状态为已支付
            order.status = Order.STATUS_PAID
            order.wechat_transaction_id = f"mock_tx_{uuid.uuid4().hex[:16]}"
            order.wechat_bank_type = "MOCK_BANK"
            order.wechat_cash_fee = int(order.amount * 100)  # 转为分
            order.pay_time = datetime.now()
            order.wechat_extra_data = {
                "mock": True,
                "trade_state": "SUCCESS",
                "bank_type": "MOCK_BANK",
                "amount": {"total": int(order.amount * 100)}
            }
            await order.save()
            
            # 增加用户时长
            await self._add_user_duration(order)
            
            # 处理优惠券使用记录
            await self._handle_coupon_usage(order)
            
            print(f"✅ Mock支付成功: {order_id}")
            return True
            
        except Exception as e:
            print(f"❌ Mock支付处理异常: {str(e)}")
            return False
    
    async def _calculate_price(self, package: PaymentPackage, coupon_code: str = None, user: AIReliefUser = None):
        """计算最终价格（处理优惠券逻辑）"""
        final_amount = package.price
        discount_amount = Decimal('0')
        coupon = None

        if coupon_code:
            # 验证优惠券有效性
            is_valid, message, coupon = await self._validate_coupon_for_payment(coupon_code, user)

            if is_valid and coupon:
                if coupon.type == "discount":
                    # 折扣券：直接减免金额
                    discount_amount = min(coupon.value, package.price)
                    final_amount = max(Decimal('0.01'), package.price - discount_amount)  # 最低0.01元
                elif coupon.type == "duration":
                    # 时长券：不能用于支付，应该直接兑换时长
                    raise Exception("时长券不能用于支付，请直接使用兑换功能")
            else:
                raise Exception(f"优惠券无效: {message}")

        return final_amount, discount_amount, coupon

    async def _validate_coupon_for_payment(self, coupon_code: str, user: AIReliefUser):
        """验证优惠券是否可用于支付"""
        try:
            coupon = await Coupon.get(code=coupon_code)
        except:
            return False, "优惠券不存在", None

        # 检查是否激活
        if not coupon.is_active:
            return False, "优惠券已被禁用", coupon

        # 检查有效期
        now = datetime.now()
        
        # 统一处理时区，将所有datetime转换为naive datetime进行比较
        valid_from = coupon.valid_from.replace(tzinfo=None) if coupon.valid_from.tzinfo else coupon.valid_from
        valid_until = coupon.valid_until.replace(tzinfo=None) if coupon.valid_until.tzinfo else coupon.valid_until
        
        if now < valid_from:
            return False, "优惠券尚未生效", coupon

        if now > valid_until:
            return False, "优惠券已过期", coupon

        # 检查使用次数
        if coupon.used_count >= coupon.max_uses:
            return False, "优惠券已达到最大使用次数", coupon

        # 检查用户是否已经使用过这个优惠券
        if user:
            existing_usage = await CouponUsage.filter(
                coupon=coupon,
                user=user
            ).exists()

            if existing_usage:
                return False, "您已经使用过这个优惠券", coupon

        # 检查优惠券类型是否适用于支付
        if coupon.type not in ["discount"]:
            return False, f"{coupon.type}类型的优惠券不能用于支付", coupon

        return True, "优惠券有效", coupon

    async def _handle_coupon_usage(self, order: Order):
        """处理优惠券使用记录"""
        if not order.coupon_code:
            return

        try:
            # 获取优惠券
            coupon = await Coupon.get(code=order.coupon_code)
            user = await order.user

            # 创建使用记录
            await CouponUsage.create(
                coupon=coupon,
                user=user,
                order=order
            )

            # 更新优惠券使用次数
            coupon.used_count += 1
            await coupon.save()

            print(f"✅ Mock优惠券使用记录创建: {order.coupon_code}")

        except Exception as e:
            # 记录错误但不影响主流程
            print(f"❌ Mock处理优惠券使用记录失败: {str(e)}")

    def _generate_order_id(self):
        """生成订单号"""
        timestamp = int(time.time() * 1000)
        random_part = uuid.uuid4().hex[:8].upper()
        return f"AIR{timestamp}{random_part}"

    async def _add_user_duration(self, order: Order):
        """增加用户陪伴时长"""
        try:
            user = await order.user
            package = await order.package

            # 增加总陪伴时长
            user.total_duration += package.duration_seconds

            # 更新会员到期时间
            now = datetime.now()
            
            # 统一处理时区，将expiry_date转换为naive datetime进行比较
            if user.expiry_date:
                expiry_date = user.expiry_date.replace(tzinfo=None) if user.expiry_date.tzinfo else user.expiry_date
                if expiry_date > now:
                    # 如果还有剩余时长，在现有基础上增加
                    user.expiry_date = expiry_date + timedelta(seconds=package.duration_seconds)
                else:
                    # 如果已过期，从当前时间开始计算
                    user.expiry_date = now + timedelta(seconds=package.duration_seconds)
            else:
                # 如果首次购买，从当前时间开始计算
                user.expiry_date = now + timedelta(seconds=package.duration_seconds)

            # 更新新用户状态
            if user.is_new_user:
                user.is_new_user = False

            await user.save()
            
            print(f"✅ Mock用户时长增加: {user.user_id} +{package.duration_seconds}秒")

        except Exception as e:
            print(f"❌ Mock增加用户时长失败: {str(e)}")
    
    # Mock支付特有的测试方法
    
    async def mock_payment_success(self, order_id: str):
        """手动触发Mock支付成功（用于测试）"""
        print(f"🧪 手动触发Mock支付成功: {order_id}")
        return await self._mock_payment_success(order_id)
    
    async def mock_payment_fail(self, order_id: str, reason: str = "Mock支付失败"):
        """手动触发Mock支付失败（用于测试）"""
        try:
            order = await Order.get(order_id=order_id)
            order.status = Order.STATUS_FAILED
            order.wechat_extra_data = {
                "mock": True,
                "error": reason,
                "trade_state": "PAYERROR"
            }
            await order.save()
            
            print(f"✅ Mock支付失败: {order_id} - {reason}")
            return True
            
        except Exception as e:
            print(f"❌ Mock支付失败处理异常: {str(e)}")
            return False
    
    def get_mock_info(self):
        """获取Mock服务信息"""
        return {
            "service_type": "MockWeChatPayService",
            "test_mode": True,
            "is_configured": True,
            "features": [
                "create_jsapi_order",
                "handle_pay_notify", 
                "mock_payment_success",
                "mock_payment_fail"
            ],
            "description": "微信支付Mock服务，用于测试环境模拟支付功能"
        } 