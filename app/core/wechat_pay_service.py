import time
import uuid
from decimal import Decimal
from datetime import datetime, timedelta
from wechatpayv3 import WeChatPay, WeChatPayType
from app.models.airelief import Order, AIReliefUser, PaymentPackage, Coupon, CouponUsage
from app.settings.config import settings


class WeChatPayService:
    def __init__(self):
        try:
            with open(settings.WECHAT_PAY_PRIVATE_KEY_PATH) as f:
                private_key = f.read()

            self.wxpay = WeChatPay(
                wechatpay_type=WeChatPayType.JSAPI,
                mchid=settings.WECHAT_PAY_MCHID,
                private_key=private_key,
                cert_serial_no=settings.WECHAT_PAY_CERT_SERIAL_NO,
                apiv3_key=settings.WECHAT_PAY_APIV3_KEY,
                appid=settings.WECHAT_PAY_APPID,
                notify_url=settings.WECHAT_PAY_NOTIFY_URL,
                cert_dir=settings.WECHAT_PAY_CERT_DIR,
                timeout=settings.WECHAT_PAY_TIMEOUT
            )
            self.is_configured = True
        except FileNotFoundError as e:
            print(f"⚠️  微信支付证书文件未找到: {e}")
            print("   请从微信商户平台下载证书文件并放置到正确位置")
            self.wxpay = None
            self.is_configured = False
        except Exception as e:
            print(f"⚠️  微信支付初始化失败: {e}")
            self.wxpay = None
            self.is_configured = False
    
    async def create_jsapi_order(self, user_id: str, package_id: int, coupon_code: str = None):
        """创建JSAPI支付订单"""
        # 检查微信支付是否已正确配置
        if not self.is_configured:
            raise Exception("微信支付未正确配置，请检查证书文件和配置")

        # 1. 获取用户和套餐信息
        user = await AIReliefUser.get(user_id=user_id)
        package = await PaymentPackage.get(id=package_id, is_active=True)
        
        # 2. 计算价格(处理优惠券逻辑)
        final_amount, discount_amount, coupon = await self._calculate_price(package, coupon_code, user)
        
        # 3. 生成订单号
        order_id = self._generate_order_id()
        
        # 4. 创建本地订单记录
        order = await Order.create(
            order_id=order_id,
            user=user,
            package=package,
            package_name=package.name,
            amount=final_amount,
            original_amount=package.price,
            coupon_code=coupon_code,
            discount_amount=discount_amount,
            status=Order.STATUS_PENDING,
            payment_method="wechat",
            wechat_trade_type="JSAPI",
            wechat_notify_url=settings.WECHAT_PAY_NOTIFY_URL
        )
        
        # 5. 调用微信支付统一下单API
        code, message = self.wxpay.pay(
            description=f"AI-Relief {package.name}",
            out_trade_no=order_id,
            amount={'total': int(final_amount * 100)},  # 转为分
            payer={'openid': user.wechat_openid}
        )
        
        if code == 200:
            # 解析返回结果
            import json
            result = json.loads(message)
            prepay_id = result.get('prepay_id')
            
            # 更新订单的prepay_id
            order.wechat_prepay_id = prepay_id
            await order.save()
            
            # 生成前端调起支付的参数
            pay_params = self._generate_pay_params(prepay_id)
            
            return {
                "order_id": order_id,
                "pay_params": pay_params
            }
        else:
            # 支付创建失败，更新订单状态
            order.status = Order.STATUS_FAILED
            order.wechat_extra_data = {"error": message}
            await order.save()
            raise Exception(f"创建支付订单失败: {message}")
    
    def _generate_pay_params(self, prepay_id: str):
        """生成前端调起支付的参数"""
        timestamp = str(int(time.time()))
        nonce_str = str(uuid.uuid4()).replace('-', '').upper()
        package = f"prepay_id={prepay_id}"
        
        print(f"🔧 生成支付参数:")
        print(f"   timestamp: {timestamp}")
        print(f"   nonce_str: {nonce_str}")
        print(f"   package: {package}")
        
        # 计算签名
        sign_params = [
            settings.WECHAT_PAY_APPID,
            timestamp,
            nonce_str,
            package
        ]
        
        try:
            pay_sign = self.wxpay.sign(sign_params)
            print(f"   pay_sign: {pay_sign[:50]}...")
        except Exception as e:
            print(f"❌ 签名生成失败: {e}")
            pay_sign = "SIGN_ERROR"

        result = {
            "appId": settings.WECHAT_PAY_APPID,
            "timeStamp": timestamp,
            "nonceStr": nonce_str,
            "package": package,
            "signType": "RSA",
            "paySign": pay_sign
        }
        
        print(f"✅ 最终支付参数: {result}")
        return result
    
    async def handle_pay_notify(self, headers, body):
        """处理支付结果通知"""
        # 验证并解密回调数据
        result = self.wxpay.callback(headers, body)
        
        if result and result.get('event_type') == 'TRANSACTION.SUCCESS':
            resource = result.get('resource')
            out_trade_no = resource.get('out_trade_no')
            transaction_id = resource.get('transaction_id')
            trade_state = resource.get('trade_state')
            
            # 更新订单状态
            order = await Order.get(order_id=out_trade_no)
            
            if trade_state == 'SUCCESS':
                order.status = Order.STATUS_PAID
                order.wechat_transaction_id = transaction_id
                order.wechat_bank_type = resource.get('bank_type')
                order.wechat_cash_fee = resource.get('amount', {}).get('total')
                order.pay_time = datetime.now()
                order.wechat_extra_data = resource
                await order.save()
                
                # 增加用户时长
                await self._add_user_duration(order)

                # 处理优惠券使用记录
                await self._handle_coupon_usage(order)

                return True
            else:
                order.status = Order.STATUS_FAILED
                order.wechat_extra_data = resource
                await order.save()
                
        return False

    async def _calculate_price(self, package: PaymentPackage, coupon_code: str = None, user: AIReliefUser = None):
        """计算最终价格（处理优惠券逻辑）"""
        final_amount = package.price
        discount_amount = Decimal('0')
        coupon = None

        if coupon_code:
            # 验证优惠券有效性
            is_valid, message, coupon = await self._validate_coupon_for_payment(coupon_code, user)

            if is_valid and coupon:
                if coupon.type == "discount":
                    # 折扣券：直接减免金额
                    discount_amount = min(coupon.value, package.price)
                    final_amount = max(Decimal('0.01'), package.price - discount_amount)  # 最低0.01元
                elif coupon.type == "duration":
                    # 时长券：不能用于支付，应该直接兑换时长
                    raise Exception("时长券不能用于支付，请直接使用兑换功能")
            else:
                raise Exception(f"优惠券无效: {message}")

        return final_amount, discount_amount, coupon

    async def _validate_coupon_for_payment(self, coupon_code: str, user: AIReliefUser):
        """验证优惠券是否可用于支付"""
        try:
            coupon = await Coupon.get(code=coupon_code)
        except:
            return False, "优惠券不存在", None

        # 检查是否激活
        if not coupon.is_active:
            return False, "优惠券已被禁用", coupon

        # 检查有效期
        now = datetime.now()
        
        # 统一处理时区，将所有datetime转换为naive datetime进行比较
        valid_from = coupon.valid_from.replace(tzinfo=None) if coupon.valid_from.tzinfo else coupon.valid_from
        valid_until = coupon.valid_until.replace(tzinfo=None) if coupon.valid_until.tzinfo else coupon.valid_until
        
        if now < valid_from:
            return False, "优惠券尚未生效", coupon

        if now > valid_until:
            return False, "优惠券已过期", coupon

        # 检查使用次数
        if coupon.used_count >= coupon.max_uses:
            return False, "优惠券已达到最大使用次数", coupon

        # 检查用户是否已经使用过这个优惠券
        if user:
            existing_usage = await CouponUsage.filter(
                coupon=coupon,
                user=user
            ).exists()

            if existing_usage:
                return False, "您已经使用过这个优惠券", coupon

        # 检查优惠券类型是否适用于支付
        if coupon.type not in ["discount"]:
            return False, f"{coupon.type}类型的优惠券不能用于支付", coupon

        return True, "优惠券有效", coupon

    async def _handle_coupon_usage(self, order: Order):
        """处理优惠券使用记录"""
        if not order.coupon_code:
            return

        try:
            # 获取优惠券
            coupon = await Coupon.get(code=order.coupon_code)
            user = await order.user

            # 创建使用记录
            await CouponUsage.create(
                coupon=coupon,
                user=user,
                order=order
            )

            # 更新优惠券使用次数
            coupon.used_count += 1
            await coupon.save()

        except Exception as e:
            # 记录错误但不影响主流程
            print(f"处理优惠券使用记录失败: {str(e)}")

    def _generate_order_id(self):
        """生成订单号"""
        timestamp = str(int(time.time()))
        random_str = str(uuid.uuid4()).replace('-', '')[:8].upper()
        return f"AIR{timestamp}{random_str}"

    async def _add_user_duration(self, order: Order):
        """增加用户时长"""
        user = await order.user
        package = await order.package

        # 更新总陪伴时长（累计购买的时长）
        user.total_duration += package.duration_seconds

        # 更新会员到期时间（实时剩余时长基于此计算）
        now = datetime.now()
        
        # 统一处理时区，将expiry_date转换为naive datetime进行比较
        if user.expiry_date:
            expiry_date = user.expiry_date.replace(tzinfo=None) if user.expiry_date.tzinfo else user.expiry_date
            if expiry_date > now:
                # 如果当前会员未过期，在现有到期时间基础上延长
                user.expiry_date = expiry_date + timedelta(seconds=package.duration_seconds)
            else:
                # 如果当前会员已过期，从现在开始计算
                user.expiry_date = now + timedelta(seconds=package.duration_seconds)
        else:
            # 如果没有到期时间，从现在开始计算
            user.expiry_date = now + timedelta(seconds=package.duration_seconds)

        # 如果是新用户，标记为老用户
        if user.is_new_user:
            user.is_new_user = False

        await user.save()