"""
AI-Relief 业务数据模型
基于现有 vue-fastapi-admin 框架扩展
"""
import uuid
from datetime import datetime
from tortoise import fields
from .base import BaseModel, TimestampMixin


def generate_short_user_id() -> str:
    """
    生成基于UUID的短用户ID

    使用UUID前缀截取方案，性能最优（211,250个/秒）
    生成10位十六进制字符串，保证唯一性

    Returns:
        10位十六进制短用户ID，如：A7F2C8E1B3
    """
    return str(uuid.uuid4()).replace('-', '')[:10].upper()


class AIReliefUser(BaseModel, TimestampMixin):
    """AI-Relief 用户信息表 - 整合微信用户功能"""

    # 用户唯一标识ID（10位十六进制短ID）
    user_id = fields.CharField(max_length=16, default=generate_short_user_id, unique=True, description="用户唯一ID", index=True)

    # 关联原有管理员用户表（可选，用于管理后台访问）
    admin_user = fields.ForeignKeyField("models.User", null=True, description="关联管理员用户", on_delete=fields.SET_NULL)

    # 微信相关字段
    wechat_openid = fields.CharField(max_length=64, unique=True, description="微信OpenID", index=True)
    wechat_unionid = fields.CharField(max_length=64, null=True, description="微信UnionID", index=True)

    # 微信授权相关字段
    wechat_access_token = fields.TextField(null=True, description="微信访问令牌")
    wechat_refresh_token = fields.TextField(null=True, description="微信刷新令牌")
    wechat_token_expires_in = fields.IntField(default=0, description="access_token有效期(秒)")
    wechat_token_expires_at = fields.DatetimeField(null=True, description="令牌过期时间")

    # 微信用户详细信息
    wechat_province = fields.CharField(max_length=50, null=True, description="省份")
    wechat_city = fields.CharField(max_length=50, null=True, description="城市")
    wechat_country = fields.CharField(max_length=50, null=True, description="国家")
    wechat_privilege = fields.JSONField(default=list, description="用户特权信息")
    is_wechat_subscribed = fields.BooleanField(default=False, description="是否关注公众号")
    wechat_subscribe_time = fields.DatetimeField(null=True, description="关注时间")
    wechat_unsubscribe_time = fields.DatetimeField(null=True, description="取消关注时间")

    # 用户基本信息
    nickname = fields.CharField(max_length=100, description="用户昵称", index=True)
    avatar = fields.TextField(null=True, description="头像URL")
    phone = fields.CharField(max_length=20, null=True, description="手机号", index=True)
    # 统一性别字段：0=未知, 1=女性, 2=男性
    gender = fields.IntField(default=0, description="性别：0=未知, 1=女性, 2=男性")
    birthday = fields.DateField(null=True, description="生日")

    # AI-Relief 特有字段
    is_new_user = fields.BooleanField(default=True, description="是否新用户", index=True)
    duration = fields.IntField(default=0, description="剩余时长(秒)")
    total_duration = fields.IntField(default=0, description="总购买时长(秒)")
    expiry_date = fields.DatetimeField(null=True, description="会员到期时间", index=True)
    ip_location = fields.CharField(max_length=100, null=True, description="IP属地")

    # 状态字段
    is_active = fields.BooleanField(default=True, description="是否激活", index=True)
    last_login = fields.DatetimeField(null=True, description="最后登录时间", index=True)

    @property
    def remaining_duration(self) -> int:
        """
        实时计算剩余时长（秒）
        基于会员到期时间计算，实现时长随现实时间流逝

        Returns:
            int: 剩余时长（秒），如果已过期则返回0
        """
        if not self.expiry_date:
            return 0

        now = datetime.now()
        expiry_date = self.expiry_date

        # 处理时区问题：如果expiry_date有时区信息，移除时区信息进行比较
        if expiry_date.tzinfo is not None:
            expiry_date = expiry_date.replace(tzinfo=None)

        if now >= expiry_date:
            return 0

        # 计算剩余秒数
        time_diff = expiry_date - now
        remaining_seconds = int(time_diff.total_seconds())

        return max(0, remaining_seconds)

    @property
    def is_membership_active(self) -> bool:
        """
        检查会员是否有效

        Returns:
            bool: 会员是否有效
        """
        return self.remaining_duration > 0

    @property
    def consumed_duration(self) -> int:
        """
        计算已陪伴时长（基于时间流逝）
        已陪伴时长 = 总购买时长 - 剩余时长

        Returns:
            int: 已陪伴时长（秒）
        """
        return max(0, self.total_duration - self.remaining_duration)

    @property
    def companion_days(self) -> int:
        """
        计算已陪伴天数
        根据已陪伴时长计算，>0显示1天，>24小时显示2天，以此类推

        Returns:
            int: 已陪伴天数
        """
        consumed = self.consumed_duration
        if consumed <= 0:
            return 0

        # 按24小时为一天，向上取整
        return max(1, (consumed + 24 * 60 * 60 - 1) // (24 * 60 * 60))

    class Meta:
        table = "airelief_user"
        table_description = "AI-Relief用户信息表"


class PaymentPackage(BaseModel, TimestampMixin):
    """支付套餐表"""
    
    name = fields.CharField(max_length=50, description="套餐名称", index=True)
    duration = fields.CharField(max_length=20, description="时长描述，如：30天")
    duration_seconds = fields.IntField(description="时长秒数")
    price = fields.DecimalField(max_digits=10, decimal_places=2, description="价格")
    original_price = fields.DecimalField(max_digits=10, decimal_places=2, null=True, description="原价")
    tag = fields.CharField(max_length=20, null=True, description="标签，如：推荐、热门")
    is_active = fields.BooleanField(default=True, description="是否激活", index=True)
    sort_order = fields.IntField(default=0, description="排序", index=True)

    class Meta:
        table = "payment_package"
        table_description = "支付套餐表"


class Order(BaseModel, TimestampMixin):
    """订单表 - 整合微信支付功能"""

    order_id = fields.CharField(max_length=64, unique=True, description="订单号", index=True)
    user = fields.ForeignKeyField("models.AIReliefUser", description="用户", on_delete=fields.CASCADE)
    package = fields.ForeignKeyField("models.PaymentPackage", description="套餐", on_delete=fields.RESTRICT)
    package_name = fields.CharField(max_length=50, description="套餐名称")
    amount = fields.DecimalField(max_digits=10, decimal_places=2, description="支付金额")
    original_amount = fields.DecimalField(max_digits=10, decimal_places=2, description="原价")
    coupon_code = fields.CharField(max_length=50, null=True, description="兑换码", index=True)
    discount_amount = fields.DecimalField(max_digits=10, decimal_places=2, default=0, description="折扣金额")

    # 订单状态：pending(待支付), paid(已支付), failed(失败), cancelled(取消), refunded(已退款), expired(已过期)
    status = fields.CharField(max_length=20, default="pending", description="订单状态", index=True)
    payment_method = fields.CharField(max_length=20, default="wechat", description="支付方式")

    # 微信支付相关字段
    wechat_prepay_id = fields.CharField(max_length=64, null=True, description="微信预支付ID")
    wechat_transaction_id = fields.CharField(max_length=64, null=True, description="微信交易号", index=True)
    wechat_trade_type = fields.CharField(max_length=20, default="JSAPI", description="交易类型")
    wechat_bank_type = fields.CharField(max_length=50, null=True, description="付款银行")
    wechat_cash_fee = fields.IntField(null=True, description="现金支付金额(分)")
    wechat_notify_url = fields.CharField(max_length=500, null=True, description="支付结果通知地址")
    wechat_extra_data = fields.JSONField(default=dict, description="微信支付额外数据")
    pay_time = fields.DatetimeField(null=True, description="支付时间", index=True)

    # 订单状态常量
    STATUS_PENDING = "pending"      # 待支付
    STATUS_PAID = "paid"           # 已支付
    STATUS_FAILED = "failed"       # 支付失败
    STATUS_CANCELLED = "cancelled" # 已取消
    STATUS_REFUNDED = "refunded"   # 已退款
    STATUS_EXPIRED = "expired"     # 已过期

    class Meta:
        table = "order"
        table_description = "订单表"


class ChatSession(BaseModel, TimestampMixin):
    """聊天会话表"""
    
    session_id = fields.CharField(max_length=64, unique=True, description="会话ID", index=True)
    user = fields.ForeignKeyField("models.AIReliefUser", description="用户", on_delete=fields.CASCADE)
    title = fields.CharField(max_length=200, null=True, description="会话标题")
    message_count = fields.IntField(default=0, description="消息数量")
    duration_consumed = fields.IntField(default=0, description="消耗时长(秒)")
    
    # 会话状态：active(活跃), ended(结束)
    status = fields.CharField(max_length=20, default="active", description="会话状态", index=True)
    ended_at = fields.DatetimeField(null=True, description="结束时间")

    class Meta:
        table = "chat_session"
        table_description = "聊天会话表"


class ChatMessage(BaseModel, TimestampMixin):
    """聊天消息表"""

    message_id = fields.CharField(max_length=64, unique=True, description="消息ID", index=True)
    session = fields.ForeignKeyField("models.ChatSession", description="会话", on_delete=fields.CASCADE)
    user = fields.ForeignKeyField("models.AIReliefUser", description="用户", on_delete=fields.CASCADE)
    content = fields.TextField(description="消息内容")

    # 消息类型：text(文本), audio(语音)
    message_type = fields.CharField(max_length=10, description="消息类型", index=True)

    # 角色：user(用户), assistant(AI助手)
    role = fields.CharField(max_length=20, description="角色", index=True)

    # 语音消息相关
    audio_url = fields.TextField(null=True, description="语音URL")
    audio_duration = fields.IntField(null=True, description="语音时长(秒)")
    audio_file_size = fields.IntField(null=True, description="语音文件大小(字节)")

    # 语音识别相关
    transcription_text = fields.TextField(null=True, description="语音识别文本")
    transcription_confidence = fields.FloatField(null=True, description="识别置信度")
    transcription_status = fields.CharField(max_length=20, default="pending", description="识别状态: pending/success/failed")
    transcription_error = fields.TextField(null=True, description="识别错误信息")

    # 外部AI服务相关
    external_session_id = fields.CharField(max_length=128, null=True, description="外部AI会话ID")
    external_message_id = fields.CharField(max_length=128, null=True, description="外部AI消息ID")

    # AI 相关
    tokens_used = fields.IntField(default=0, description="使用的tokens")
    processing_time = fields.FloatField(null=True, description="处理耗时(秒)")

    class Meta:
        table = "chat_message"
        table_description = "聊天消息表"


class Coupon(BaseModel, TimestampMixin):
    """兑换码表"""
    
    code = fields.CharField(max_length=50, unique=True, description="兑换码", index=True)
    
    # 兑换码类型：discount(折扣), duration(时长)
    type = fields.CharField(max_length=20, default="discount", description="类型", index=True)
    value = fields.DecimalField(max_digits=10, decimal_places=2, description="优惠金额")
    max_uses = fields.IntField(default=1, description="最大使用次数")
    used_count = fields.IntField(default=0, description="已使用次数")
    valid_from = fields.DatetimeField(description="有效期开始", index=True)
    valid_until = fields.DatetimeField(description="有效期结束", index=True)
    is_active = fields.BooleanField(default=True, description="是否激活", index=True)
    
    # 创建者（管理员）
    created_by = fields.ForeignKeyField("models.User", null=True, description="创建者", on_delete=fields.SET_NULL)

    class Meta:
        table = "coupon"
        table_description = "兑换码表"


class CouponUsage(BaseModel, TimestampMixin):
    """兑换码使用记录表"""
    
    coupon = fields.ForeignKeyField("models.Coupon", description="兑换码", on_delete=fields.CASCADE)
    user = fields.ForeignKeyField("models.AIReliefUser", description="用户", on_delete=fields.CASCADE)
    order = fields.ForeignKeyField("models.Order", null=True, description="关联订单", on_delete=fields.SET_NULL)
    used_at = fields.DatetimeField(auto_now_add=True, description="使用时间", index=True)

    class Meta:
        table = "coupon_usage"
        table_description = "兑换码使用记录表"


class WeChatAuthLog(BaseModel, TimestampMixin):
    """微信授权日志表"""

    user = fields.ForeignKeyField("models.AIReliefUser", null=True, description="关联用户", on_delete=fields.SET_NULL)
    wechat_openid = fields.CharField(max_length=64, description="用户openid", index=True)
    auth_type = fields.CharField(max_length=20, description="授权类型", index=True)
    auth_scope = fields.CharField(max_length=50, description="授权范围")
    ip_address = fields.CharField(max_length=45, null=True, description="IP地址")
    user_agent = fields.TextField(null=True, description="用户代理")
    success = fields.BooleanField(default=True, description="是否成功", index=True)
    error_message = fields.TextField(null=True, description="错误信息")

    class Meta:
        table = "wechat_auth_log"
        table_description = "微信授权日志表"


class SystemConfig(BaseModel, TimestampMixin):
    """系统配置表"""

    key = fields.CharField(max_length=100, unique=True, description="配置键", index=True)
    value = fields.TextField(description="配置值")
    description = fields.CharField(max_length=200, null=True, description="配置描述")
    is_active = fields.BooleanField(default=True, description="是否激活")

    class Meta:
        table = "system_config"
        table_description = "系统配置表"


class Agreement(BaseModel, TimestampMixin):
    """协议管理表"""

    # 协议类型：user_agreement(用户协议), privacy_policy(隐私政策), payment_agreement(充值协议), disclaimer(免责声明)
    type = fields.CharField(max_length=50, description="协议类型", index=True)
    title = fields.CharField(max_length=200, description="协议标题")
    content = fields.TextField(description="协议内容")
    version = fields.CharField(max_length=20, default="1.0", description="版本号")

    # 协议状态：active(生效中), draft(草稿), deprecated(已废弃)
    status = fields.CharField(max_length=20, default="draft", description="协议状态", index=True)

    # 生效时间
    effective_date = fields.DatetimeField(null=True, description="生效时间")

    # 创建者（管理员）
    created_by = fields.ForeignKeyField("models.User", null=True, description="创建者", on_delete=fields.SET_NULL)

    # 协议状态常量
    STATUS_ACTIVE = "active"        # 生效中
    STATUS_DRAFT = "draft"          # 草稿
    STATUS_DEPRECATED = "deprecated" # 已废弃

    # 协议类型常量
    TYPE_USER_AGREEMENT = "user_agreement"      # 用户协议
    TYPE_PRIVACY_POLICY = "privacy_policy"      # 隐私政策
    TYPE_PAYMENT_AGREEMENT = "payment_agreement" # 充值协议
    TYPE_DISCLAIMER = "disclaimer"              # 免责声明

    class Meta:
        table = "agreement"
        table_description = "协议管理表"
