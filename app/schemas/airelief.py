from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


# ===== 认证相关 Schema =====

class AuthUrlResponse(BaseModel):
    """微信授权URL响应"""
    auth_url: str = Field(..., description="微信授权URL")
    scope: str = Field(..., description="授权作用域")
    state: Optional[str] = Field(None, description="状态参数")


class WeChatLoginRequest(BaseModel):
    """微信登录请求"""
    code: str = Field(..., description="微信授权码")


class CallbackResponse(BaseModel):
    """授权回调响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    user_id: Optional[int] = Field(None, description="用户ID")
    openid: Optional[str] = Field(None, description="用户openid")


# ===== 用户相关 Schema =====

class AIReliefUserInfo(BaseModel):
    """AI-Relief用户信息 - 使用驼峰命名匹配前端"""
    id: int = Field(..., description="用户数据库ID")
    user_id: str = Field(..., description="用户唯一标识ID")
    wechat_openid: str = Field(..., description="微信OpenID")
    wechat_unionid: Optional[str] = Field(None, description="微信UnionID")
    nickname: str = Field(..., description="用户昵称")
    avatar: Optional[str] = Field(None, description="头像URL")
    phone: Optional[str] = Field(None, description="手机号")
    gender: int = Field(0, description="性别：0=未知, 1=女性, 2=男性")
    birthday: Optional[str] = Field(None, description="生日")

    # 微信地理信息
    wechat_province: Optional[str] = Field(None, description="省份")
    wechat_city: Optional[str] = Field(None, description="城市")
    wechat_country: Optional[str] = Field(None, description="国家")
    wechat_privilege: List[str] = Field(default_factory=list, description="用户特权信息")
    is_wechat_subscribed: bool = Field(False, description="是否关注公众号")

    # AI-Relief 特有字段（使用驼峰命名匹配前端）
    isNewUser: bool = Field(True, description="是否新用户")
    duration: int = Field(0, description="剩余时长(秒)")
    totalDuration: int = Field(0, description="总购买时长(秒)")
    consumedDuration: int = Field(0, description="已陪伴时长(秒)")
    companionDays: int = Field(0, description="已陪伴天数")
    expiryDate: Optional[str] = Field(None, description="会员到期时间")
    ipLocation: Optional[str] = Field(None, description="IP属地")

    # 状态字段
    is_active: bool = Field(True, description="是否激活")
    last_login: Optional[str] = Field(None, description="最后登录时间")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")

    class Config:
        # 启用JSON编码器来处理日期时间
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class UserUpdateRequest(BaseModel):
    """用户信息更新请求"""
    nickname: Optional[str] = Field(None, max_length=100, description="用户昵称")
    avatar: Optional[str] = Field(None, description="头像URL")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    gender: Optional[int] = Field(None, ge=0, le=2, description="性别：0=未知, 1=女性, 2=男性")
    birthday: Optional[str] = Field(None, description="生日（YYYY-MM-DD格式）")


# ===== 支付相关 Schema =====

class PaymentPackageInfo(BaseModel):
    """支付套餐信息"""
    id: Optional[int] = Field(None, description="套餐ID")
    name: str = Field(..., description="套餐名称")
    duration: str = Field(..., description="时长描述")
    duration_seconds: int = Field(..., description="时长秒数")
    price: float = Field(..., description="价格")
    original_price: Optional[float] = Field(None, description="原价")
    tag: Optional[str] = Field(None, description="标签")
    is_active: bool = Field(True, description="是否激活")
    sort_order: int = Field(0, description="排序")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")

    class Config:
        from_attributes = True


class CreateOrderRequest(BaseModel):
    """创建订单请求"""
    user_id: int = Field(..., description="用户ID")
    package_id: int = Field(..., description="套餐ID")
    coupon_code: Optional[str] = Field(None, description="兑换码")
    notify_url: Optional[str] = Field(None, description="支付结果通知地址")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="额外数据")


class CreateOrderResponse(BaseModel):
    """创建订单响应"""
    order_id: str = Field(..., description="商户订单号")
    prepay_id: str = Field(..., description="预支付交易会话标识")
    jsapi_params: Dict[str, Any] = Field(..., description="JSAPI支付参数")
    amount: float = Field(..., description="支付金额（元）")
    original_amount: float = Field(..., description="原价（元）")
    discount_amount: float = Field(0, description="折扣金额（元）")
    coupon_code: Optional[str] = Field(None, description="使用的兑换码")
    package_name: str = Field(..., description="套餐名称")


class JSAPIPayParams(BaseModel):
    """JSAPI支付参数"""
    appId: str = Field(..., description="公众号ID")
    timeStamp: str = Field(..., description="时间戳")
    nonceStr: str = Field(..., description="随机字符串")
    package: str = Field(..., description="订单详情扩展字符串")
    signType: str = Field(..., description="签名方式")
    paySign: str = Field(..., description="签名")


class OrderStatusResponse(BaseModel):
    """订单状态响应"""
    id: int = Field(..., description="订单ID")
    order_id: str = Field(..., description="商户订单号")
    wechat_transaction_id: Optional[str] = Field(None, description="微信支付订单号")
    status: str = Field(..., description="订单状态")
    amount: float = Field(..., description="订单金额（元）")
    original_amount: float = Field(..., description="原价（元）")
    discount_amount: float = Field(0, description="折扣金额（元）")
    coupon_code: Optional[str] = Field(None, description="使用的兑换码")
    package_name: str = Field(..., description="套餐名称")
    payment_method: str = Field(..., description="支付方式")
    pay_time: Optional[datetime] = Field(None, description="支付时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class PaymentNotifyRequest(BaseModel):
    """支付通知请求"""
    return_code: str = Field(..., description="返回状态码")
    return_msg: str = Field(..., description="返回信息")
    result_code: Optional[str] = Field(None, description="业务结果")
    err_code: Optional[str] = Field(None, description="错误代码")
    err_code_des: Optional[str] = Field(None, description="错误代码描述")
    openid: Optional[str] = Field(None, description="用户openid")
    trade_type: Optional[str] = Field(None, description="交易类型")
    bank_type: Optional[str] = Field(None, description="付款银行")
    total_fee: Optional[int] = Field(None, description="订单金额")
    cash_fee: Optional[int] = Field(None, description="现金支付金额")
    transaction_id: Optional[str] = Field(None, description="微信支付订单号")
    out_trade_no: Optional[str] = Field(None, description="商户订单号")
    time_end: Optional[str] = Field(None, description="支付完成时间")


class RefundRequest(BaseModel):
    """退款请求"""
    refund_amount: Optional[float] = Field(None, description="退款金额（元），不填则全额退款")
    refund_reason: str = Field(..., description="退款原因")


class RefundResponse(BaseModel):
    """退款响应"""
    message: str = Field(..., description="响应消息")
    order_id: str = Field(..., description="商户订单号")
    refund_amount: float = Field(..., description="退款金额（元）")
    refund_reason: str = Field(..., description="退款原因")


# ===== 聊天相关 Schema =====

class ChatSessionInfo(BaseModel):
    """聊天会话信息"""
    id: int = Field(..., description="会话ID")
    session_id: str = Field(..., description="会话ID")
    title: Optional[str] = Field(None, description="会话标题")
    message_count: int = Field(0, description="消息数量")
    duration_consumed: int = Field(0, description="消耗时长(秒)")
    status: str = Field(..., description="会话状态")
    ended_at: Optional[datetime] = Field(None, description="结束时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class ChatMessageInfo(BaseModel):
    """聊天消息信息"""
    id: int = Field(..., description="消息ID")
    message_id: str = Field(..., description="消息ID")
    content: str = Field(..., description="消息内容")
    message_type: str = Field(..., description="消息类型")
    role: str = Field(..., description="角色")
    audio_url: Optional[str] = Field(None, description="语音URL")
    audio_duration: Optional[int] = Field(None, description="语音时长(秒)")
    tokens_used: int = Field(0, description="使用的tokens")
    created_at: datetime = Field(..., description="创建时间")


# ===== 兑换码相关 Schema =====

class CouponInfo(BaseModel):
    """兑换码信息"""
    id: int = Field(..., description="兑换码ID")
    code: str = Field(..., description="兑换码")
    type: str = Field("discount", description="类型")
    value: float = Field(..., description="优惠金额")
    max_uses: int = Field(1, description="最大使用次数")
    used_count: int = Field(0, description="已使用次数")
    valid_from: datetime = Field(..., description="有效期开始")
    valid_until: datetime = Field(..., description="有效期结束")
    is_active: bool = Field(True, description="是否激活")
    created_by: Optional[int] = Field(None, description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class CreateCouponRequest(BaseModel):
    """创建兑换码请求"""
    type: str = Field("discount", description="类型：discount(折扣), duration(时长)")
    value: float = Field(..., description="优惠金额或时长值")
    max_uses: int = Field(1, description="最大使用次数")
    valid_from: datetime = Field(..., description="有效期开始")
    valid_until: datetime = Field(..., description="有效期结束")
    
    # 兑换码生成选项
    code: Optional[str] = Field(None, description="自定义兑换码（不填则自动生成）")
    code_length: int = Field(8, description="生成兑换码长度")
    code_prefix: Optional[str] = Field(None, description="兑换码前缀")
    use_separator: bool = Field(True, description="是否使用分隔符")


class BatchCreateCouponRequest(BaseModel):
    """批量创建兑换码请求"""
    count: int = Field(..., ge=1, le=1000, description="生成数量（1-1000）")
    type: str = Field("discount", description="类型：discount(折扣), duration(时长)")
    value: float = Field(..., description="优惠金额或时长值")
    max_uses: int = Field(1, description="最大使用次数")
    valid_from: datetime = Field(..., description="有效期开始")
    valid_until: datetime = Field(..., description="有效期结束")
    
    # 兑换码生成选项
    code_length: int = Field(8, description="生成兑换码长度")
    code_prefix: Optional[str] = Field(None, description="兑换码前缀")
    use_separator: bool = Field(True, description="是否使用分隔符")


class UpdateCouponRequest(BaseModel):
    """更新兑换码请求"""
    id: int = Field(..., description="兑换码ID")
    type: Optional[str] = Field(None, description="类型")
    value: Optional[float] = Field(None, description="优惠金额")
    max_uses: Optional[int] = Field(None, description="最大使用次数")
    valid_from: Optional[datetime] = Field(None, description="有效期开始")
    valid_until: Optional[datetime] = Field(None, description="有效期结束")
    is_active: Optional[bool] = Field(None, description="是否激活")


class CouponListRequest(BaseModel):
    """兑换码列表查询请求"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    type: Optional[str] = Field(None, description="类型筛选")
    is_active: Optional[bool] = Field(None, description="状态筛选")
    search: Optional[str] = Field(None, description="搜索关键词（兑换码）")
    created_by: Optional[int] = Field(None, description="创建者筛选")


class CouponListResponse(BaseModel):
    """兑换码列表响应"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    items: List[CouponInfo] = Field(..., description="兑换码列表")


class ValidateCouponRequest(BaseModel):
    """验证兑换码请求"""
    code: str = Field(..., description="兑换码")


class ValidateCouponResponse(BaseModel):
    """验证兑换码响应"""
    valid: bool = Field(..., description="是否有效")
    message: str = Field(..., description="响应消息")
    discount: Optional[float] = Field(None, description="优惠金额")
    expiry: Optional[str] = Field(None, description="有效期")
    coupon_info: Optional[CouponInfo] = Field(None, description="兑换码信息")


class UseCouponRequest(BaseModel):
    """使用兑换码请求"""
    code: str = Field(..., description="兑换码")
    user_id: int = Field(..., description="用户ID")


class UseCouponResponse(BaseModel):
    """使用兑换码响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    coupon_info: Optional[CouponInfo] = Field(None, description="兑换码信息")


class CouponUsageInfo(BaseModel):
    """兑换码使用记录信息"""
    id: int = Field(..., description="记录ID")
    coupon_code: str = Field(..., description="兑换码")
    user_id: str = Field(..., description="用户ID")
    user_nickname: str = Field(..., description="用户昵称")
    order_id: Optional[str] = Field(None, description="关联订单号")
    used_at: datetime = Field(..., description="使用时间")


# ===== 协议管理相关 Schema =====

class AgreementInfo(BaseModel):
    """协议信息"""
    id: int = Field(..., description="协议ID")
    type: str = Field(..., description="协议类型")
    title: str = Field(..., description="协议标题")
    content: str = Field(..., description="协议内容")
    version: str = Field(..., description="版本号")
    status: str = Field(..., description="协议状态")
    effective_date: Optional[datetime] = Field(None, description="生效时间")
    created_by: Optional[int] = Field(None, description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class CreateAgreementRequest(BaseModel):
    """创建协议请求"""
    type: str = Field(..., description="协议类型：user_agreement, privacy_policy, payment_agreement, disclaimer")
    title: str = Field(..., max_length=200, description="协议标题")
    content: str = Field(..., description="协议内容")
    version: str = Field("1.0", max_length=20, description="版本号")
    status: str = Field("draft", description="协议状态：active, draft, deprecated")
    effective_date: Optional[datetime] = Field(None, description="生效时间")


class UpdateAgreementRequest(BaseModel):
    """更新协议请求"""
    id: int = Field(..., description="协议ID")
    type: Optional[str] = Field(None, description="协议类型")
    title: Optional[str] = Field(None, max_length=200, description="协议标题")
    content: Optional[str] = Field(None, description="协议内容")
    version: Optional[str] = Field(None, max_length=20, description="版本号")
    status: Optional[str] = Field(None, description="协议状态")
    effective_date: Optional[datetime] = Field(None, description="生效时间")


class AgreementListRequest(BaseModel):
    """协议列表查询请求"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    type: Optional[str] = Field(None, description="协议类型筛选")
    status: Optional[str] = Field(None, description="状态筛选")
    search: Optional[str] = Field(None, description="搜索关键词（标题）")


class AgreementListResponse(BaseModel):
    """协议列表响应"""
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    items: List[AgreementInfo] = Field(..., description="协议列表")


class PublicAgreementResponse(BaseModel):
    """公开协议响应（用于前端展示）"""
    type: str = Field(..., description="协议类型")
    title: str = Field(..., description="协议标题")
    content: str = Field(..., description="协议内容")
    version: str = Field(..., description="版本号")
    effective_date: Optional[datetime] = Field(None, description="生效时间")
    updated_at: datetime = Field(..., description="更新时间")



