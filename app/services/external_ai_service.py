"""
外部AI服务集成
与 airelief.cn:8997 的AI代理服务进行交互
基于新的简化API接口实现
"""
import httpx
import logging
from typing import Dict, Any, Optional
from app.settings.config import settings

logger = logging.getLogger(__name__)


class ExternalAIService:
    """外部AI服务客户端"""

    def __init__(self):
        self.enabled = getattr(settings, 'EXTERNAL_AI_ENABLED', True)
        self.base_url = "https://www.airelief.cn:8997/agent"
        self.api_key = getattr(settings, 'EXTERNAL_AI_API_KEY', 'test_api_key')
        self.default_agent_id = 4  # 默认ai代理id
        self.timeout = 30.0

        # HTTP客户端配置
        self.client_config = {
            "timeout": httpx.Timeout(self.timeout),
            "headers": {
                "Content-Type": "application/json",
                "X-API-Key": self.api_key
            }
        }

        if not self.enabled:
            logger.info("外部AI服务已禁用，将使用模拟响应")

    async def start_conversation(self, user_id: str, agent_id: Optional[int] = None) -> Dict[str, Any]:
        """开始对话 - 对应新API的 /external/start/ 接口"""
        # 如果外部AI服务被禁用，返回模拟响应
        if not self.enabled:
            logger.info(f"外部AI服务已禁用，返回模拟对话开始响应: user_id={user_id}")
            import time
            return {
                "success": True,
                "session_id": f"mock_session_{user_id}_{int(time.time())}",
                "welcome_message": "您好！我是AI助手，很高兴为您服务。（当前为模拟模式）",
                "agent_name": "AI助手",
                "is_new_user": False,
                "timestamp": int(time.time())
            }

        try:
            request_data = {
                "user_id": user_id,
                "agent_id": agent_id or self.default_agent_id
            }

            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    f"{self.base_url}/external/start/",
                    json=request_data
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        data = result.get("data", {})
                        logger.info(f"外部AI对话开始成功: {data.get('session_id')}")
                        return {
                            "success": True,
                            "session_id": data.get("session_id"),
                            "welcome_message": data.get("welcome_message"),
                            "agent_name": data.get("agent_name"),
                            "is_new_user": data.get("is_new_user", False),
                            "timestamp": result.get("timestamp")
                        }

                logger.error(f"开始外部AI对话失败: {response.text}")
                return {
                    "success": False,
                    "message": "开始对话失败"
                }

        except Exception as e:
            logger.error(f"开始外部AI对话异常: {e}")
            return {
                "success": False,
                "message": str(e)
            }
    
    async def create_session(self, user_id: str, session_id: str,
                           user_info: Optional[Dict] = None) -> Dict[str, Any]:
        """创建AI会话 - 兼容旧接口，内部调用新的start_conversation"""
        # 为了保持向后兼容，这里调用新的start_conversation方法
        result = await self.start_conversation(user_id)
        if result.get("success"):
            return {
                "success": True,
                "session_id": result.get("session_id"),
                "agent_info": {"name": result.get("agent_name")},
                "start_message": result.get("welcome_message")
            }
        return result

    async def send_message(self, user_id: str, session_id: str, message: str,
                          agent_id: Optional[int] = None) -> Dict[str, Any]:
        """发送消息给AI代理 - 对应新API的 /external/message/ 接口"""
        # 如果外部AI服务被禁用，返回模拟响应
        if not self.enabled:
            logger.info(f"外部AI服务已禁用，返回模拟消息响应: user_id={user_id}, message={message}")
            import time

            # 特殊处理结束消息
            if message.lower().strip() == 'q':
                return {
                    "success": True,
                    "content": "感谢您的使用，再见！（模拟模式）",
                    "session_ended": True,
                    "processing_time": 0.1,
                    "session_id": session_id,
                    "message_id": f"ai_mock_{int(time.time())}",
                    "timestamp": int(time.time())
                }

            # 普通消息的模拟响应
            mock_responses = [
                "我理解您的问题，这是一个模拟回复。",
                "感谢您的提问，当前为模拟模式。",
                "我正在为您处理这个问题（模拟响应）。",
                "这是一个模拟的AI回复，实际服务已暂时禁用。"
            ]
            import random
            response_content = random.choice(mock_responses)

            return {
                "success": True,
                "content": response_content,
                "session_ended": False,
                "processing_time": 0.5,
                "session_id": session_id,
                "message_id": f"ai_mock_{int(time.time())}",
                "timestamp": int(time.time())
            }

        try:
            request_data = {
                "user_id": user_id,
                "agent_id": agent_id or self.default_agent_id,
                "session_id": session_id,
                "message": message
            }

            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    f"{self.base_url}/external/message/",
                    json=request_data
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        data = result.get("data", {})
                        return {
                            "success": True,
                            "content": data.get("response", ""),
                            "session_ended": data.get("session_ended", False),
                            "processing_time": data.get("process_time", 0),
                            "session_id": session_id,
                            "message_id": f"ai_{int(result.get('timestamp', 0))}",
                            "timestamp": result.get("timestamp")
                        }

                logger.error(f"发送消息到外部AI失败: {response.text}")
                return {
                    "success": False,
                    "message": "AI服务暂时不可用，请稍后重试"
                }

        except httpx.TimeoutException:
            logger.error("外部AI服务请求超时")
            return {
                "success": False,
                "message": "AI响应超时，请重试"
            }
        except Exception as e:
            logger.error(f"发送消息到外部AI异常: {e}")
            return {
                "success": False,
                "message": "AI服务异常，请稍后重试"
            }

    async def get_welcome_message(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """获取AI欢迎消息 - 兼容旧接口，使用新的start_conversation"""
        try:
            # 使用新的开始对话接口
            result = await self.start_conversation(user_id)

            if result.get("success"):
                welcome_message = result.get("welcome_message")
                if welcome_message:
                    return {
                        "success": True,
                        "content": welcome_message,
                        "session_id": result.get("session_id"),
                        "processing_time": 0.1,
                        "message_id": f"welcome_{int(session_id.split('_')[-1])}"
                    }

            # 如果没有欢迎消息，返回默认消息
            return {
                "success": True,
                "content": "你好！我是Holmes，很高兴为你服务！",
                "session_id": session_id,
                "processing_time": 0.1,
                "message_id": f"welcome_{int(session_id.split('_')[-1])}"
            }

        except Exception as e:
            logger.error(f"获取AI欢迎消息失败: {e}")
            return {
                "success": True,
                "content": "你好！我是你的AI助手，很高兴为你服务！",
                "session_id": session_id,
                "processing_time": 0.1,
                "message_id": f"welcome_{int(session_id.split('_')[-1])}"
            }

    async def get_conversation_history(self, user_id: str, agent_id: Optional[int] = None,
                                     limit: int = 50) -> Dict[str, Any]:
        """获取对话历史 - 对应新API的 /external/history/ 接口"""
        # 如果外部AI服务被禁用，返回空历史
        if not self.enabled:
            logger.info(f"外部AI服务已禁用，返回空对话历史: user_id={user_id}")
            import time
            return {
                "success": True,
                "messages": [],
                "total_count": 0,
                "agent_name": "AI助手（模拟模式）",
                "timestamp": int(time.time())
            }

        try:
            request_data = {
                "user_id": user_id,
                "agent_id": agent_id or self.default_agent_id,
                "limit": limit
            }

            async with httpx.AsyncClient(**self.client_config) as client:
                response = await client.post(
                    f"{self.base_url}/external/history/",
                    json=request_data
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        data = result.get("data", {})
                        return {
                            "success": True,
                            "messages": data.get("messages", []),
                            "total_count": data.get("total_count", 0),
                            "agent_name": data.get("agent_name", "AI助手"),
                            "timestamp": result.get("timestamp")
                        }

                logger.error(f"获取对话历史失败: {response.text}")
                return {
                    "success": False,
                    "message": "获取对话历史失败"
                }

        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    # 兼容旧接口的方法
    async def get_session_history(self, user_id: str, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取用户会话历史 - 兼容旧接口"""
        result = await self.get_conversation_history(user_id, limit=page_size)
        if result.get("success"):
            return {
                "success": True,
                "data": {
                    "messages": result.get("messages", []),
                    "total": result.get("total_count", 0)
                }
            }
        return result

    async def get_message_history(self, user_id: str, agent_id: int = None,
                                page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取消息历史 - 兼容旧接口"""
        result = await self.get_conversation_history(user_id, agent_id, limit=page_size)
        if result.get("success"):
            return {
                "success": True,
                "data": {
                    "messages": result.get("messages", []),
                    "total": result.get("total_count", 0)
                }
            }
        return result

    async def end_session(self, user_id: str, session_id: str) -> Dict[str, Any]:
        """结束会话 - 发送'q'消息触发反思总结"""
        try:
            return await self.send_message(user_id, session_id, "q")
        except Exception as e:
            logger.error(f"结束会话失败: {e}")
            return {
                "success": False,
                "message": str(e)
            }

    # 测试连接方法 - 简化版本
    async def test_connection(self) -> Dict[str, Any]:
        """测试API连接 - 通过开始对话来测试连接"""
        try:
            result = await self.start_conversation("test_user_connection")
            if result.get("success"):
                return {
                    "success": True,
                    "message": "连接成功",
                    "data": {"agent_name": result.get("agent_name")}
                }
            else:
                return {
                    "success": False,
                    "message": result.get("message", "连接失败")
                }
        except Exception as e:
            logger.error(f"外部AI服务连接异常: {e}")
            return {
                "success": False,
                "message": f"连接异常: {str(e)}"
            }
