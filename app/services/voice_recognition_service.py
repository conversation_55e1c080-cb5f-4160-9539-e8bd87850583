"""
火山引擎语音识别服务
集成火山引擎ASR API实现语音转文字功能
支持流式语音识别和录音文件识别
"""
import base64
import gzip
import hmac
import json
import logging
import asyncio
import websockets
import time
import uuid
import wave
import hashlib
from hashlib import sha256
from io import Bytes<PERSON>
from typing import Dict, Any, Optional, Callable, AsyncGenerator
from urllib.parse import urlparse
import httpx
from app.settings.config import settings

logger = logging.getLogger(__name__)

# 火山引擎协议常量
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Type
CLIENT_FULL_REQUEST = 0b0001
CLIENT_AUDIO_ONLY_REQUEST = 0b0010
SERVER_FULL_RESPONSE = 0b1001
SERVER_ACK = 0b1011
SERVER_ERROR_RESPONSE = 0b1111

# Message Type Specific Flags
NO_SEQUENCE = 0b0000
POS_SEQUENCE = 0b0001
NEG_SEQUENCE = 0b0010
NEG_SEQUENCE_1 = 0b0011

# Message Serialization
NO_SERIALIZATION = 0b0000
JSON = 0b0001
THRIFT = 0b0011
CUSTOM_TYPE = 0b1111

# Message Compression
NO_COMPRESSION = 0b0000
GZIP = 0b0001
CUSTOM_COMPRESSION = 0b1111


def generate_header(
    version=PROTOCOL_VERSION,
    message_type=CLIENT_FULL_REQUEST,
    message_type_specific_flags=NO_SEQUENCE,
    serial_method=JSON,
    compression_type=GZIP,
    reserved_data=0x00,
    extension_header=bytes()
):
    """生成火山引擎协议头"""
    header = bytearray()
    header_size = int(len(extension_header) / 4) + 1
    header.append((version << 4) | header_size)
    header.append((message_type << 4) | message_type_specific_flags)
    header.append((serial_method << 4) | compression_type)
    header.append(reserved_data)
    header.extend(extension_header)
    return header


def generate_full_default_header():
    """生成完整请求头"""
    return generate_header()


def generate_audio_default_header():
    """生成音频数据头"""
    return generate_header(message_type=CLIENT_AUDIO_ONLY_REQUEST)


def generate_last_audio_default_header():
    """生成最后音频数据头"""
    return generate_header(
        message_type=CLIENT_AUDIO_ONLY_REQUEST,
        message_type_specific_flags=NEG_SEQUENCE
    )


def parse_response(res):
    """解析火山引擎响应"""
    protocol_version = res[0] >> 4
    header_size = res[0] & 0x0f
    message_type = res[1] >> 4
    message_type_specific_flags = res[1] & 0x0f
    serialization_method = res[2] >> 4
    message_compression = res[2] & 0x0f
    reserved = res[3]
    header_extensions = res[4:header_size * 4]
    payload = res[header_size * 4:]
    result = {}
    payload_msg = None
    payload_size = 0

    if message_type == SERVER_FULL_RESPONSE:
        payload_size = int.from_bytes(payload[:4], "big", signed=True)
        payload_msg = payload[4:]
    elif message_type == SERVER_ACK:
        seq = int.from_bytes(payload[:4], "big", signed=True)
        result['seq'] = seq
        if len(payload) >= 8:
            payload_size = int.from_bytes(payload[4:8], "big", signed=False)
            payload_msg = payload[8:]
    elif message_type == SERVER_ERROR_RESPONSE:
        code = int.from_bytes(payload[:4], "big", signed=False)
        result['code'] = code
        payload_size = int.from_bytes(payload[4:8], "big", signed=False)
        payload_msg = payload[8:]

    if payload_msg is None:
        return result

    if message_compression == GZIP:
        payload_msg = gzip.decompress(payload_msg)

    if serialization_method == JSON:
        payload_msg = json.loads(str(payload_msg, "utf-8"))
    elif serialization_method != NO_SERIALIZATION:
        payload_msg = str(payload_msg, "utf-8")

    result['payload_msg'] = payload_msg
    result['payload_size'] = payload_size
    return result


class VoiceRecognitionService:
    """语音识别服务 - 支持多种识别引擎和模式"""

    def __init__(self):
        # 语音识别引擎配置
        self.engine = getattr(settings, 'VOICE_RECOGNITION_ENGINE', 'volcano').lower()
        self.mode = getattr(settings, 'VOICE_RECOGNITION_MODE', 'hybrid').lower()
        self.fallback_enabled = getattr(settings, 'VOICE_RECOGNITION_FALLBACK', True)

        # 火山引擎配置
        self.app_id = getattr(settings, 'VOLCENGINE_APP_ID', '')
        self.access_key = getattr(settings, 'VOLCENGINE_ACCESS_KEY', '')
        self.secret_key = getattr(settings, 'VOLCENGINE_SECRET_KEY', '')
        self.cluster = getattr(settings, 'VOLCENGINE_CLUSTER', 'volcengine_streaming_common')

        # API配置
        self.base_url = "https://openspeech.bytedance.com/api/v1/asr"
        # 火山引擎流式语音识别WebSocket URL (v2)
        self.streaming_url = "wss://openspeech.bytedance.com/api/v2/asr"
        self.timeout = 30.0

        # 识别参数
        self.recognition_config = {
            "language": "zh-CN",  # 中文识别
            "format": "wav",      # 音频格式
            "sample_rate": 16000, # 采样率
            "encoding": "linear16", # 编码格式
            "enable_words": True,   # 启用词级别时间戳
            "enable_punctuation": True,  # 启用标点符号
            "max_alternatives": 1,  # 最大候选结果数
        }

        # 检查配置
        self.is_volcano_configured = bool(self.app_id and self.access_key and self.secret_key)

        # 添加调试日志
        print(f"🔧 火山引擎配置检查:")
        print(f"  - app_id: {'✅' if self.app_id else '❌'} ({self.app_id[:8]}...)" if self.app_id else "  - app_id: ❌ (空)")
        print(f"  - access_key: {'✅' if self.access_key else '❌'} ({self.access_key[:8]}...)" if self.access_key else "  - access_key: ❌ (空)")
        print(f"  - secret_key: {'✅' if self.secret_key else '❌'} ({self.secret_key[:8]}...)" if self.secret_key else "  - secret_key: ❌ (空)")
        print(f"  - cluster: {self.cluster}")
        print(f"  - is_volcano_configured: {self.is_volcano_configured}")

        logger.info(f"🔧 火山引擎配置检查:")
        logger.info(f"  - app_id: {'✅' if self.app_id else '❌'} ({self.app_id[:8]}...)" if self.app_id else "  - app_id: ❌ (空)")
        logger.info(f"  - access_key: {'✅' if self.access_key else '❌'} ({self.access_key[:8]}...)" if self.access_key else "  - access_key: ❌ (空)")
        logger.info(f"  - secret_key: {'✅' if self.secret_key else '❌'} ({self.secret_key[:8]}...)" if self.secret_key else "  - secret_key: ❌ (空)")
        logger.info(f"  - cluster: {self.cluster}")
        logger.info(f"  - is_volcano_configured: {self.is_volcano_configured}")

        # 根据引擎配置决定服务状态
        logger.info(f"🔧 语音识别配置: 引擎={self.engine}, 模式={self.mode}, 降级={self.fallback_enabled}")

        if self.engine == 'disabled':
            logger.info("语音识别功能已禁用")
        elif self.engine == 'volcano' and not self.is_volcano_configured:
            logger.warning("火山引擎语音识别服务未配置，将使用模拟服务")
        elif self.engine == 'wechat':
            logger.info("使用微信语音识别引擎（需要客户端支持）")
        else:
            logger.info(f"使用语音识别引擎: {self.engine}")
    
    async def recognize_audio(self, audio_data: str, audio_format: str = "wav",
                            client_recognition: str = None, is_wechat_env: bool = False) -> Dict[str, Any]:
        """
        识别音频数据

        Args:
            audio_data: base64编码的音频数据或微信serverId/localId
            audio_format: 音频格式 (wav, mp3, m4a等)
            client_recognition: 客户端识别结果（如果有）
            is_wechat_env: 是否为微信环境

        Returns:
            识别结果字典
        """
        # 检查引擎配置
        if self.engine == 'disabled':
            return {
                "success": False,
                "error": "语音识别功能已禁用",
                "provider": "disabled"
            }

        # 如果有客户端识别结果且模式允许，优先使用客户端结果
        if client_recognition and self.mode in ['hybrid', 'client_only']:
            logger.info(f"使用客户端语音识别结果: {client_recognition}")
            return {
                "success": True,
                "text": client_recognition,
                "confidence": 0.95,  # 客户端识别假设有较高置信度
                "provider": "wechat_client",
                "source": "client"
            }

        # 如果模式为仅客户端但没有客户端结果
        if self.mode == 'client_only':
            if is_wechat_env:
                return {
                    "success": False,
                    "error": "微信语音识别需要在客户端进行，请确保微信JS-SDK正常工作",
                    "provider": "wechat_client"
                }
            else:
                return {
                    "success": False,
                    "error": "当前模式仅支持客户端识别，但非微信环境无法使用客户端识别",
                    "provider": "client_only"
                }

        # 服务器端识别逻辑
        return await self._server_side_recognition(audio_data, audio_format)

    async def _server_side_recognition(self, audio_data: str, audio_format: str) -> Dict[str, Any]:
        """服务器端语音识别"""
        # 火山引擎识别
        if self.engine == 'volcano':
            if not self.is_volcano_configured:
                return await self._mock_recognition(audio_data)

            # 暂时使用模拟识别，因为HTTP API端点需要进一步确认
            # TODO: 实现正确的火山引擎HTTP API调用
            logger.info("火山引擎HTTP API暂时使用模拟识别，流式识别使用真实API")
            return await self._mock_recognition(audio_data)

        # 微信引擎在服务器端不支持
        elif self.engine == 'wechat':
            return {
                "success": False,
                "error": "微信语音识别需要在客户端进行",
                "provider": "wechat"
            }

        # 未知引擎
        return {
            "success": False,
            "error": f"不支持的语音识别引擎: {self.engine}",
            "provider": "unknown"
        }

    async def _call_volcengine_asr(self, audio_bytes: bytes, audio_format: str) -> Dict[str, Any]:
        """调用火山引擎ASR API"""
        try:
            # 构建请求头
            headers = await self._build_auth_headers()
            headers["Content-Type"] = "application/json"
            
            # 构建请求体
            request_data = {
                "app": {
                    "appid": self.app_id,
                    "cluster": self.cluster
                },
                "user": {
                    "uid": "default_user"
                },
                "audio": {
                    "format": audio_format,
                    "rate": self.recognition_config["sample_rate"],
                    "channel": 1,
                    "bits": 16,
                    "language": self.recognition_config["language"],
                },
                "request": {
                    "reqid": f"asr_{int(asyncio.get_event_loop().time() * 1000)}",
                    "nbest": self.recognition_config["max_alternatives"],
                    "word_info": self.recognition_config["enable_words"],
                    "show_utterances": True
                },
                "audio_data": base64.b64encode(audio_bytes).decode('utf-8')
            }
            
            # 发送请求 - 使用正确的火山引擎API端点
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    self.base_url,  # 直接使用base_url，不添加/submit
                    headers=headers,
                    json=request_data
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return await self._parse_volcengine_response(result)
                else:
                    return {
                        "success": False,
                        "error": f"API请求失败: HTTP {response.status_code}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"API调用异常: {str(e)}"
            }
    
    async def _build_auth_headers(self) -> Dict[str, str]:
        """构建认证请求头"""
        import time
        import hashlib
        import hmac
        
        timestamp = str(int(time.time()))
        nonce = f"nonce_{timestamp}"
        
        # 构建签名字符串
        sign_string = f"appid={self.app_id}&timestamp={timestamp}&nonce={nonce}"
        
        # 计算签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return {
            "Authorization": f"Bearer {self.access_key}",
            "X-Timestamp": timestamp,
            "X-Nonce": nonce,
            "X-Signature": signature
        }
    
    async def _parse_volcengine_response(self, response: Dict) -> Dict[str, Any]:
        """解析火山引擎响应"""
        try:
            if response.get("code") == 0:  # 成功
                result = response.get("result", {})
                utterances = result.get("utterances", [])
                
                if utterances:
                    # 取第一个识别结果
                    utterance = utterances[0]
                    text = utterance.get("text", "")
                    confidence = utterance.get("confidence", 0.0)
                    
                    # 提取词级别信息
                    words = []
                    for word_info in utterance.get("words", []):
                        words.append({
                            "word": word_info.get("text", ""),
                            "start_time": word_info.get("start_time", 0),
                            "end_time": word_info.get("end_time", 0),
                            "confidence": word_info.get("confidence", 0.0)
                        })
                    
                    return {
                        "success": True,
                        "text": text,
                        "confidence": confidence,
                        "duration": result.get("duration", 0),
                        "words": words
                    }
                else:
                    return {
                        "success": False,
                        "error": "未识别到语音内容"
                    }
            else:
                error_msg = response.get("message", "未知错误")
                return {
                    "success": False,
                    "error": f"识别失败: {error_msg}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"响应解析失败: {str(e)}"
            }
    
    async def _mock_recognition(self, audio_data: str) -> Dict[str, Any]:
        """模拟语音识别（用于开发测试）"""
        try:
            # 模拟处理延迟
            await asyncio.sleep(1.0)
            
            # 根据音频数据长度模拟不同的识别结果
            audio_bytes = base64.b64decode(audio_data)
            audio_length = len(audio_bytes)
            
            # 模拟识别结果
            mock_texts = [
                "你好，我想咨询一下",
                "我最近感到很焦虑",
                "谢谢你的帮助",
                "我明白了",
                "还有其他问题吗",
                "今天天气不错",
                "我需要更多信息"
            ]
            
            # 根据音频长度选择文本
            text_index = (audio_length // 1000) % len(mock_texts)
            mock_text = mock_texts[text_index]
            
            # 模拟置信度
            confidence = 0.85 + (audio_length % 100) / 1000
            
            return {
                "success": True,
                "text": mock_text,
                "confidence": min(confidence, 0.99),
                "duration": audio_length / 16000,  # 假设16kHz采样率
                "words": [
                    {
                        "word": word,
                        "start_time": i * 0.5,
                        "end_time": (i + 1) * 0.5,
                        "confidence": confidence
                    }
                    for i, word in enumerate(mock_text)
                ],
                "provider": "mock"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"模拟识别失败: {str(e)}",
                "provider": "mock"
            }
    
    async def create_streaming_session(self,
                                     on_result: Callable[[Dict[str, Any]], None],
                                     on_error: Callable[[str], None] = None) -> 'StreamingSession':
        """创建流式识别会话"""
        if self.engine == 'disabled':
            raise Exception("语音识别功能已禁用")

        if self.engine == 'wechat':
            raise Exception("微信语音识别不支持流式识别")

        if self.engine == 'volcano':
            if not self.is_volcano_configured:
                # 返回模拟流式识别会话
                logger.info("火山引擎未配置，使用模拟流式识别会话")
                return MockStreamingSession(on_result, on_error)
            else:
                # 使用真实的火山引擎流式识别会话
                logger.info("🔥 创建火山引擎真实流式识别会话")
                logger.info(f"🔧 配置信息: app_id={self.app_id[:8]}..., cluster={self.cluster}")
                logger.info(f"🌐 WebSocket URL: {self.streaming_url}")

                try:
                    session = VolcanoStreamingSession(
                        self.app_id, self.access_key, self.secret_key,
                        self.cluster, self.streaming_url,
                        on_result, on_error
                    )
                    logger.info("✅ 火山引擎流式识别会话创建成功")
                    return session
                except Exception as e:
                    logger.error(f"❌ 创建火山引擎流式识别会话失败: {e}")
                    logger.info("🔄 降级到模拟流式识别会话")
                    return MockStreamingSession(on_result, on_error)

        raise Exception(f"不支持的语音识别引擎: {self.engine}")
    
    def validate_audio_format(self, audio_data: str, max_size_mb: int = 10) -> Dict[str, Any]:
        """验证音频格式和大小"""
        try:
            audio_bytes = base64.b64decode(audio_data)
            audio_size = len(audio_bytes)
            
            # 检查文件大小
            max_size_bytes = max_size_mb * 1024 * 1024
            if audio_size > max_size_bytes:
                return {
                    "valid": False,
                    "error": f"音频文件过大，最大支持{max_size_mb}MB"
                }
            
            # 检查最小大小
            if audio_size < 1024:  # 1KB
                return {
                    "valid": False,
                    "error": "音频文件过小"
                }
            
            return {
                "valid": True,
                "size": audio_size,
                "size_mb": round(audio_size / 1024 / 1024, 2)
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"音频数据格式错误: {str(e)}"
            }
    
    def get_supported_formats(self) -> list:
        """获取支持的音频格式"""
        return ["wav", "mp3", "m4a", "aac", "flac", "ogg"]
    
    def get_recommended_mode(self, is_wechat_env: bool = False) -> Dict[str, Any]:
        """获取推荐的识别模式"""
        if self.engine == 'disabled':
            return {
                "mode": "disabled",
                "reason": "语音识别功能已禁用"
            }

        # 根据引擎和环境推荐模式
        if self.engine == 'wechat':
            return {
                "mode": "client_only",
                "reason": "微信语音识别仅支持客户端模式",
                "fallback": None
            }
        elif self.engine == 'volcano':
            if is_wechat_env and self.mode == 'hybrid':
                return {
                    "mode": "client_first",
                    "reason": "微信环境下优先使用客户端识别，失败时使用火山引擎",
                    "fallback": "server" if self.fallback_enabled else None
                }
            else:
                return {
                    "mode": "server",
                    "reason": "使用火山引擎服务器端识别",
                    "fallback": None
                }

        return {
            "mode": "unknown",
            "reason": f"未知的引擎配置: {self.engine}"
        }

    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        if self.engine == 'disabled':
            return {
                "engine": self.engine,
                "mode": self.mode,
                "configured": False,
                "provider": "disabled",
                "status": "disabled",
                "message": "语音识别功能已禁用"
            }

        if self.engine == 'wechat':
            return {
                "engine": self.engine,
                "mode": self.mode,
                "configured": True,
                "provider": "wechat",
                "status": "client_side",
                "volcano_configured": self.is_volcano_configured,
                "fallback_enabled": self.fallback_enabled,
                "message": "使用微信客户端语音识别",
                "supported_formats": ["amr", "mp3"],
                "supported_languages": ["zh-CN"],
                "features": {
                    "real_time": True,
                    "streaming": False,
                    "word_timestamps": False,
                    "confidence_scores": False,
                    "punctuation": True
                }
            }

        if self.engine == 'volcano':
            return {
                "engine": self.engine,
                "mode": self.mode,
                "configured": self.is_volcano_configured,
                "provider": "volcengine" if self.is_volcano_configured else "mock",
                "status": "active" if self.is_volcano_configured else "mock",
                "volcano_configured": self.is_volcano_configured,
                "fallback_enabled": self.fallback_enabled,
                "supported_formats": self.get_supported_formats(),
                "max_file_size_mb": 10,
                "supported_languages": ["zh-CN", "en-US"],
                "features": {
                    "real_time": True,
                    "streaming": True,
                    "word_timestamps": True,
                    "confidence_scores": True,
                    "punctuation": True
                }
            }



        return {
            "engine": self.engine,
            "mode": self.mode,
            "configured": False,
            "provider": "unknown",
            "status": "error",
            "message": f"不支持的语音识别引擎: {self.engine}"
        }


class StreamingSession:
    """流式识别会话基类"""

    def __init__(self, on_result: Callable[[Dict[str, Any]], None],
                 on_error: Callable[[str], None] = None):
        self.on_result = on_result
        self.on_error = on_error or (lambda x: None)
        self.is_active = False

    async def start(self) -> bool:
        """开始流式识别"""
        raise NotImplementedError

    async def send_audio(self, audio_data: bytes) -> bool:
        """发送音频数据"""
        raise NotImplementedError

    async def end(self) -> bool:
        """结束流式识别"""
        raise NotImplementedError

    async def close(self):
        """关闭会话"""
        raise NotImplementedError


class VolcanoStreamingSession(StreamingSession):
    """火山引擎流式识别会话 - 基于官方demo实现"""

    def __init__(self, app_id: str, access_key: str, secret_key: str,
                 cluster: str, streaming_url: str,
                 on_result: Callable[[Dict[str, Any]], None],
                 on_error: Callable[[str], None] = None):
        super().__init__(on_result, on_error)
        self.app_id = app_id
        self.access_key = access_key  # 这里实际是token
        self.secret_key = secret_key
        self.cluster = cluster
        self.streaming_url = streaming_url
        self.websocket = None
        self.session_id = f"streaming_{int(time.time() * 1000)}"
        self.reqid = str(uuid.uuid4())
        self.success_code = 1000

        # 音频参数 - 根据火山引擎文档优化
        self.audio_format = "raw"  # 使用raw格式，因为我们发送的是PCM原始数据
        self.codec = "raw"         # 音频编码格式，raw表示PCM原始数据
        self.sample_rate = 16000
        self.language = "zh-CN"
        self.bits = 16
        self.channel = 1

        print(f"🎵 音频参数配置:")
        print(f"  - format: {self.audio_format}")
        print(f"  - sample_rate: {self.sample_rate}")
        print(f"  - language: {self.language}")
        print(f"  - bits: {self.bits}")
        print(f"  - channel: {self.channel}")
        print(f"  - codec: {self.codec}")

    def construct_request(self):
        """构建火山引擎请求参数"""
        req = {
            'app': {
                'appid': self.app_id,
                'cluster': self.cluster,
                'token': self.access_key,  # 直接使用控制台获取的token
            },
            'user': {
                'uid': f'streaming_asr_{self.session_id}',  # 使用会话ID作为用户标识
                'device': 'WebBrowser',
                'platform': 'Web|1.0',
                'network': 'WiFi'
            },
            'request': {
                'reqid': self.reqid,
                'sequence': 1,  # 根据官方文档，这是必填字段
                'nbest': 1,
                'workflow': 'audio_in,resample,partition,vad,fe,decode',  # 使用官方文档的默认值
                'show_utterances': True,  # 启用utterances以获取更详细的结果
                # 移除一些可能不支持的字段，使用最基本的配置
            },
            'audio': {
                'format': self.audio_format,    # "wav"
                'rate': self.sample_rate,       # 16000
                'bits': self.bits,             # 16
                'channel': self.channel,       # 1
                'language': self.language      # "zh-CN" - 根据官方文档添加
                # 移除codec字段，官方文档中没有这个字段
            }
        }

        print(f"🔐 构建火山引擎请求参数:")
        print(f"  - appid: {self.app_id}")
        print(f"  - cluster: {self.cluster}")
        print(f"  - token: {self.access_key[:16]}...")
        print(f"  - audio format: {req['audio']['format']}")
        print(f"  - sample rate: {req['audio']['rate']}")
        print(f"  - language: {req['audio']['language']}")
        print(f"  - workflow: {req['request']['workflow']}")
        print(f"  - show_utterances: {req['request']['show_utterances']}")

        logger.info(f"完整请求参数: {req}")

        return req

    def token_auth(self):
        """Token认证 - 根据火山引擎官方文档"""
        # 格式：Authorization: Bearer; {token}
        # 注意：Bearer和token之间使用分号分隔
        return {'Authorization': f'Bearer; {self.access_key}'}

    async def start(self) -> bool:
        """开始火山引擎流式识别"""
        try:
            logger.info("🚀 开始启动火山引擎流式识别")
            logger.info(f"🔗 连接URL: {self.streaming_url}")
            logger.info(f"🆔 会话ID: {self.session_id}")
            logger.info(f"📋 请求ID: {self.reqid}")

            # 构建 full client request
            request_params = self.construct_request()
            logger.info(f"📝 请求参数: {json.dumps(request_params, indent=2, ensure_ascii=False)}")

            payload_bytes = str.encode(json.dumps(request_params))
            payload_bytes = gzip.compress(payload_bytes)
            logger.info(f"📦 压缩后载荷大小: {len(payload_bytes)} bytes")

            full_client_request = bytearray(generate_full_default_header())
            full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            full_client_request.extend(payload_bytes)
            logger.info(f"📨 完整请求大小: {len(full_client_request)} bytes")

            # 构建认证头
            header = self.token_auth()
            logger.info(f"🔐 认证头: {header}")
            print(f"🔐 认证头: {header}")

            # 建立WebSocket连接
            logger.info("🔌 开始建立WebSocket连接...")
            print("🔌 开始建立WebSocket连接...")

            # 使用正确的认证头连接
            try:
                self.websocket = await websockets.connect(
                    self.streaming_url,
                    additional_headers=header,
                    max_size=1000000000
                )
                logger.info("✅ WebSocket连接成功")
                print("✅ WebSocket连接成功")
            except TypeError:
                # 如果不支持additional_headers，尝试extra_headers
                try:
                    self.websocket = await websockets.connect(
                        self.streaming_url,
                        extra_headers=header,
                        max_size=1000000000
                    )
                    logger.info("✅ WebSocket连接成功（使用extra_headers）")
                    print("✅ WebSocket连接成功（使用extra_headers）")
                except Exception as e:
                    logger.error(f"❌ WebSocket连接失败: {e}")
                    print(f"❌ WebSocket连接失败: {e}")
                    raise

            logger.info("🎉 WebSocket连接已建立")

            # 发送 full client request
            logger.info("📤 发送初始化请求...")
            await self.websocket.send(full_client_request)
            logger.info("✅ 初始化请求已发送")

            # 接收响应
            logger.info("📥 等待服务器响应...")
            res = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            logger.info(f"📨 收到响应，大小: {len(res)} bytes")

            result = parse_response(res)
            logger.info(f"📋 解析响应: {result}")

            if 'payload_msg' in result and result['payload_msg']['code'] != self.success_code:
                error_msg = f"初始化失败: {result['payload_msg']}"
                logger.error(f"❌ {error_msg}")
                self.on_error(error_msg)
                return False

            self.is_active = True
            logger.info("🎉 火山引擎流式识别已启动成功")

            # 不在这里启动后台接收任务，而是在发送音频时启动
            # asyncio.create_task(self._receive_messages())

            return True

        except asyncio.TimeoutError:
            error_msg = "等待服务器响应超时"
            logger.error(f"⏰ {error_msg}")
            self.on_error(error_msg)
            return False
        except websockets.exceptions.ConnectionClosed as e:
            error_msg = f"WebSocket连接被关闭: {e}"
            logger.error(f"🔌 {error_msg}")
            self.on_error(error_msg)
            return False
        except Exception as e:
            error_msg = f"启动火山引擎流式识别失败: {e}"
            logger.error(f"❌ {error_msg}")
            logger.exception("详细错误信息:")
            self.on_error(error_msg)
            return False

    async def send_audio(self, audio_data: bytes) -> bool:
        """发送音频数据到火山引擎"""
        if not self.is_active:
            logger.warning("⚠️ 流式识别会话未激活")
            return False

        if not self.websocket:
            logger.warning("⚠️ WebSocket连接不存在")
            return False

        try:
            logger.debug(f"🎵 准备发送音频数据: {len(audio_data)} bytes")

            # 压缩音频数据
            payload_bytes = gzip.compress(audio_data)
            logger.debug(f"📦 压缩后大小: {len(payload_bytes)} bytes")

            # 构建音频数据请求
            audio_only_request = bytearray(generate_audio_default_header())
            audio_only_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            audio_only_request.extend(payload_bytes)
            logger.debug(f"📨 完整音频请求大小: {len(audio_only_request)} bytes")

            # 发送音频数据
            await self.websocket.send(audio_only_request)
            logger.debug(f"✅ 音频数据发送成功: {len(audio_data)} bytes")

            # 启动后台任务接收响应（如果还没有启动）
            if not hasattr(self, '_receive_task') or self._receive_task.done():
                self._receive_task = asyncio.create_task(self._receive_messages())
                print(f"🔄 启动后台接收任务")

            return True

        except Exception as e:
            logger.error(f"发送音频数据失败: {e}")
            self.on_error(f"发送音频数据失败: {str(e)}")
            return False

    async def end(self) -> bool:
        """结束火山引擎流式识别"""
        print(f"🛑 开始结束火山引擎流式识别...")
        if not self.is_active or not self.websocket:
            print(f"⚠️ 流式识别未激活或WebSocket不存在")
            return False

        try:
            # 发送最后的音频数据包（空数据，标记结束）
            payload_bytes = gzip.compress(b"")
            last_audio_request = bytearray(generate_last_audio_default_header())
            last_audio_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            last_audio_request.extend(payload_bytes)

            print(f"📤 发送结束标记，大小: {len(last_audio_request)} bytes")
            await self.websocket.send(last_audio_request)
            logger.info("已发送结束标记")
            print(f"✅ 结束标记已发送")

            # 等待后台接收任务处理最终结果
            print(f"⏳ 等待后台任务处理最终识别结果...")
            try:
                # 等待一段时间让后台任务处理最终结果
                await asyncio.sleep(3.0)
                print(f"✅ 等待完成")
            except Exception as e:
                logger.error(f"等待最终结果失败: {e}")
                print(f"❌ 等待最终结果失败: {e}")

            self.is_active = False
            logger.info(f"火山引擎流式识别会话 {self.session_id} 已结束")
            print(f"🔚 火山引擎流式识别会话已结束")
            return True

        except Exception as e:
            logger.error(f"结束流式识别失败: {e}")
            print(f"❌ 结束流式识别失败: {e}")
            self.on_error(f"结束流式识别失败: {str(e)}")
            return False

    async def close(self):
        """关闭火山引擎WebSocket连接"""
        print(f"🔚 关闭火山引擎WebSocket连接...")
        self.is_active = False

        # 取消后台接收任务
        if hasattr(self, '_receive_task') and not self._receive_task.done():
            print(f"🛑 取消后台接收任务...")
            self._receive_task.cancel()
            try:
                await self._receive_task
            except asyncio.CancelledError:
                print(f"✅ 后台接收任务已取消")

        if self.websocket:
            try:
                await self.websocket.close()
                print(f"✅ WebSocket连接已关闭")
            except Exception as e:
                print(f"❌ 关闭WebSocket连接失败: {e}")
                logger.error(f"关闭WebSocket连接失败: {e}")
            finally:
                self.websocket = None

    def _build_auth_params(self) -> str:
        """构建认证参数"""
        timestamp = str(int(time.time()))
        nonce = f"nonce_{timestamp}"

        # 构建签名字符串
        sign_string = f"appid={self.app_id}&timestamp={timestamp}&nonce={nonce}"

        # 计算签名
        signature = hmac.new(
            self.secret_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

        return f"appid={self.app_id}&timestamp={timestamp}&nonce={nonce}&signature={signature}&access_key={self.access_key}"

    async def _receive_messages(self):
        """接收WebSocket消息（实时识别结果）"""
        print(f"🔄 开始接收WebSocket消息...")
        try:
            while self.is_active and self.websocket:
                try:
                    print(f"⏳ 等待接收消息...")
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
                    print(f"📨 收到原始消息，大小: {len(message)} bytes")

                    result = parse_response(message)
                    print(f"📋 解析后的消息: {result}")

                    if 'payload_msg' in result:
                        print(f"📤 处理payload_msg...")
                        self._process_recognition_result(result['payload_msg'])
                    else:
                        print(f"⚠️ 消息中没有payload_msg字段")

                except asyncio.TimeoutError:
                    print(f"⏰ 接收消息超时，继续等待...")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print(f"🔌 WebSocket连接已关闭")
                    logger.info("WebSocket连接已关闭")
                    break
                except Exception as e:
                    print(f"❌ 处理WebSocket消息失败: {e}")
                    logger.error(f"处理WebSocket消息失败: {e}")

        except Exception as e:
            print(f"❌ 接收WebSocket消息失败: {e}")
            logger.error(f"接收WebSocket消息失败: {e}")
            self.on_error(f"接收消息失败: {str(e)}")

        print(f"🔚 消息接收循环结束")

    async def _receive_final_results(self):
        """接收最终识别结果"""
        try:
            # 等待最终结果
            for _ in range(10):  # 最多等待10次
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                    result = parse_response(message)

                    if 'payload_msg' in result:
                        self._process_recognition_result(result['payload_msg'])

                except asyncio.TimeoutError:
                    continue
                except websockets.exceptions.ConnectionClosed:
                    break

        except Exception as e:
            logger.error(f"接收最终结果失败: {e}")

    def _process_recognition_result(self, payload_msg: dict):
        """处理识别结果"""
        try:
            print(f"🔍 火山引擎处理消息: {payload_msg}")
            logger.info(f"🔍 火山引擎处理消息: {payload_msg}")

            # 检查消息类型
            message_code = payload_msg.get('code')
            message_type = payload_msg.get('message', '')

            if message_code == 1000 and message_type == 'Success':
                sequence = payload_msg.get('sequence', 0)

                # 检查是否有result字段来区分ACK和识别结果
                if 'result' not in payload_msg:
                    # 这是一个ACK确认消息，不是识别结果
                    print(f"✅ 收到ACK确认消息，序列号: {sequence}")
                    return
                else:
                    # 这是包含识别结果的成功响应
                    print(f"🎯 收到识别结果响应，序列号: {sequence}")
                    # 继续处理识别结果

            # 检查是否有识别结果
            if 'result' in payload_msg:
                result_list = payload_msg['result']
                print(f"📋 提取结果列表: {result_list}")

                # result是一个列表，取第一个元素
                if result_list and len(result_list) > 0:
                    result_data = result_list[0]
                    print(f"📋 提取第一个结果: {result_data}")

                    # 提取文本结果
                    text = result_data.get('text', '')
                    confidence = result_data.get('confidence', 0.0)

                    # 检查sequence来判断是否为最终结果
                    sequence = payload_msg.get('sequence', 0)
                    is_final = sequence < 0  # 负数表示最后一包

                    print(f"📝 提取文本: '{text}', sequence: {sequence}, is_final: {is_final}, confidence: {confidence}")

                    if text:
                        result = {
                            "type": "final_result" if is_final else "partial_result",
                            "text": text,
                            "is_final": is_final,
                            "confidence": confidence,
                            "timestamp": time.time(),
                            "sequence": sequence
                        }

                        logger.info(f"火山引擎识别结果: {text} (sequence: {sequence}, final: {is_final})")
                        print(f"🎉 火山引擎识别结果: {text} (sequence: {sequence}, final: {is_final})")
                        print(f"📤 调用回调函数发送结果...")
                        self.on_result(result)
                        print(f"✅ 回调函数调用完成")
                    else:
                        print(f"⚠️ 没有提取到文本内容")

                    # 如果有utterances，也打印出来用于调试
                    if 'utterances' in result_data:
                        utterances = result_data['utterances']
                        print(f"🗣️ 发现utterances: {len(utterances)}个")
                        for i, utterance in enumerate(utterances):
                            utterance_text = utterance.get('text', '')
                            start_time = utterance.get('start_time', 0)
                            end_time = utterance.get('end_time', 0)
                            print(f"  utterance[{i}]: '{utterance_text}' ({start_time}-{end_time}ms)")
                else:
                    print(f"⚠️ result列表为空")
            else:
                # 检查是否有错误信息
                if message_code != 1000:
                    error_msg = f"火山引擎错误: code={message_code}, message={message_type}"
                    print(f"❌ {error_msg}")
                    logger.error(error_msg)
                else:
                    print(f"ℹ️ 收到非识别结果消息: {payload_msg}")

        except Exception as e:
            logger.error(f"处理识别结果失败: {e}")
            print(f"❌ 处理识别结果失败: {e}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")




class MockStreamingSession(StreamingSession):
    """模拟流式识别会话"""

    def __init__(self, on_result: Callable[[Dict[str, Any]], None],
                 on_error: Callable[[str], None] = None):
        super().__init__(on_result, on_error)
        self.audio_buffer = b""
        self.mock_texts = [
            "你好", "我想", "咨询", "一下", "心理", "问题",
            "最近", "感到", "很", "焦虑", "不知道", "怎么办"
        ]
        self.text_index = 0

    async def start(self) -> bool:
        """开始模拟流式识别"""
        self.is_active = True
        logger.info("模拟流式识别会话已开始")
        return True

    async def send_audio(self, audio_data: bytes) -> bool:
        """模拟处理音频数据"""
        if not self.is_active:
            print(f"⚠️ 模拟流式识别会话未激活")
            return False

        self.audio_buffer += audio_data
        logger.info(f"模拟流式识别收到音频数据: {len(audio_data)} bytes, 总缓冲: {len(self.audio_buffer)} bytes")
        print(f"🎵 模拟流式识别收到音频数据: {len(audio_data)} bytes, 总缓冲: {len(self.audio_buffer)} bytes")

        # 降低触发阈值，每收到较少的音频数据就返回识别结果
        if len(self.audio_buffer) > 512:  # 512 bytes，降低阈值
            if self.text_index < len(self.mock_texts):
                result = {
                    "type": "partial_result",
                    "text": " ".join(self.mock_texts[:self.text_index + 1]),  # 累积文本
                    "is_final": False,
                    "confidence": 0.85,
                    "timestamp": time.time()
                }
                logger.info(f"模拟流式识别返回部分结果: {result['text']}")
                print(f"🎯 模拟流式识别返回部分结果: {result['text']}")
                print(f"📤 调用回调函数...")
                self.on_result(result)
                print(f"✅ 回调函数调用完成")
                self.text_index += 1

            self.audio_buffer = b""

        return True

    async def end(self) -> bool:
        """结束模拟识别"""
        print(f"🛑 模拟流式识别结束，当前状态: active={self.is_active}, text_index={self.text_index}")

        if self.is_active:
            # 如果没有收到任何音频数据，使用默认文本
            if self.text_index == 0:
                final_text = "你好，我想咨询一下心理问题"
                logger.info("模拟流式识别：没有收到音频数据，使用默认文本")
                print("📝 模拟流式识别：没有收到音频数据，使用默认文本")
            else:
                final_text = " ".join(self.mock_texts[:self.text_index])
                print(f"📝 模拟流式识别：使用累积文本，索引: {self.text_index}")

            result = {
                "type": "final_result",
                "text": final_text,
                "is_final": True,
                "confidence": 0.88,
                "timestamp": time.time()
            }
            logger.info(f"模拟流式识别返回最终结果: {final_text}")
            print(f"🎉 模拟流式识别返回最终结果: {final_text}")
            print(f"📤 调用回调函数发送最终结果...")
            self.on_result(result)
            print(f"✅ 最终结果回调函数调用完成")
            self.is_active = False
            logger.info("模拟流式识别会话已结束")
            print("🔚 模拟流式识别会话已结束")

        return True

    async def close(self):
        """关闭模拟会话"""
        self.is_active = False


# 全局语音识别服务实例
voice_recognition_service = VoiceRecognitionService()
