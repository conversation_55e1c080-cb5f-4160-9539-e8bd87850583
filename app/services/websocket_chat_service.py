"""
WebSocket聊天服务
实现实时AI聊天功能，包括文本和语音消息处理
"""
import json
import uuid
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import WebSocket
from fastapi.websockets import WebSocketDisconnect
from app.models.airelief import AIReliefUser, ChatSession, ChatMessage
from app.services.external_ai_service import ExternalAIService
from app.services.voice_recognition_service import VoiceRecognitionService
from app.utils.audio_file_helper import audio_file_helper

logger = logging.getLogger(__name__)


class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        # 存储活跃连接: {user_id: {session_id: websocket}}
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        # 存储用户会话映射: {user_id: current_session_id}
        self.user_sessions: Dict[str, str] = {}
        # 存储流式语音识别会话: {user_id: streaming_session}
        self.streaming_sessions: Dict[str, Any] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, session_id: str):
        """建立WebSocket连接"""
        # 注意：websocket.accept() 应该在调用此方法之前已经被调用

        if user_id not in self.active_connections:
            self.active_connections[user_id] = {}

        self.active_connections[user_id][session_id] = websocket
        self.user_sessions[user_id] = session_id

        logger.info(f"用户 {user_id} 会话 {session_id} WebSocket连接已建立")
    
    def disconnect(self, user_id: str, session_id: str):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            if session_id in self.active_connections[user_id]:
                del self.active_connections[user_id][session_id]
            
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
                if user_id in self.user_sessions:
                    del self.user_sessions[user_id]
        
        logger.info(f"用户 {user_id} 会话 {session_id} WebSocket连接已断开")
    
    async def send_personal_message(self, message: dict, user_id: str, session_id: str):
        """发送个人消息"""
        if (user_id in self.active_connections and 
            session_id in self.active_connections[user_id]):
            websocket = self.active_connections[user_id][session_id]
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                self.disconnect(user_id, session_id)
    
    def get_user_connection(self, user_id: str) -> Optional[str]:
        """获取用户当前会话ID"""
        return self.user_sessions.get(user_id)

    def set_streaming_session(self, user_id: str, streaming_session):
        """设置用户的流式语音识别会话"""
        self.streaming_sessions[user_id] = streaming_session

    def get_streaming_session(self, user_id: str):
        """获取用户的流式语音识别会话"""
        return self.streaming_sessions.get(user_id)

    def remove_streaming_session(self, user_id: str):
        """移除用户的流式语音识别会话"""
        if user_id in self.streaming_sessions:
            del self.streaming_sessions[user_id]


class WebSocketChatService:
    """WebSocket聊天服务"""

    def __init__(self):
        self.manager = ConnectionManager()
        self.ai_service = ExternalAIService()
        self.voice_service = VoiceRecognitionService()

        # 流式音频缓冲区 - 用于合并音频片段
        self.streaming_audio_buffers: Dict[str, Dict] = {}  # {user_id: {'chunks': [bytes], 'start_time': float, 'total_duration': float}}
    
    async def handle_websocket(self, websocket: WebSocket, user_id: str):
        """处理WebSocket连接"""
        # 获取或创建会话
        session = await self._get_or_create_session(user_id)
        session_id = session.session_id

        await self.manager.connect(websocket, user_id, session_id)
        logger.info(f"✅ 用户 {user_id} 重新连接到会话 {session_id}")

        try:
            # 添加小延迟确保WebSocket连接完全建立
            await asyncio.sleep(0.1)

            # 发送连接成功消息
            await self._send_system_message(
                user_id, session_id, "connected",
                {"message": "连接成功", "session_id": session_id}
            )

            # 发送AI欢迎消息（如果是新会话）
            if session.message_count == 0:
                await self._send_ai_welcome_message(user_id, session_id)

            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message_data = json.loads(data)

                # 添加调试日志
                logger.info(f"📨 收到WebSocket消息 - 用户: {user_id}, 类型: {message_data.get('type')}, 数据: {str(message_data)[:200]}...")

                # 处理消息
                await self._handle_message(user_id, session_id, message_data)

        except WebSocketDisconnect:
            logger.info(f"用户 {user_id} WebSocket连接断开，结束会话 {session_id}")
            # WebSocket断开时结束会话
            await self._handle_disconnect(user_id, session_id)
        except Exception as e:
            logger.error(f"WebSocket处理异常: {e}")
            # 异常情况下也结束会话
            await self._handle_disconnect(user_id, session_id)
        finally:
            # 清理WebSocket连接资源
            self.manager.disconnect(user_id, session_id)
            # 清理流式识别会话
            await self._cleanup_streaming_session(user_id)
            # 清理音频缓冲区
            await self._cleanup_audio_buffer(user_id)
            logger.info(f"✅ 用户 {user_id} 连接资源已清理，会话已结束")
    
    async def _get_or_create_session(self, user_id: str) -> ChatSession:
        """获取或创建聊天会话"""
        user = await AIReliefUser.get(user_id=user_id)

        # 查找活跃会话
        session = await ChatSession.filter(
            user=user,
            status="active"
        ).first()

        if not session:
            # 先在外部AI服务开始对话，获取正确的session_id
            try:
                ai_result = await self.ai_service.start_conversation(user_id)
                if ai_result.get("success"):
                    external_session_id = ai_result.get("session_id")
                    session_id = external_session_id or f"chat_{user_id}_{int(datetime.now().timestamp())}"
                else:
                    logger.error(f"外部AI开始对话失败: {ai_result.get('message')}")
                    session_id = f"chat_{user_id}_{int(datetime.now().timestamp())}"
            except Exception as e:
                logger.error(f"外部AI开始对话异常: {e}")
                session_id = f"chat_{user_id}_{int(datetime.now().timestamp())}"

            # 创建本地会话记录
            session = await ChatSession.create(
                session_id=session_id,
                user=user,
                title="AI聊天会话",
                status="active"
            )

        return session
    
    async def _handle_message(self, user_id: str, session_id: str, message_data: dict):
        """处理接收到的消息"""
        message_type = message_data.get("type")

        if message_type == "text":
            await self._handle_text_message(user_id, session_id, message_data)
        elif message_type == "audio" or message_type == "voice_message":
            await self._handle_audio_message(user_id, session_id, message_data)
        elif message_type == "streaming_start":
            await self._handle_streaming_start(user_id, session_id, message_data)
        elif message_type == "streaming_audio":
            await self._handle_streaming_audio(user_id, session_id, message_data)
        elif message_type == "streaming_end":
            await self._handle_streaming_end(user_id, session_id, message_data)
        elif message_type == "ping":
            await self._handle_ping(user_id, session_id)
        else:
            logger.warning(f"收到不支持的消息类型: {message_type}")
            await self._send_error_message(user_id, session_id, f"不支持的消息类型: {message_type}")
    
    async def _handle_text_message(self, user_id: str, session_id: str, message_data: dict):
        """处理文本消息"""
        content = message_data.get("content", "").strip()
        if not content:
            await self._send_error_message(user_id, session_id, "消息内容不能为空")
            return

        try:
            # 保存用户消息
            user_message = await self._save_message(
                user_id, session_id, content, "text", "user"
            )

            # 发送消息确认
            await self._send_message_status(user_id, session_id, user_message.message_id, "sent")

            # 发送AI正在输入状态
            await self._send_typing_status(user_id, session_id, True)

            # 调用外部AI服务
            ai_response = await self.ai_service.send_message(user_id, session_id, content)

            # 检查AI是否返回了结束标志
            if ai_response.get("session_ended"):
                # AI主动结束会话，更新状态
                await self._end_session_by_ai(user_id, session_id)

            # 保存AI回复
            ai_message = await self._save_message(
                user_id, session_id, ai_response["content"], "text", "assistant",
                external_data=ai_response
            )

            # 发送AI回复
            await self._send_ai_message(user_id, session_id, ai_message)

        except Exception as e:
            logger.error(f"处理文本消息失败: {e}")
            await self._send_error_message(user_id, session_id, "消息处理失败，请重试")
        finally:
            await self._send_typing_status(user_id, session_id, False)
    
    async def _handle_audio_message(self, user_id: str, session_id: str, message_data: dict):
        """处理语音消息 - 支持多种识别模式"""
        # 支持两种数据结构：直接在message_data中，或在data子对象中
        data = message_data.get("data", message_data)
        audio_data = data.get("audio_data")  # base64编码的音频数据或微信serverId/localId
        audio_duration = data.get("duration", 0)
        recognition_mode = data.get("recognition_mode", "auto")
        client_recognition = data.get("client_recognition")  # 客户端识别结果
        is_wechat_env = data.get("is_wechat_env", False)  # 是否为微信环境
        audio_format = data.get("audio_format", "wav")  # 音频格式

        if not audio_data:
            await self._send_error_message(user_id, session_id, "音频数据不能为空")
            return

        # 如果是模拟数据，跳过处理但返回成功
        if audio_data == "mock_audio_data_for_streaming":
            logger.info("收到流式识别的模拟音频数据，跳过处理")
            await self._send_system_message(
                user_id, session_id, "voice_processing",
                {"message": "流式识别音频数据已保存"}
            )
            return

        try:
            transcription = ""
            confidence = 0.0
            provider = "unknown"

            # 发送处理状态消息
            if client_recognition:
                await self._send_system_message(
                    user_id, session_id, "voice_processing",
                    {"message": "使用客户端语音识别结果..."}
                )
            else:
                await self._send_system_message(
                    user_id, session_id, "voice_processing",
                    {"message": "正在识别语音..."}
                )

            # 调用语音识别服务（支持多种模式）
            recognition_result = await self.voice_service.recognize_audio(
                audio_data,
                audio_format=audio_format,
                client_recognition=client_recognition,
                is_wechat_env=is_wechat_env
            )

            if not recognition_result["success"]:
                # 如果启用了降级机制且是客户端识别失败，尝试服务器端识别
                if (client_recognition and
                    self.voice_service.fallback_enabled and
                    recognition_result.get("provider") == "wechat_client"):

                    logger.info("客户端识别失败，尝试服务器端降级识别")
                    await self._send_system_message(
                        user_id, session_id, "voice_processing",
                        {"message": "客户端识别失败，尝试服务器端识别..."}
                    )

                    # 重新调用，不传递客户端识别结果
                    fallback_result = await self.voice_service.recognize_audio(
                        audio_data,
                        audio_format=audio_format,
                        client_recognition=None,
                        is_wechat_env=is_wechat_env
                    )

                    if fallback_result["success"]:
                        recognition_result = fallback_result
                        logger.info("服务器端降级识别成功")
                    else:
                        await self._send_error_message(user_id, session_id,
                            f"语音识别失败: {recognition_result.get('error', '未知错误')}")
                        return
                else:
                    await self._send_error_message(user_id, session_id,
                        f"语音识别失败: {recognition_result.get('error', '未知错误')}")
                    return

            transcription = recognition_result["text"]
            confidence = recognition_result["confidence"]
            provider = recognition_result.get("provider", "unknown")
            source = recognition_result.get("source", "server")

            logger.info(f"语音识别成功 [{provider}] ({source}): {transcription}")

            # 保存语音消息（包含识别文本）
            user_message = await self._save_voice_message(
                user_id, session_id, audio_data, audio_duration,
                transcription, confidence, provider
            )

            # 发送语音消息确认（包含识别文本）
            await self._send_voice_message_confirmation(user_id, session_id, user_message)

            # 如果识别成功，继续AI对话
            if transcription.strip():
                await self._send_typing_status(user_id, session_id, True)

                # 使用识别文本调用AI服务
                ai_response = await self.ai_service.send_message(user_id, session_id, transcription)

                # 检查AI是否返回了结束标志
                if ai_response.get("session_ended"):
                    # AI主动结束会话，更新状态
                    await self._end_session_by_ai(user_id, session_id)

                # 保存AI回复
                ai_message = await self._save_message(
                    user_id, session_id, ai_response["content"], "text", "assistant",
                    external_data=ai_response
                )

                # 发送AI回复
                await self._send_ai_message(user_id, session_id, ai_message)

        except Exception as e:
            logger.error(f"处理语音消息失败: {e}")
            await self._send_error_message(user_id, session_id, "语音消息处理失败")
        finally:
            await self._send_typing_status(user_id, session_id, False)
    
    async def _save_message(self, user_id: str, session_id: str, content: str, 
                          message_type: str, role: str, external_data: dict = None) -> ChatMessage:
        """保存消息到数据库"""
        user = await AIReliefUser.get(user_id=user_id)
        session = await ChatSession.get(session_id=session_id)
        
        message_id = f"msg_{uuid.uuid4().hex[:16]}"
        
        message = await ChatMessage.create(
            message_id=message_id,
            session=session,
            user=user,
            content=content,
            message_type=message_type,
            role=role,
            external_session_id=external_data.get("session_id") if external_data else None,
            external_message_id=external_data.get("message_id") if external_data else None,
            processing_time=external_data.get("processing_time") if external_data else None
        )
        
        # 更新会话消息计数
        session.message_count += 1
        await session.save()
        
        return message
    
    async def _save_voice_message(self, user_id: str, session_id: str, audio_data: str,
                                duration: int, transcription: str, confidence: float,
                                provider: str = "unknown") -> ChatMessage:
        """保存语音消息"""
        user = await AIReliefUser.get(user_id=user_id)
        session = await ChatSession.get(session_id=session_id)

        message_id = f"voice_{uuid.uuid4().hex[:16]}"

        # 如果有音频数据，保存为data URL格式，便于前端播放
        audio_url = None
        audio_file_size = 0

        if audio_data:
            try:
                import base64
                audio_bytes = base64.b64decode(audio_data)
                audio_file_size = len(audio_bytes)
                # 使用data URL格式，前端可以直接播放
                audio_url = f"data:audio/webm;base64,{audio_data}"
            except Exception as e:
                logger.error(f"处理音频数据失败: {e}")

        message = await ChatMessage.create(
            message_id=message_id,
            session=session,
            user=user,
            content=transcription,  # 使用识别文本作为content
            message_type="audio",
            role="user",
            audio_url=audio_url,
            audio_duration=duration,
            audio_file_size=audio_file_size,
            transcription_text=transcription,
            transcription_confidence=confidence,
            transcription_status="success"
        )

        # 更新会话消息计数
        session.message_count += 1
        await session.save()

        return message
    
    async def _send_ai_welcome_message(self, user_id: str, session_id: str):
        """发送AI欢迎消息"""
        try:
            # 检查是否是新创建的会话，如果是则可能已经有欢迎消息了
            session = await ChatSession.get(session_id=session_id)
            existing_messages = await ChatMessage.filter(session=session, role="assistant").count()

            if existing_messages == 0:
                # 如果没有AI消息，获取欢迎消息
                welcome_response = await self.ai_service.get_welcome_message(user_id, session_id)

                ai_message = await self._save_message(
                    user_id, session_id, welcome_response["content"], "text", "assistant",
                    external_data=welcome_response
                )

                await self._send_ai_message(user_id, session_id, ai_message)
            else:
                logger.info(f"会话 {session_id} 已有AI消息，跳过欢迎消息发送")

        except Exception as e:
            logger.error(f"发送AI欢迎消息失败: {e}")
    
    async def _send_ai_message(self, user_id: str, session_id: str, message: ChatMessage):
        """发送AI消息"""
        await self.manager.send_personal_message({
            "type": "ai_message",
            "data": {
                "message_id": message.message_id,
                "content": message.content,
                "role": message.role,
                "timestamp": message.created_at.isoformat(),
                "processing_time": message.processing_time
            }
        }, user_id, session_id)
    
    async def _send_voice_message_confirmation(self, user_id: str, session_id: str, message: ChatMessage):
        """发送语音消息确认"""
        await self.manager.send_personal_message({
            "type": "voice_message_sent",
            "data": {
                "message_id": message.message_id,
                "content": message.content,
                "type": "audio",
                "role": "user",
                "transcription": message.transcription_text,
                "confidence": message.transcription_confidence,
                "audioDuration": message.audio_duration,
                "audioUrl": message.audio_url,
                "timestamp": message.created_at.isoformat()
            }
        }, user_id, session_id)
    
    async def _send_message_status(self, user_id: str, session_id: str, message_id: str, status: str):
        """发送消息状态"""
        await self.manager.send_personal_message({
            "type": "message_status",
            "data": {
                "message_id": message_id,
                "status": status
            }
        }, user_id, session_id)
    
    async def _send_typing_status(self, user_id: str, session_id: str, is_typing: bool):
        """发送打字状态"""
        await self.manager.send_personal_message({
            "type": "typing_status",
            "data": {
                "is_typing": is_typing
            }
        }, user_id, session_id)
    
    async def _send_system_message(self, user_id: str, session_id: str, message_type: str, data: dict):
        """发送系统消息"""
        await self.manager.send_personal_message({
            "type": message_type,
            "data": data
        }, user_id, session_id)
    
    async def _send_error_message(self, user_id: str, session_id: str, error: str):
        """发送错误消息"""
        await self.manager.send_personal_message({
            "type": "error",
            "data": {
                "message": error,
                "timestamp": datetime.now().isoformat()
            }
        }, user_id, session_id)
    
    async def _handle_ping(self, user_id: str, session_id: str):
        """处理心跳检测"""
        await self.manager.send_personal_message({
            "type": "pong",
            "data": {
                "timestamp": datetime.now().isoformat()
            }
        }, user_id, session_id)



    async def _end_session_by_ai(self, user_id: str, session_id: str):
        """AI主动结束会话时的处理"""
        try:
            session = await ChatSession.get(session_id=session_id)
            if session.status == "active":
                session.status = "ended"
                session.ended_at = datetime.now()
                await session.save()

                logger.info(f"会话 {session_id} 被AI主动结束")

        except Exception as e:
            logger.error(f"AI结束会话处理异常: {e}")

    async def _handle_disconnect(self, user_id: str, session_id: str):
        """处理WebSocket断开 - 结束AI会话"""
        try:
            session = await ChatSession.get(session_id=session_id)
            if session.status == "active":
                logger.info(f"WebSocket断开，结束会话 {session_id}")

                # 1. 调用外部AI服务结束对话
                try:
                    ai_response = await self.ai_service.end_session(user_id, session_id)
                    logger.info(f"外部AI会话已结束: {ai_response}")
                except Exception as e:
                    logger.error(f"结束外部AI会话失败: {e}")

                # 2. 更新本地会话状态
                session.status = "ended"
                session.ended_at = datetime.now()
                await session.save()

                logger.info(f"会话 {session_id} 因WebSocket断开而结束")

        except Exception as e:
            logger.error(f"处理WebSocket断开异常: {e}")

    async def end_session(self, session_id: str):
        """手动结束会话（供外部调用）"""
        try:
            session = await ChatSession.get(session_id=session_id)
            if session.status == "active":
                session.status = "ended"
                session.ended_at = datetime.now()
                await session.save()
                logger.info(f"会话 {session_id} 已手动结束")
        except Exception as e:
            logger.error(f"手动结束会话异常: {e}")

    async def _cleanup_streaming_session(self, user_id: str):
        """清理流式识别会话"""
        streaming_session = self.manager.get_streaming_session(user_id)
        if streaming_session:
            try:
                await streaming_session.close()
                self.manager.remove_streaming_session(user_id)
                logger.debug(f"用户 {user_id} 流式识别会话已清理")
            except Exception as e:
                logger.error(f"清理流式识别会话失败: {e}")

    async def _cleanup_audio_buffer(self, user_id: str):
        """清理音频缓冲区"""
        if user_id in self.streaming_audio_buffers:
            logger.info(f"清理用户 {user_id} 的音频缓冲区")
            del self.streaming_audio_buffers[user_id]

    def _init_audio_buffer(self, user_id: str):
        """初始化音频缓冲区"""
        import time
        self.streaming_audio_buffers[user_id] = {
            'chunks': [],
            'start_time': time.time(),
            'total_duration': 0.0
        }
        logger.info(f"为用户 {user_id} 初始化音频缓冲区")

    def _add_audio_chunk(self, user_id: str, audio_bytes: bytes):
        """添加音频片段到缓冲区"""
        if user_id not in self.streaming_audio_buffers:
            self._init_audio_buffer(user_id)

        self.streaming_audio_buffers[user_id]['chunks'].append(audio_bytes)
        logger.debug(f"为用户 {user_id} 添加音频片段: {len(audio_bytes)} bytes")

    def _get_merged_audio(self, user_id: str) -> tuple[bytes, float]:
        """获取合并后的完整音频数据"""
        if user_id not in self.streaming_audio_buffers:
            return b"", 0.0

        import time
        buffer = self.streaming_audio_buffers[user_id]
        merged_audio = b"".join(buffer['chunks'])
        duration = time.time() - buffer['start_time']

        logger.info(f"用户 {user_id} 合并音频: {len(merged_audio)} bytes, 时长: {duration:.2f}s")
        return merged_audio, duration

    async def _save_streaming_voice_message(self, user_id: str, session_id: str,
                                          transcription: str, confidence: float) -> ChatMessage:
        """保存流式语音消息（使用文件上传功能）"""
        user = await AIReliefUser.get(user_id=user_id)
        session = await ChatSession.get(session_id=session_id)

        message_id = f"stream_voice_{uuid.uuid4().hex[:16]}"

        # 获取合并后的音频数据
        merged_audio_bytes, duration = self._get_merged_audio(user_id)

        # 处理音频数据 - 使用文件上传功能
        audio_url = None
        audio_file_size = len(merged_audio_bytes)

        if merged_audio_bytes:
            try:
                # 使用音频文件助手保存文件，将PCM数据转换为WAV格式
                audio_url, file_path = await audio_file_helper.save_audio_bytes_to_file(
                    merged_audio_bytes, user_id, "wav",
                    is_pcm_data=True,  # 标记为PCM数据，需要转换
                    sample_rate=16000,  # 火山引擎使用16kHz采样率
                    channels=1  # 单声道
                )
                logger.info(f"流式语音消息文件保存成功: {audio_url}, 大小: {audio_file_size} bytes")
            except Exception as e:
                logger.error(f"保存流式音频文件失败: {e}")
                # 如果文件保存失败，回退到data URL方式
                try:
                    import base64
                    # 对于PCM数据，也需要转换为WAV格式再编码
                    from app.utils.audio_file_helper import AudioFileHelper
                    wav_data = AudioFileHelper.pcm_to_wav(merged_audio_bytes, 16000, 1)
                    audio_data_b64 = base64.b64encode(wav_data).decode('utf-8')
                    audio_url = f"data:audio/wav;base64,{audio_data_b64}"
                    logger.warning(f"回退到data URL方式: {audio_file_size} bytes")
                except Exception as fallback_error:
                    logger.error(f"回退方式也失败: {fallback_error}")

        message = await ChatMessage.create(
            message_id=message_id,
            session=session,
            user=user,
            content=transcription,  # 使用识别文本作为content
            message_type="audio",
            role="user",
            audio_url=audio_url,
            audio_duration=int(duration),
            audio_file_size=audio_file_size,
            transcription_text=transcription,
            transcription_confidence=confidence,
            transcription_status="success"
        )

        # 更新会话消息计数
        session.message_count += 1
        await session.save()

        # 清理音频缓冲区
        await self._cleanup_audio_buffer(user_id)

        return message

    async def _handle_streaming_start(self, user_id: str, session_id: str, message_data: dict):
        """处理开始流式语音识别"""
        try:
            logger.info(f"🎤 开始处理流式语音识别启动 - 用户: {user_id}")

            # 检查是否已有流式会话
            existing_session = self.manager.get_streaming_session(user_id)
            if existing_session:
                logger.info(f"🔄 关闭现有流式会话 - 用户: {user_id}")
                await existing_session.close()
                self.manager.remove_streaming_session(user_id)

            # 创建流式识别会话
            def on_result(result: Dict[str, Any]):
                """处理流式识别结果"""
                logger.info(f"🎯 收到流式识别结果: {result} - 用户: {user_id}")
                asyncio.create_task(self._handle_streaming_result(user_id, session_id, result))

            def on_error(error: str):
                """处理流式识别错误"""
                logger.error(f"❌ 流式识别错误: {error} - 用户: {user_id}")
                asyncio.create_task(self._send_error_message(user_id, session_id, f"流式识别错误: {error}"))

            logger.info(f"🔧 开始创建流式识别会话 - 用户: {user_id}")
            print(f"🔧 开始创建流式识别会话 - 用户: {user_id}")
            try:
                streaming_session = await self.voice_service.create_streaming_session(on_result, on_error)
                logger.info(f"✅ 流式识别会话创建成功，类型: {type(streaming_session).__name__} - 用户: {user_id}")
                print(f"✅ 流式识别会话创建成功，类型: {type(streaming_session).__name__} - 用户: {user_id}")
            except Exception as create_error:
                logger.error(f"❌ 创建流式识别会话失败: {create_error} - 用户: {user_id}")
                print(f"❌ 创建流式识别会话失败: {create_error} - 用户: {user_id}")
                await self._send_error_message(user_id, session_id, f"创建流式识别会话失败: {create_error}")
                return

            # 启动流式识别
            logger.info(f"🚀 开始启动流式识别 - 用户: {user_id}")
            print(f"🚀 开始启动流式识别 - 用户: {user_id}")
            try:
                success = await streaming_session.start()
                logger.info(f"🔍 流式识别启动结果: {success} - 用户: {user_id}")
                print(f"🔍 流式识别启动结果: {success} - 用户: {user_id}")

                if success:
                    self.manager.set_streaming_session(user_id, streaming_session)
                    logger.info(f"✅ 流式识别会话已保存到管理器 - 用户: {user_id}")
                    print(f"✅ 流式识别会话已保存到管理器 - 用户: {user_id}")

                    # 初始化音频缓冲区
                    self._init_audio_buffer(user_id)
                    logger.info(f"✅ 音频缓冲区已初始化 - 用户: {user_id}")
                    print(f"✅ 音频缓冲区已初始化 - 用户: {user_id}")

                    await self._send_system_message(
                        user_id, session_id, "streaming_started",
                        {"message": "流式语音识别已开始"}
                    )
                    logger.info(f"🎉 用户 {user_id} 流式语音识别已完全启动")
                    print(f"🎉 用户 {user_id} 流式语音识别已完全启动")
                else:
                    logger.error(f"❌ 启动流式语音识别失败 - 用户: {user_id}")
                    print(f"❌ 启动流式语音识别失败 - 用户: {user_id}")
                    await self._send_error_message(user_id, session_id, "启动流式语音识别失败")
            except Exception as start_error:
                logger.error(f"❌ 启动流式识别异常: {start_error} - 用户: {user_id}")
                print(f"❌ 启动流式识别异常: {start_error} - 用户: {user_id}")
                await self._send_error_message(user_id, session_id, f"启动流式识别失败: {start_error}")

        except Exception as e:
            logger.error(f"❌ 处理流式识别开始异常: {e} - 用户: {user_id}")
            await self._send_error_message(user_id, session_id, "启动流式识别失败")

    async def _handle_streaming_audio(self, user_id: str, session_id: str, message_data: dict):
        """处理流式音频数据"""
        try:
            logger.info(f"🎵 开始处理流式音频数据 - 用户: {user_id}")
            logger.info(f"🔍 消息数据: {message_data}")

            streaming_session = self.manager.get_streaming_session(user_id)
            logger.info(f"🔍 查找流式识别会话 - 用户: {user_id}, 会话存在: {streaming_session is not None}")

            if not streaming_session:
                logger.error(f"❌ 流式识别会话不存在 - 用户: {user_id}")
                logger.info(f"🔍 当前活跃会话: {list(self.manager.streaming_sessions.keys())}")
                await self._send_error_message(user_id, session_id, "流式识别会话不存在")
                return

            # 从data字段中获取音频数据
            data = message_data.get("data", {})
            audio_data = data.get("audio_data")

            logger.info(f"🔍 音频数据详情: message_data keys={list(message_data.keys())}, data keys={list(data.keys())}, audio_data length={len(audio_data) if audio_data else 0}")

            if not audio_data:
                logger.error(f"❌ 音频数据为空 - 用户: {user_id}")
                await self._send_error_message(user_id, session_id, "音频数据不能为空")
                return

            # 解码base64音频数据
            import base64
            try:
                audio_bytes = base64.b64decode(audio_data)
                logger.info(f"✅ 音频数据解码成功: {len(audio_bytes)} bytes - 用户: {user_id}")
            except Exception as decode_error:
                logger.error(f"❌ 音频数据解码失败: {decode_error} - 用户: {user_id}")
                await self._send_error_message(user_id, session_id, "音频数据格式错误")
                return

            # 添加音频片段到缓冲区（用于后续保存完整音频）
            self._add_audio_chunk(user_id, audio_bytes)

            # 检查音频数据格式并处理
            # 前端现在发送的应该是PCM数据，但我们仍然保留转换逻辑作为备用
            pcm_data = audio_bytes

            # 添加音频数据格式调试信息
            print(f"🎵 音频数据分析 - 用户: {user_id}")
            print(f"  - 数据大小: {len(audio_bytes)} bytes")
            print(f"  - 前16字节: {audio_bytes[:16].hex() if len(audio_bytes) >= 16 else audio_bytes.hex()}")

            # 检查音频数据质量
            if len(audio_bytes) < 512:
                print(f"⚠️ 音频数据过小，可能影响识别效果")

            # 检查是否全为零（静音）
            if all(b == 0 for b in audio_bytes[:100]):
                print(f"⚠️ 检测到静音数据，可能无法识别")

            # 检查是否是WebM格式（通过文件头判断）
            if len(audio_bytes) > 4 and audio_bytes[:4] == b'\x1a\x45\xdf\xa3':
                # 这是WebM格式，需要转换
                print(f"🔄 检测到WebM格式，开始转换...")
                try:
                    pcm_data = await self._convert_to_pcm(audio_bytes)
                    logger.info(f"✅ WebM音频格式转换成功: {len(audio_bytes)} bytes -> {len(pcm_data)} bytes PCM - 用户: {user_id}")
                    print(f"✅ WebM音频格式转换成功: {len(audio_bytes)} bytes -> {len(pcm_data)} bytes PCM")
                except Exception as convert_error:
                    logger.error(f"❌ 音频格式转换失败: {convert_error} - 用户: {user_id}")
                    print(f"❌ 音频格式转换失败: {convert_error}")
                    # 如果转换失败，尝试直接发送原始数据
                    pcm_data = audio_bytes
                    logger.warning(f"⚠️ 使用原始音频数据，可能影响识别效果 - 用户: {user_id}")
                    print(f"⚠️ 使用原始音频数据，可能影响识别效果")
            else:
                # 假设是PCM数据，直接使用
                logger.info(f"✅ 检测到PCM音频数据，直接使用: {len(audio_bytes)} bytes - 用户: {user_id}")
                print(f"✅ 检测到PCM音频数据，直接使用: {len(audio_bytes)} bytes")
                print(f"  - PCM数据前16字节: {pcm_data[:16].hex() if len(pcm_data) >= 16 else pcm_data.hex()}")

                # 计算音频能量（简单的音量检测）
                if len(pcm_data) >= 100:
                    # 将字节转换为16位整数并计算RMS
                    import struct
                    samples = struct.unpack('<' + 'h' * (len(pcm_data[:100]) // 2), pcm_data[:100])
                    rms = (sum(s*s for s in samples) / len(samples)) ** 0.5
                    print(f"  - 音频能量(RMS): {rms:.2f}")
                    if rms < 100:
                        print(f"⚠️ 音频能量较低，可能是静音或音量过小")

            # 发送音频数据到流式识别服务
            logger.info(f"📤 发送音频数据到流式识别服务 - 用户: {user_id}, 数据大小: {len(pcm_data)} bytes")
            print(f"📤 发送音频数据到流式识别服务 - 用户: {user_id}, 数据大小: {len(pcm_data)} bytes")
            success = await streaming_session.send_audio(pcm_data)
            if not success:
                logger.error(f"❌ 发送音频数据失败 - 用户: {user_id}")
                print(f"❌ 发送音频数据失败 - 用户: {user_id}")
                await self._send_error_message(user_id, session_id, "发送音频数据失败")
            else:
                logger.info(f"✅ 音频数据发送成功 - 用户: {user_id}")
                # 只在前几次发送时打印，避免日志过多
                if hasattr(self, '_audio_send_count'):
                    self._audio_send_count += 1
                else:
                    self._audio_send_count = 1

                if self._audio_send_count <= 5 or self._audio_send_count % 20 == 0:
                    print(f"✅ 音频数据发送成功 - 用户: {user_id} (第{self._audio_send_count}次)")

        except Exception as e:
            logger.error(f"❌ 处理流式音频数据异常: {e} - 用户: {user_id}")
            await self._send_error_message(user_id, session_id, "处理音频数据失败")

    async def _handle_streaming_end(self, user_id: str, session_id: str, message_data: dict):
        """处理结束流式语音识别"""
        try:
            streaming_session = self.manager.get_streaming_session(user_id)
            if not streaming_session:
                await self._send_error_message(user_id, session_id, "流式识别会话不存在")
                return

            # 结束流式识别
            success = await streaming_session.end()
            if success:
                await self._send_system_message(
                    user_id, session_id, "streaming_ended",
                    {"message": "流式语音识别已结束"}
                )
                logger.info(f"用户 {user_id} 流式语音识别已结束")

            # 清理流式会话
            await streaming_session.close()
            self.manager.remove_streaming_session(user_id)

        except Exception as e:
            logger.error(f"处理流式识别结束失败: {e}")
            await self._send_error_message(user_id, session_id, "结束流式识别失败")

    async def _handle_streaming_result(self, user_id: str, session_id: str, result: Dict[str, Any]):
        """处理流式识别结果"""
        try:
            logger.info(f"🎯 处理流式识别结果 - 用户: {user_id}, 结果: {result}")
            print(f"🎯 处理流式识别结果 - 用户: {user_id}, 结果: {result}")

            # 发送识别结果给前端
            await self._send_system_message(
                user_id, session_id, "streaming_result", result
            )
            print(f"📤 已发送流式识别结果到前端 - 用户: {user_id}")

            # 如果是最终结果，处理AI对话
            if result.get("is_final") and result.get("text", "").strip():
                transcription = result["text"].strip()
                logger.info(f"流式识别最终结果: {transcription}")
                print(f"🎉 流式识别最终结果: {transcription} - 用户: {user_id}")

                # 保存流式语音消息（包含完整音频数据）
                user_message = await self._save_streaming_voice_message(
                    user_id, session_id, transcription, result.get("confidence", 0.0)
                )

                # 发送语音消息确认
                await self._send_voice_message_confirmation(user_id, session_id, user_message)

                # 发送AI正在输入状态
                await self._send_typing_status(user_id, session_id, True)

                try:
                    # 使用识别文本调用AI服务
                    ai_response = await self.ai_service.send_message(user_id, session_id, transcription)

                    # 检查AI是否返回了结束标志
                    if ai_response.get("session_ended"):
                        await self._end_session_by_ai(user_id, session_id)

                    # 保存AI回复
                    ai_message = await self._save_message(
                        user_id, session_id, ai_response["content"], "text", "assistant",
                        external_data=ai_response
                    )

                    # 发送AI回复
                    await self._send_ai_message(user_id, session_id, ai_message)

                except Exception as e:
                    logger.error(f"处理AI回复失败: {e}")
                    print(f"❌ 处理AI回复失败: {e} - 用户: {user_id}")
                    await self._send_error_message(user_id, session_id, "AI回复处理失败")
                finally:
                    await self._send_typing_status(user_id, session_id, False)

        except Exception as e:
            logger.error(f"处理流式识别结果失败: {e}")
            print(f"❌ 处理流式识别结果失败: {e} - 用户: {user_id}")
            await self._send_error_message(user_id, session_id, "处理识别结果失败")

    async def _convert_to_pcm(self, audio_bytes: bytes) -> bytes:
        """
        将WebM音频数据转换为PCM格式
        火山引擎需要：16kHz, 16bit, 单声道, raw PCM
        """
        try:
            # 临时解决方案：使用ffmpeg进行音频转换
            import tempfile
            import subprocess
            import os

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as input_file:
                input_file.write(audio_bytes)
                input_path = input_file.name

            with tempfile.NamedTemporaryFile(suffix='.raw', delete=False) as output_file:
                output_path = output_file.name

            try:
                # 使用ffmpeg转换音频格式
                # -f s16le: 16位小端格式
                # -ar 16000: 采样率16kHz
                # -ac 1: 单声道
                cmd = [
                    'ffmpeg', '-y',  # -y: 覆盖输出文件
                    '-i', input_path,  # 输入文件
                    '-f', 's16le',     # 输出格式：16位小端PCM
                    '-ar', '16000',    # 采样率：16kHz
                    '-ac', '1',        # 声道数：单声道
                    '-loglevel', 'error',  # 只显示错误日志
                    output_path        # 输出文件
                ]

                # 执行转换
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                if result.returncode != 0:
                    logger.error(f"ffmpeg转换失败: {result.stderr}")
                    raise Exception(f"音频转换失败: {result.stderr}")

                # 读取转换后的PCM数据
                with open(output_path, 'rb') as f:
                    pcm_data = f.read()

                logger.info(f"音频转换成功: {len(audio_bytes)} bytes -> {len(pcm_data)} bytes PCM")
                return pcm_data

            finally:
                # 清理临时文件
                try:
                    os.unlink(input_path)
                    os.unlink(output_path)
                except:
                    pass

        except subprocess.TimeoutExpired:
            logger.error("音频转换超时")
            raise Exception("音频转换超时")
        except FileNotFoundError:
            logger.error("ffmpeg未安装，无法进行音频转换")
            raise Exception("音频转换工具未安装")
        except Exception as e:
            logger.error(f"音频转换异常: {e}")
            raise Exception(f"音频转换失败: {str(e)}")


# 全局WebSocket聊天服务实例
websocket_chat_service = WebSocketChatService()
