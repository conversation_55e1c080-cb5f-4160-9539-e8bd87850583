"""
音频文件处理辅助工具
用于流式语音识别的音频文件保存和管理
"""
import os
import uuid
import base64
from typing import Tuple, Optional
from app.settings.config import settings
from app.utils.file_upload import file_upload_manager
import logging

logger = logging.getLogger(__name__)


class AudioFileHelper:
    """音频文件处理辅助类"""
    
    @staticmethod
    async def save_audio_bytes_to_file(
        audio_bytes: bytes, 
        user_id: str,
        file_format: str = "webm"
    ) -> Tuple[str, str]:
        """
        将音频字节数据保存为文件
        
        Args:
            audio_bytes: 音频字节数据
            user_id: 用户ID
            file_format: 音频格式，默认webm
            
        Returns:
            Tuple[str, str]: (文件访问URL, 本地文件路径)
        """
        try:
            # 确保音频目录存在
            os.makedirs(settings.UPLOAD_AUDIO_DIR, exist_ok=True)
            
            # 生成唯一文件名
            timestamp = int(uuid.uuid1().time)
            unique_id = uuid.uuid4().hex[:16]
            filename = f"stream_audio_{timestamp}_{unique_id}.{file_format}"
            
            # 构建文件路径
            file_path = os.path.join(settings.UPLOAD_AUDIO_DIR, filename)
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(audio_bytes)
            
            # 生成访问URL
            file_url = f"{settings.UPLOAD_BASE_URL}/api/v1/airelief/files/audio/{filename}"
            
            logger.info(f"音频文件保存成功: {filename}, 大小: {len(audio_bytes)} bytes, 用户: {user_id}")
            
            return file_url, file_path
            
        except Exception as e:
            logger.error(f"保存音频文件失败: {e}")
            raise Exception(f"保存音频文件失败: {str(e)}")
    
    @staticmethod
    async def save_base64_audio_to_file(
        audio_base64: str,
        user_id: str,
        file_format: str = "webm"
    ) -> Tuple[str, str]:
        """
        将base64编码的音频数据保存为文件
        
        Args:
            audio_base64: base64编码的音频数据
            user_id: 用户ID
            file_format: 音频格式，默认webm
            
        Returns:
            Tuple[str, str]: (文件访问URL, 本地文件路径)
        """
        try:
            # 解码base64数据
            audio_bytes = base64.b64decode(audio_base64)
            
            # 调用字节数据保存方法
            return await AudioFileHelper.save_audio_bytes_to_file(
                audio_bytes, user_id, file_format
            )
            
        except Exception as e:
            logger.error(f"保存base64音频文件失败: {e}")
            raise Exception(f"保存base64音频文件失败: {str(e)}")
    
    @staticmethod
    def get_audio_file_info(file_path: str) -> dict:
        """
        获取音频文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 文件信息
        """
        try:
            if not os.path.exists(file_path):
                return {}
            
            file_size = os.path.getsize(file_path)
            filename = os.path.basename(file_path)
            
            # 获取MIME类型
            import mimetypes
            mime_type, _ = mimetypes.guess_type(file_path)
            
            return {
                "filename": filename,
                "file_path": file_path,
                "file_size": file_size,
                "mime_type": mime_type or "audio/webm"
            }
            
        except Exception as e:
            logger.error(f"获取音频文件信息失败: {e}")
            return {}
    
    @staticmethod
    def delete_audio_file(file_path: str) -> bool:
        """
        删除音频文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"音频文件删除成功: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除音频文件失败: {e}")
            return False
    
    @staticmethod
    def cleanup_old_audio_files(max_age_hours: int = 24) -> int:
        """
        清理旧的音频文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
            
        Returns:
            int: 清理的文件数量
        """
        try:
            import time
            
            if not os.path.exists(settings.UPLOAD_AUDIO_DIR):
                return 0
            
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            cleaned_count = 0
            
            for filename in os.listdir(settings.UPLOAD_AUDIO_DIR):
                file_path = os.path.join(settings.UPLOAD_AUDIO_DIR, filename)
                
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    
                    if file_age > max_age_seconds:
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                            logger.info(f"清理旧音频文件: {filename}")
                        except Exception as e:
                            logger.error(f"清理文件失败 {filename}: {e}")
            
            logger.info(f"音频文件清理完成，共清理 {cleaned_count} 个文件")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理音频文件失败: {e}")
            return 0


# 全局音频文件助手实例
audio_file_helper = AudioFileHelper()
