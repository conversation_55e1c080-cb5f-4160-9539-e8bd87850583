"""
音频文件处理辅助工具
用于流式语音识别的音频文件保存和管理
"""
import os
import uuid
import base64
import struct
from typing import Tuple, Optional
from app.settings.config import settings
from app.utils.file_upload import file_upload_manager
import logging

logger = logging.getLogger(__name__)


class AudioFileHelper:
    """音频文件处理辅助类"""

    @staticmethod
    def pcm_to_wav(pcm_data: bytes, sample_rate: int = 16000, channels: int = 1, bits_per_sample: int = 16) -> bytes:
        """
        将PCM数据转换为WAV格式

        Args:
            pcm_data: PCM音频数据
            sample_rate: 采样率，默认16000Hz
            channels: 声道数，默认1（单声道）
            bits_per_sample: 位深度，默认16位

        Returns:
            bytes: WAV格式的音频数据
        """
        # WAV文件头部信息
        byte_rate = sample_rate * channels * bits_per_sample // 8
        block_align = channels * bits_per_sample // 8
        data_size = len(pcm_data)
        file_size = 36 + data_size

        # 构建WAV头部
        wav_header = struct.pack('<4sI4s4sIHHIIHH4sI',
            b'RIFF',           # ChunkID
            file_size,         # ChunkSize
            b'WAVE',           # Format
            b'fmt ',           # Subchunk1ID
            16,                # Subchunk1Size (PCM)
            1,                 # AudioFormat (PCM)
            channels,          # NumChannels
            sample_rate,       # SampleRate
            byte_rate,         # ByteRate
            block_align,       # BlockAlign
            bits_per_sample,   # BitsPerSample
            b'data',           # Subchunk2ID
            data_size          # Subchunk2Size
        )

        # 合并头部和数据
        wav_data = wav_header + pcm_data
        logger.info(f"PCM转WAV完成: PCM {len(pcm_data)} bytes -> WAV {len(wav_data)} bytes")
        return wav_data

    @staticmethod
    async def save_audio_bytes_to_file(
        audio_bytes: bytes,
        user_id: str,
        file_format: str = "wav",
        is_pcm_data: bool = False,
        sample_rate: int = 16000,
        channels: int = 1
    ) -> Tuple[str, str]:
        """
        将音频字节数据保存为文件

        Args:
            audio_bytes: 音频字节数据
            user_id: 用户ID
            file_format: 音频格式，默认wav
            is_pcm_data: 是否为PCM原始数据，如果是则转换为WAV格式
            sample_rate: 采样率（仅在is_pcm_data=True时使用）
            channels: 声道数（仅在is_pcm_data=True时使用）

        Returns:
            Tuple[str, str]: (文件访问URL, 本地文件路径)
        """
        try:
            # 确保音频目录存在
            os.makedirs(settings.UPLOAD_AUDIO_DIR, exist_ok=True)

            # 如果是PCM数据，转换为WAV格式
            if is_pcm_data:
                logger.info(f"检测到PCM数据，转换为WAV格式: {len(audio_bytes)} bytes")
                audio_bytes = AudioFileHelper.pcm_to_wav(audio_bytes, sample_rate, channels)
                file_format = "wav"  # 强制使用WAV格式

            # 生成唯一文件名
            timestamp = int(uuid.uuid1().time)
            unique_id = uuid.uuid4().hex[:16]
            filename = f"stream_audio_{timestamp}_{unique_id}.{file_format}"

            # 构建文件路径
            file_path = os.path.join(settings.UPLOAD_AUDIO_DIR, filename)

            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(audio_bytes)

            # 生成访问URL
            file_url = f"{settings.UPLOAD_BASE_URL}/api/v1/airelief/files/audio/{filename}"

            logger.info(f"音频文件保存成功: {filename}, 大小: {len(audio_bytes)} bytes, 用户: {user_id}")

            return file_url, file_path

        except Exception as e:
            logger.error(f"保存音频文件失败: {e}")
            raise Exception(f"保存音频文件失败: {str(e)}")
    
    @staticmethod
    async def save_base64_audio_to_file(
        audio_base64: str,
        user_id: str,
        file_format: str = "webm"
    ) -> Tuple[str, str]:
        """
        将base64编码的音频数据保存为文件
        
        Args:
            audio_base64: base64编码的音频数据
            user_id: 用户ID
            file_format: 音频格式，默认webm
            
        Returns:
            Tuple[str, str]: (文件访问URL, 本地文件路径)
        """
        try:
            # 解码base64数据
            audio_bytes = base64.b64decode(audio_base64)
            
            # 调用字节数据保存方法
            return await AudioFileHelper.save_audio_bytes_to_file(
                audio_bytes, user_id, file_format
            )
            
        except Exception as e:
            logger.error(f"保存base64音频文件失败: {e}")
            raise Exception(f"保存base64音频文件失败: {str(e)}")
    
    @staticmethod
    def get_audio_file_info(file_path: str) -> dict:
        """
        获取音频文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 文件信息
        """
        try:
            if not os.path.exists(file_path):
                return {}
            
            file_size = os.path.getsize(file_path)
            filename = os.path.basename(file_path)
            
            # 获取MIME类型
            import mimetypes
            mime_type, _ = mimetypes.guess_type(file_path)
            
            return {
                "filename": filename,
                "file_path": file_path,
                "file_size": file_size,
                "mime_type": mime_type or "audio/webm"
            }
            
        except Exception as e:
            logger.error(f"获取音频文件信息失败: {e}")
            return {}
    
    @staticmethod
    def delete_audio_file(file_path: str) -> bool:
        """
        删除音频文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"音频文件删除成功: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除音频文件失败: {e}")
            return False
    
    @staticmethod
    def cleanup_old_audio_files(max_age_hours: int = 24) -> int:
        """
        清理旧的音频文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
            
        Returns:
            int: 清理的文件数量
        """
        try:
            import time
            
            if not os.path.exists(settings.UPLOAD_AUDIO_DIR):
                return 0
            
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            cleaned_count = 0
            
            for filename in os.listdir(settings.UPLOAD_AUDIO_DIR):
                file_path = os.path.join(settings.UPLOAD_AUDIO_DIR, filename)
                
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    
                    if file_age > max_age_seconds:
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                            logger.info(f"清理旧音频文件: {filename}")
                        except Exception as e:
                            logger.error(f"清理文件失败 {filename}: {e}")
            
            logger.info(f"音频文件清理完成，共清理 {cleaned_count} 个文件")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理音频文件失败: {e}")
            return 0


# 全局音频文件助手实例
audio_file_helper = AudioFileHelper()
