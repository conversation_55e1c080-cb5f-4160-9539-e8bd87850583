"""
兑换码生成工具
支持多种生成策略和唯一性验证
"""
import random
import string
import secrets
from typing import Optional, List
from app.models.airelief import Coupon


class CouponGenerator:
    """兑换码生成器"""
    
    # 排除易混淆字符的字符集
    DIGITS = "23456789"  # 排除0和1
    UPPERCASE = "ABCDEFGHJKLMNPQRSTUVWXYZ"  # 排除I和O
    ALPHANUMERIC = DIGITS + UPPERCASE
    
    @classmethod
    async def generate_unique_code(
        cls,
        length: int = 8,
        prefix: Optional[str] = None,
        separator: Optional[str] = None,
        segment_length: int = 4,
        max_attempts: int = 100
    ) -> str:
        """
        生成唯一的兑换码
        
        Args:
            length: 兑换码长度（不包括前缀和分隔符）
            prefix: 前缀（如：VIP2025）
            separator: 分段分隔符（如：-）
            segment_length: 分段长度
            max_attempts: 最大尝试次数
            
        Returns:
            唯一的兑换码字符串
            
        Raises:
            ValueError: 超过最大尝试次数仍无法生成唯一兑换码
        """
        for attempt in range(max_attempts):
            # 生成随机码
            random_code = cls._generate_random_code(length)
            
            # 添加分段
            if separator and segment_length > 0:
                random_code = cls._add_segments(random_code, separator, segment_length)
            
            # 添加前缀
            if prefix:
                code = f"{prefix}{separator or ''}{random_code}"
            else:
                code = random_code
            
            # 检查唯一性
            if not await cls._code_exists(code):
                return code
        
        raise ValueError(f"无法在{max_attempts}次尝试内生成唯一兑换码")
    
    @classmethod
    def _generate_random_code(cls, length: int) -> str:
        """生成指定长度的随机码"""
        return ''.join(secrets.choice(cls.ALPHANUMERIC) for _ in range(length))
    
    @classmethod
    def _add_segments(cls, code: str, separator: str, segment_length: int) -> str:
        """为兑换码添加分段分隔符"""
        if segment_length <= 0:
            return code
        
        segments = []
        for i in range(0, len(code), segment_length):
            segments.append(code[i:i + segment_length])
        
        return separator.join(segments)
    
    @classmethod
    async def _code_exists(cls, code: str) -> bool:
        """检查兑换码是否已存在"""
        return await Coupon.filter(code=code).exists()
    
    @classmethod
    async def batch_generate_codes(
        cls,
        count: int,
        length: int = 8,
        prefix: Optional[str] = None,
        separator: Optional[str] = None,
        segment_length: int = 4
    ) -> List[str]:
        """
        批量生成唯一兑换码
        
        Args:
            count: 生成数量
            length: 兑换码长度
            prefix: 前缀
            separator: 分隔符
            segment_length: 分段长度
            
        Returns:
            兑换码列表
        """
        codes = []
        for _ in range(count):
            code = await cls.generate_unique_code(
                length=length,
                prefix=prefix,
                separator=separator,
                segment_length=segment_length
            )
            codes.append(code)
        
        return codes


# 便捷函数
async def generate_coupon_code(
    length: int = 8,
    prefix: Optional[str] = None,
    use_separator: bool = True
) -> str:
    """
    生成兑换码的便捷函数
    
    Args:
        length: 兑换码长度
        prefix: 前缀
        use_separator: 是否使用分隔符
        
    Returns:
        兑换码字符串
    """
    separator = "-" if use_separator else None
    return await CouponGenerator.generate_unique_code(
        length=length,
        prefix=prefix,
        separator=separator,
        segment_length=4
    )


async def generate_batch_coupon_codes(
    count: int,
    length: int = 8,
    prefix: Optional[str] = None,
    use_separator: bool = True
) -> List[str]:
    """
    批量生成兑换码的便捷函数
    
    Args:
        count: 生成数量
        length: 兑换码长度
        prefix: 前缀
        use_separator: 是否使用分隔符
        
    Returns:
        兑换码列表
    """
    separator = "-" if use_separator else None
    return await CouponGenerator.batch_generate_codes(
        count=count,
        length=length,
        prefix=prefix,
        separator=separator,
        segment_length=4
    ) 