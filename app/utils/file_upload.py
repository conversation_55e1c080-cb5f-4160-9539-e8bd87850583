"""
文件上传工具模块
提供通用的文件上传、验证、存储功能
"""
import os
import uuid
import mimetypes
from typing import Optional, Tuple, Dict, Any
from pathlib import Path
from fastapi import UploadFile, HTTPException
from app.settings.config import settings
import logging

logger = logging.getLogger(__name__)


class FileUploadManager:
    """文件上传管理器"""
    
    def __init__(self):
        self.file_type_configs = {
            "audio": {
                "upload_dir": settings.UPLOAD_AUDIO_DIR,
                "max_size": settings.MAX_AUDIO_FILE_SIZE,
                "allowed_formats": settings.ALLOWED_AUDIO_FORMATS,
                "description": "音频文件"
            },
            "avatar": {
                "upload_dir": settings.UPLOAD_AVATAR_DIR,
                "max_size": settings.MAX_AVATAR_FILE_SIZE,
                "allowed_formats": settings.ALLOWED_AVATAR_FORMATS,
                "description": "头像图片"
            }
        }
        
        # 确保上传目录存在
        self._ensure_upload_directories()
    
    def _ensure_upload_directories(self):
        """确保所有上传目录存在"""
        for file_type, config in self.file_type_configs.items():
            upload_dir = config["upload_dir"]
            os.makedirs(upload_dir, exist_ok=True)
            logger.info(f"确保{config['description']}目录存在: {upload_dir}")
    
    def validate_file_type(self, file_type: str) -> Dict[str, Any]:
        """验证文件类型是否支持"""
        if file_type not in self.file_type_configs:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {file_type}。支持的类型: {list(self.file_type_configs.keys())}"
            )
        return self.file_type_configs[file_type]
    
    def validate_file(self, file: UploadFile, file_type: str) -> Tuple[str, int]:
        """
        验证上传的文件
        返回: (文件扩展名, 文件大小)
        """
        config = self.validate_file_type(file_type)
        
        # 检查文件名
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 获取文件扩展名
        file_extension = Path(file.filename).suffix.lower()
        if not file_extension:
            raise HTTPException(status_code=400, detail="文件必须有扩展名")
        
        # 检查文件格式
        if file_extension not in config["allowed_formats"]:
            raise HTTPException(
                status_code=400,
                detail=f"{config['description']}格式不支持。允许的格式: {', '.join(config['allowed_formats'])}"
            )
        
        # 检查文件大小
        file.file.seek(0, 2)  # 移动到文件末尾
        file_size = file.file.tell()
        file.file.seek(0)  # 重置到文件开头
        
        if file_size > config["max_size"]:
            max_size_mb = config["max_size"] / (1024 * 1024)
            raise HTTPException(
                status_code=400,
                detail=f"{config['description']}大小不能超过 {max_size_mb:.1f}MB"
            )
        
        if file_size == 0:
            raise HTTPException(status_code=400, detail="文件不能为空")
        
        return file_extension, file_size
    
    def generate_unique_filename(self, original_filename: str, file_type: str) -> str:
        """生成唯一的文件名"""
        file_extension = Path(original_filename).suffix.lower()
        unique_id = uuid.uuid4().hex[:16]
        timestamp = int(uuid.uuid1().time)
        return f"{file_type}_{timestamp}_{unique_id}{file_extension}"
    
    async def save_file(self, file: UploadFile, file_type: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        保存上传的文件
        返回文件信息字典
        """
        try:
            # 验证文件
            file_extension, file_size = self.validate_file(file, file_type)
            config = self.file_type_configs[file_type]
            
            # 生成唯一文件名
            unique_filename = self.generate_unique_filename(file.filename, file_type)
            
            # 构建文件路径
            file_path = os.path.join(config["upload_dir"], unique_filename)
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            # 生成访问URL
            file_url = f"{settings.UPLOAD_BASE_URL}/api/v1/airelief/files/{file_type}/{unique_filename}"
            
            # 获取MIME类型
            mime_type, _ = mimetypes.guess_type(file_path)
            
            file_info = {
                "filename": unique_filename,
                "original_filename": file.filename,
                "file_path": file_path,
                "file_url": file_url,
                "file_size": file_size,
                "file_type": file_type,
                "mime_type": mime_type or "application/octet-stream",
                "user_id": user_id
            }
            
            logger.info(f"文件保存成功: {file_info}")
            return file_info
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise HTTPException(status_code=500, detail=f"保存文件失败: {str(e)}")
    
    def get_file_path(self, file_type: str, filename: str) -> str:
        """获取文件的完整路径"""
        config = self.validate_file_type(file_type)
        return os.path.join(config["upload_dir"], filename)
    
    def file_exists(self, file_type: str, filename: str) -> bool:
        """检查文件是否存在"""
        file_path = self.get_file_path(file_type, filename)
        return os.path.exists(file_path)
    
    def delete_file(self, file_type: str, filename: str) -> bool:
        """删除文件"""
        try:
            file_path = self.get_file_path(file_type, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"文件删除成功: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False


# 全局文件上传管理器实例
file_upload_manager = FileUploadManager()
