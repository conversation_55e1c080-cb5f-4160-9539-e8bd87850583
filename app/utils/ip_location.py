"""
IP地理位置查询工具
支持根据IP地址获取地理位置信息
"""
import asyncio
import json
import logging
import urllib.request
import urllib.error
from typing import Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class IPLocationService:
    """IP地理位置查询服务"""
    
    def __init__(self):
        self.timeout = 5  # 5秒超时
        self.executor = ThreadPoolExecutor(max_workers=2)
        
    async def get_location_by_ip(self, ip_address: str) -> Optional[str]:
        """
        根据IP地址获取地理位置
        
        Args:
            ip_address: IP地址
            
        Returns:
            格式化的地理位置字符串，如："中国 浙江省 杭州市"
        """
        if not ip_address or ip_address in ['127.0.0.1', 'localhost', '::1']:
            return "本机地址"
            
        # 尝试多个IP查询服务，提高成功率
        services = [
            self._query_ip_api_com,
            self._query_ipapi_co,
        ]
        
        for service in services:
            try:
                location = await service(ip_address)
                if location:
                    logger.info(f"IP地理位置查询成功: {ip_address} -> {location}")
                    return location
            except Exception as e:
                logger.warning(f"IP地理位置查询服务失败 {service.__name__}: {e}")
                continue
                
        logger.error(f"所有IP地理位置查询服务都失败: {ip_address}")
        return None
    
    async def _query_ip_api_com(self, ip_address: str) -> Optional[str]:
        """使用ip-api.com查询IP地理位置（支持中文）"""
        def _sync_request():
            url = f"http://ip-api.com/json/{ip_address}?lang=zh-CN"
            try:
                with urllib.request.urlopen(url, timeout=self.timeout) as response:
                    if response.status == 200:
                        data = json.loads(response.read().decode('utf-8'))
                        if data.get('status') == 'success':
                            country = data.get('country', '')
                            region = data.get('regionName', '')
                            city = data.get('city', '')
                            
                            # 格式化地理位置
                            location_parts = [part for part in [country, region, city] if part]
                            if location_parts:
                                return ' '.join(location_parts)
            except Exception as e:
                logger.debug(f"ip-api.com查询失败: {e}")
            return None
        
        # 在线程池中执行同步请求
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _sync_request)
    
    async def _query_ipapi_co(self, ip_address: str) -> Optional[str]:
        """使用ipapi.co查询IP地理位置"""
        def _sync_request():
            url = f"https://ipapi.co/{ip_address}/json/"
            try:
                with urllib.request.urlopen(url, timeout=self.timeout) as response:
                    if response.status == 200:
                        data = json.loads(response.read().decode('utf-8'))
                        if not data.get('error'):
                            country = data.get('country_name', '')
                            region = data.get('region', '')
                            city = data.get('city', '')
                            
                            # 格式化地理位置
                            location_parts = [part for part in [country, region, city] if part]
                            if location_parts:
                                return ' '.join(location_parts)
            except Exception as e:
                logger.debug(f"ipapi.co查询失败: {e}")
            return None
        
        # 在线程池中执行同步请求
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _sync_request)


# 全局实例
ip_location_service = IPLocationService()


async def get_ip_location(ip_address: str) -> Optional[str]:
    """
    获取IP地址的地理位置
    
    Args:
        ip_address: IP地址
        
    Returns:
        地理位置字符串或None
    """
    return await ip_location_service.get_location_by_ip(ip_address) 