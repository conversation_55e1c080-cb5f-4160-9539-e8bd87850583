import json
import random
from datetime import datetime, timedelta
from typing import Dict, Any
from loguru import logger
import httpx

from app.settings.config import settings


class SMSService:
    """短信服务类 - 基于028lk.com API"""
    
    def __init__(self):
        self.api_url = settings.SMS_API_URL
        self.secret_name = settings.SMS_SECRET_NAME
        self.secret_key = settings.SMS_SECRET_KEY
        self.sign_name = settings.SMS_SIGN_NAME
        self.code_expire_minutes = settings.SMS_CODE_EXPIRE_MINUTES
        self.code_length = settings.SMS_CODE_LENGTH
        
        # 内存存储验证码（生产环境建议使用Redis）
        self._verification_codes: Dict[str, Dict[str, Any]] = {}
    
    def generate_verification_code(self) -> str:
        """生成验证码"""
        return ''.join([str(random.randint(0, 9)) for _ in range(self.code_length)])
    
    def _clean_expired_codes(self):
        """清理过期的验证码"""
        current_time = datetime.now()
        expired_phones = []
        
        for phone, code_info in self._verification_codes.items():
            if current_time > code_info['expires_at']:
                expired_phones.append(phone)
        
        for phone in expired_phones:
            del self._verification_codes[phone]
            logger.info(f"清理过期验证码: {phone}")
    
    def store_verification_code(self, phone: str, code: str) -> None:
        """存储验证码"""
        self._clean_expired_codes()
        
        expires_at = datetime.now() + timedelta(minutes=self.code_expire_minutes)
        self._verification_codes[phone] = {
            'code': code,
            'created_at': datetime.now(),
            'expires_at': expires_at,
            'attempts': 0  # 验证尝试次数
        }
        
        logger.info(f"存储验证码: {phone}, 过期时间: {expires_at}")
    
    def verify_code(self, phone: str, code: str) -> Dict[str, Any]:
        """验证验证码"""
        self._clean_expired_codes()
        
        if phone not in self._verification_codes:
            return {
                'valid': False,
                'message': '验证码不存在或已过期'
            }
        
        code_info = self._verification_codes[phone]
        
        # 检查尝试次数（防止暴力破解）
        if code_info['attempts'] >= 5:
            del self._verification_codes[phone]
            return {
                'valid': False,
                'message': '验证码尝试次数过多，请重新获取'
            }
        
        # 增加尝试次数
        code_info['attempts'] += 1
        
        # 验证码码
        if code_info['code'] != code:
            return {
                'valid': False,
                'message': '验证码错误'
            }
        
        # 验证成功，删除验证码
        del self._verification_codes[phone]
        
        return {
            'valid': True,
            'message': '验证码验证成功'
        }

    def get_send_cooldown(self, phone: str) -> int:
        """获取验证码发送冷却时间（秒）"""
        self._clean_expired_codes()

        if phone not in self._verification_codes:
            return 0

        code_info = self._verification_codes[phone]
        created_at = code_info['created_at']

        # 计算距离上次发送的时间间隔（秒）
        time_diff = (datetime.now() - created_at).total_seconds()

        # 设置最小发送间隔为60秒
        min_interval = 60
        if time_diff < min_interval:
            return int(min_interval - time_diff)

        return 0
    
    async def send_verification_code(self, phone: str) -> Dict[str, Any]:
        """发送验证码短信"""
        try:
            # 清理过期验证码
            self._clean_expired_codes()

            # 检查是否已有未过期的验证码
            if phone in self._verification_codes:
                code_info = self._verification_codes[phone]
                created_at = code_info['created_at']

                # 计算距离上次发送的时间间隔（秒）
                time_diff = (datetime.now() - created_at).total_seconds()

                # 设置最小发送间隔为60秒，防止频繁发送
                min_interval = 60
                if time_diff < min_interval:
                    remaining_time = int(min_interval - time_diff)
                    logger.warning(f"验证码发送过于频繁: {phone}, 剩余等待时间: {remaining_time}秒")
                    return {
                        'success': False,
                        'message': f'验证码发送过于频繁，请{remaining_time}秒后重试'
                    }

            # 生成验证码
            code = self.generate_verification_code()

            # 构造短信内容（不包含签名，让API自动添加已绑定的签名）
            content = f"您的验证码是{code}，有效期{self.code_expire_minutes}分钟。"

            # 发送短信
            result = await self._send_sms(phone, content)

            if result['success']:
                # 存储验证码
                self.store_verification_code(phone, code)

                logger.info(f"验证码发送成功: {phone}, code: {code}")

                return {
                    'success': True,
                    'message': '验证码发送成功',
                    'data': {
                        'batch_id': result.get('batch_id'),
                        'expires_in': self.code_expire_minutes * 60  # 秒
                    }
                }
            else:
                logger.error(f"验证码发送失败: {phone}, error: {result['message']}")
                return {
                    'success': False,
                    'message': f"验证码发送失败: {result['message']}"
                }

        except Exception as e:
            logger.error(f"发送验证码异常: {phone}, error: {e}")
            return {
                'success': False,
                'message': '验证码发送失败，请稍后重试'
            }
    
    async def _send_sms(self, phone: str, content: str) -> Dict[str, Any]:
        """发送短信的底层方法"""
        try:
            # 构造请求数据（不包含SignName字段，避免与内容中的签名冲突）
            data = {
                "SecretName": self.secret_name,
                "SecretKey": self.secret_key,
                "Mobile": phone,
                "Content": content,
                "TemplateId": "",
                "ExtCode": "",
                "Timing": "",
                "CustomId": ""
            }

            # 将数据转换为JSON字符串并编码为UTF-8
            post_data = json.dumps(data, ensure_ascii=False).encode('utf-8')

            headers = {
                'Content-Type': 'application/json; charset=utf-8',
                'Connection': 'Close'
            }

            # 发送HTTP请求（使用content参数发送原始数据）
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.api_url,
                    content=post_data,
                    headers=headers
                )

                response_text = response.text
                logger.info(f"短信API响应: {response.status_code}, {response_text}")

                if response.status_code == 200:
                    try:
                        result = response.json()

                        if result.get('code') == 0:
                            return {
                                'success': True,
                                'batch_id': result.get('data'),
                                'message': '短信发送成功'
                            }
                        else:
                            return {
                                'success': False,
                                'message': result.get('msg', '短信发送失败')
                            }
                    except json.JSONDecodeError:
                        return {
                            'success': False,
                            'message': '短信API响应格式错误'
                        }
                else:
                    return {
                        'success': False,
                        'message': f'短信API请求失败: HTTP {response.status_code}'
                    }
                        
        except httpx.TimeoutException:
            return {
                'success': False,
                'message': '短信发送超时'
            }
        except httpx.RequestError as e:
            logger.error(f"短信发送请求异常: {e}")
            return {
                'success': False,
                'message': '短信发送网络异常'
            }
        except Exception as e:
            logger.error(f"短信发送异常: {e}")
            return {
                'success': False,
                'message': f'短信发送异常: {str(e)}'
            }
    
    async def send_custom_sms(self, phone: str, content: str) -> Dict[str, Any]:
        """发送自定义内容短信"""
        try:
            # 如果内容不包含签名，自动添加签名
            if not content.startswith("【") and not content.startswith("["):
                content = f"【AIRelief】{content}"

            result = await self._send_sms(phone, content)
            
            if result['success']:
                logger.info(f"自定义短信发送成功: {phone}")
                return {
                    'success': True,
                    'message': '短信发送成功',
                    'data': {
                        'batch_id': result.get('batch_id')
                    }
                }
            else:
                logger.error(f"自定义短信发送失败: {phone}, error: {result['message']}")
                return {
                    'success': False,
                    'message': f"短信发送失败: {result['message']}"
                }
                
        except Exception as e:
            logger.error(f"发送自定义短信异常: {phone}, error: {e}")
            return {
                'success': False,
                'message': '短信发送失败，请稍后重试'
            }


# 全局实例
sms_service = SMSService()
