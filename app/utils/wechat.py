import asyncio
import uuid
import time
from typing import Optional, Dict, Any
from loguru import logger

from wechatpy.oauth import WeChatOAuth
from wechatpy.pay import WeChatPay as WeChatPaySDK
from wechatpy.exceptions import WeChatException
from wechatpy.client import WeChatClient
from wechatpy.utils import WeChatSigner

from app.settings.config import settings


class WeChatAuth:
    """微信网页授权工具类 - 基于 wechatpy"""

    def __init__(self):
        self.app_id = settings.WECHAT_APP_ID
        self.app_secret = settings.WECHAT_APP_SECRET
        self.redirect_uri = settings.WECHAT_REDIRECT_URI

        # 初始化 wechatpy OAuth 客户端
        self.oauth_client = WeChatOAuth(
            app_id=self.app_id,
            secret=self.app_secret,
            redirect_uri=self.redirect_uri
        )
        
    def get_auth_url(self, scope: str = None, state: str = None) -> str:
        """
        生成微信授权URL

        Args:
            scope: 应用授权作用域，如果为None则使用配置文件中的默认值
            state: 重定向后会带上state参数

        Returns:
            授权URL
        """
        if not scope:
            scope = settings.WECHAT_AUTH_SCOPE

        if not state:
            state = str(uuid.uuid4())

        # 更新 OAuth 客户端的参数
        self.oauth_client.scope = scope
        self.oauth_client.state = state

        return self.oauth_client.authorize_url
    
    async def get_access_token(self, code: str) -> Optional[Dict[str, Any]]:
        """
        通过code换取网页授权access_token
        
        Args:
            code: 微信授权码
        
        Returns:
            包含access_token、refresh_token、openid等信息的字典
        """
        try:
            # wechatpy 的 fetch_access_token 是同步方法，需要在线程池中运行
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                self.oauth_client.fetch_access_token, 
                code
            )
            return result
        except WeChatException as e:
            logger.error(f"获取access_token失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取access_token异常: {e}")
            return None
    
    async def get_user_info(self, access_token: str, openid: str) -> Optional[Dict[str, Any]]:
        """
        拉取用户信息
        
        Args:
            access_token: 网页授权接口调用凭证
            openid: 用户的唯一标识
        
        Returns:
            用户信息字典
        """
        try:
            # wechatpy 的 get_user_info 是同步方法，需要在线程池中运行
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.oauth_client.get_user_info,
                openid,
                access_token
            )
            return result
        except WeChatException as e:
            logger.error(f"获取用户信息失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取用户信息异常: {e}")
            return None
    
    async def refresh_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        刷新access_token
        
        Args:
            refresh_token: 刷新令牌
        
        Returns:
            新的token信息
        """
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.oauth_client.refresh_access_token,
                refresh_token
            )
            return result
        except WeChatException as e:
            logger.error(f"刷新access_token失败: {e}")
            return None
        except Exception as e:
            logger.error(f"刷新access_token异常: {e}")
            return None
    
    async def check_access_token(self, access_token: str, openid: str) -> bool:
        """
        检验授权凭证（access_token）是否有效
        
        Args:
            access_token: 网页授权接口调用凭证
            openid: 用户的唯一标识
        
        Returns:
            有效返回True，否则False
        """
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.oauth_client.check_access_token,
                openid,
                access_token
            )
            return result
        except Exception as e:
            logger.error(f"检验access_token异常: {e}")
            return False
    
    async def get_jsapi_ticket(self) -> Optional[str]:
        """
        获取jsapi_ticket
        用于生成微信JS-SDK配置
        
        Note: 在生产环境中应该实现access_token和jsapi_ticket的缓存机制
        """
        try:
            # 首先获取JSAPI所需的access_token
            access_token = await self.get_jsapi_access_token()
            if not access_token:
                return None
            
            # 获取jsapi_ticket
            client = WeChatClient(self.app_id, self.app_secret)
            result = client.jsapi.get_jsapi_ticket()
            
            return result.get('ticket')
        except Exception as e:
            logger.error(f"获取jsapi_ticket失败: {e}")
            return None
    
    async def get_jsapi_access_token(self) -> Optional[str]:
        """
        获取JSAPI所需的公众号access_token（用于调用微信API）
        
        Note: 在生产环境中应该实现缓存机制，避免频繁获取
        """
        try:
            client = WeChatClient(self.app_id, self.app_secret)
            token = client.access_token
            return token
        except Exception as e:
            logger.error(f"获取JSAPI access_token失败: {e}")
            return None
    
    async def get_js_sdk_config(self, url: str) -> Optional[Dict[str, Any]]:
        """
        获取微信JS-SDK配置
        
        Args:
            url: 当前页面URL
            
        Returns:
            JS-SDK配置参数
        """
        try:
            import time
            import random
            import string
            from wechatpy.utils import WeChatSigner
            
            # 获取jsapi_ticket
            jsapi_ticket = await self.get_jsapi_ticket()
            if not jsapi_ticket:
                return None
            
            # 生成签名参数
            timestamp = str(int(time.time()))
            noncestr = ''.join(random.choices(string.ascii_letters + string.digits, k=16))
            
            # 生成签名
            sign_params = {
                'jsapi_ticket': jsapi_ticket,
                'noncestr': noncestr,
                'timestamp': timestamp,
                'url': url.split('#')[0]  # 去掉hash部分
            }
            
            signature = WeChatSigner().sign(**sign_params)
            
            config = {
                "appId": self.app_id,
                "timestamp": timestamp,
                "nonceStr": noncestr,
                "signature": signature,
                "jsApiList": [
                    "chooseWXPay",
                    "requestPayment",
                    "checkJsApi",
                    # 录音相关API
                    "startRecord",
                    "stopRecord",
                    "onVoiceRecordEnd",
                    "playVoice",
                    "pauseVoice",
                    "stopVoice",
                    "onVoicePlayEnd",
                    "uploadVoice",
                    "downloadVoice",
                    "translateVoice"
                ]
            }
            
            return config
            
        except Exception as e:
            logger.error(f"生成JS-SDK配置失败: {e}")
            return None


class WeChatPay:
    """微信支付工具类 - 基于 wechatpy"""
    
    def __init__(self):
        self.app_id = settings.WECHAT_APP_ID
        self.merchant_id = settings.WECHAT_PAY_MCHID
        self.key = settings.WECHAT_PAY_APIV3_KEY
        
        # 初始化 wechatpy 支付客户端
        self.pay_client = WeChatPaySDK(
            appid=self.app_id,
            api_key=self.key,
            mch_id=self.merchant_id,
            # 如果有证书文件，可以添加
            # mch_cert=settings.WECHAT_PAYMENT_CERT_PATH,
            # mch_key=settings.WECHAT_PAYMENT_KEY_PATH,
        )
        
    def generate_order_id(self) -> str:
        """生成订单号"""
        return f"ORDER_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8].upper()}"
    
    async def create_order(self, 
                          order_id: str,
                          amount: int,
                          description: str,
                          openid: str,
                          notify_url: str) -> Optional[Dict[str, Any]]:
        """
        创建微信支付订单
        
        Args:
            order_id: 商户订单号
            amount: 金额（分）
            description: 商品描述
            openid: 用户openid
            notify_url: 支付结果通知地址
        
        Returns:
            订单信息
        """
        try:
            # 构造订单参数
            order_data = {
                'body': description,
                'out_trade_no': order_id,
                'total_fee': amount,
                'spbill_create_ip': '127.0.0.1',
                'notify_url': notify_url,
                'trade_type': 'JSAPI',
                'openid': openid
            }
            
            # wechatpy 的统一下单接口是同步的，需要在线程池中运行
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self.pay_client.order.create,
                **order_data
            )
            
            return result
            
        except WeChatException as e:
            logger.error(f"创建微信支付订单失败: {e}")
            return None
        except Exception as e:
            logger.error(f"创建微信支付订单异常: {e}")
            return None
    
    def create_jsapi_params(self, prepay_id: str) -> Dict[str, Any]:
        """
        生成JSAPI支付参数
        
        Args:
            prepay_id: 预支付交易会话标识
        
        Returns:
            JSAPI支付参数
        """
        try:
            # 使用 wechatpy 生成 JSAPI 支付参数
            jsapi_params = self.pay_client.jsapi.get_jsapi_params(prepay_id)
            return jsapi_params
        except Exception as e:
            logger.error(f"生成JSAPI支付参数失败: {e}")
            return {}


# 全局实例
wechat_auth = WeChatAuth()
# 暂时注释掉旧的支付实现，避免与新的 wechatpayv3 冲突
# wechat_pay = WeChatPay()
