# 微信支付证书文件说明

## 概述

此目录用于存放微信支付所需的证书文件。这些文件需要从微信商户平台下载。

## 需要的文件

### 1. apiclient_key.pem
- **说明**: 商户API私钥文件
- **获取方式**: 从微信商户平台下载
- **用途**: 用于API请求签名

### 2. apiclient_cert.pem (可选)
- **说明**: 商户API证书文件
- **获取方式**: 从微信商户平台下载
- **用途**: 用于双向SSL认证

## 如何获取证书文件

1. 登录 [微信商户平台](https://pay.weixin.qq.com/)
2. 进入 "账户中心" -> "API安全"
3. 下载API证书
4. 将下载的文件重命名并放置到此目录

## 配置说明

在 `app/settings/config.py` 中需要配置以下参数：

```python
# 微信支付JSAPI配置
WECHAT_PAY_MCHID: str = "你的商户号"
WECHAT_PAY_APPID: str = "你的应用ID"
WECHAT_PAY_APIV3_KEY: str = "你的APIv3密钥"
WECHAT_PAY_CERT_SERIAL_NO: str = "你的证书序列号"
WECHAT_PAY_PRIVATE_KEY_PATH: str = "certs/apiclient_key.pem"
WECHAT_PAY_CERT_DIR: str = "certs"
WECHAT_PAY_NOTIFY_URL: str = "你的支付回调地址"
```

## 安全注意事项

⚠️ **重要**: 
- 证书文件包含敏感信息，请勿提交到版本控制系统
- 确保文件权限设置正确，只有应用程序可以读取
- 定期更新证书文件

## 文件权限设置

```bash
chmod 600 apiclient_key.pem
chmod 644 apiclient_cert.pem
```

## 测试配置

配置完成后，可以通过以下方式测试：

```python
from app.core.wechat_pay_service import WeChatPayService

service = WeChatPayService()
if service.is_configured:
    print("✅ 微信支付配置成功")
else:
    print("❌ 微信支付配置失败")
```
