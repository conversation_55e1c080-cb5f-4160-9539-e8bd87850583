# 文件上传功能实现文档

## 📋 功能概述

本文档描述了AIRelief后端通用文件上传系统的实现，支持音频文件和头像图片的上传、存储和访问。

## 🏗️ 架构设计

### 核心组件

1. **FileUploadManager** (`app/utils/file_upload.py`)
   - 通用文件上传管理器
   - 文件验证、存储、访问控制

2. **AudioFileHelper** (`app/utils/audio_file_helper.py`)
   - 音频文件专用处理工具
   - 支持字节数据和base64数据保存

3. **Upload API** (`app/api/v1/airelief/upload.py`)
   - RESTful文件上传接口
   - 文件访问和删除接口

## 🔧 配置说明

### 配置文件 (`app/settings/config.py`)

```python
# 文件上传配置
UPLOAD_BASE_DIR: str = os.path.join(BASE_DIR, "upload")  # 文件上传根目录
UPLOAD_AUDIO_DIR: str = os.path.join(UPLOAD_BASE_DIR, "audio")  # 音频文件目录
UPLOAD_AVATAR_DIR: str = os.path.join(UPLOAD_BASE_DIR, "avatar")  # 头像文件目录
UPLOAD_BASE_URL: str = "http://localhost:8000"  # 文件访问基础URL，部署时需要修改

# 文件大小限制（字节）
MAX_AUDIO_FILE_SIZE: int = 10 * 1024 * 1024  # 音频文件最大10MB
MAX_AVATAR_FILE_SIZE: int = 5 * 1024 * 1024   # 头像文件最大5MB

# 允许的文件格式
ALLOWED_AUDIO_FORMATS: typing.List[str] = [".webm", ".wav", ".mp3", ".m4a"]
ALLOWED_AVATAR_FORMATS: typing.List[str] = [".jpg", ".jpeg", ".png", ".gif"]
```

### 部署时配置修改

**重要**: 部署到生产环境时，需要修改 `UPLOAD_BASE_URL` 为实际的域名：

```python
# 开发环境
UPLOAD_BASE_URL: str = "http://localhost:8000"

# 生产环境
UPLOAD_BASE_URL: str = "https://www.airelief.cn"
```

## 📁 目录结构

```
AIRelief-backend/
├── upload/                    # 文件上传根目录
│   ├── audio/                # 音频文件存储目录
│   │   ├── stream_audio_1234567890_abc123.webm
│   │   └── audio_1234567891_def456.wav
│   └── avatar/               # 头像文件存储目录
│       ├── avatar_1234567892_ghi789.jpg
│       └── avatar_1234567893_jkl012.png
├── app/
│   ├── api/v1/airelief/
│   │   └── upload.py         # 文件上传API
│   ├── utils/
│   │   ├── file_upload.py    # 文件上传管理器
│   │   └── audio_file_helper.py  # 音频文件助手
│   └── settings/
│       └── config.py         # 配置文件
└── docs/
    └── FILE_UPLOAD_IMPLEMENTATION.md  # 本文档
```

## 🚀 API接口

### 1. 文件上传接口

**POST** `/api/v1/airelief/upload`

**请求参数:**
- `file`: 文件数据 (multipart/form-data)
- `file_type`: 文件类型 ("audio" 或 "avatar")

**响应示例:**
```json
{
  "success": true,
  "data": {
    "file_url": "http://localhost:8000/api/v1/airelief/files/audio/stream_audio_1234567890_abc123.webm",
    "filename": "stream_audio_1234567890_abc123.webm",
    "original_filename": "recording.webm",
    "file_size": 1024000,
    "file_type": "audio",
    "mime_type": "audio/webm"
  },
  "msg": "文件上传成功"
}
```

### 2. 文件访问接口

**GET** `/api/v1/airelief/files/{file_type}/{filename}`

**示例:**
- 音频文件: `GET /api/v1/airelief/files/audio/stream_audio_1234567890_abc123.webm`
- 头像文件: `GET /api/v1/airelief/files/avatar/avatar_1234567892_ghi789.jpg`

### 3. 文件删除接口

**DELETE** `/api/v1/airelief/files/{file_type}/{filename}`

需要用户认证。

### 4. 获取上传配置

**GET** `/api/v1/airelief/upload/config`

返回各种文件类型的限制和支持格式。

## 🔄 流式语音识别集成

### 修改说明

1. **WebSocket服务集成** (`app/services/websocket_chat_service.py`)
   - 修改 `_save_streaming_voice_message` 方法
   - 使用 `AudioFileHelper` 保存音频文件
   - 返回文件URL而不是data URL

2. **前端消息处理**
   - 语音消息现在包含真实的文件URL
   - 支持音频文件的重复播放和下载

### 工作流程

```
1. 用户开始录音 → 前端发送流式音频数据
2. 后端缓存音频片段 → 进行实时语音识别
3. 识别完成 → 合并音频片段 → 保存为文件
4. 返回语音消息 → 包含文件URL → 前端可播放
```

## 🧪 测试

### 运行测试脚本

```bash
# 确保后端服务运行在 localhost:9999
python test_upload_api.p
```

### 测试内容

1. ✅ 获取上传配置
2. ✅ 音频文件上传
3. ✅ 头像文件上传
4. ✅ 文件访问测试
5. ✅ 无效文件类型处理

## 🔒 安全考虑

### 文件验证

1. **文件类型验证**: 基于文件扩展名和MIME类型
2. **文件大小限制**: 防止大文件攻击
3. **文件名安全**: 生成唯一文件名，防止路径遍历
4. **用户认证**: 上传和删除操作需要用户认证

### 存储安全

1. **目录隔离**: 不同类型文件存储在不同目录
2. **权限控制**: 文件访问通过API控制
3. **定期清理**: 支持旧文件自动清理

## 🚀 部署指南

### 1. 目录权限

```bash
# 确保上传目录有写权限
mkdir -p /path/to/project/upload/audio
mkdir -p /path/to/project/upload/avatar
chmod 755 /path/to/project/upload
chmod 755 /path/to/project/upload/audio
chmod 755 /path/to/project/upload/avatar
```

### 2. Nginx配置

```nginx
# 静态文件服务
location /api/v1/airelief/files/ {
    alias /path/to/project/upload/;
    expires 1d;
    add_header Cache-Control "public, immutable";
}

# 文件上传大小限制
client_max_body_size 10M;
```

### 3. 环境变量

```bash
# 生产环境配置
export UPLOAD_BASE_URL="https://www.airelief.cn"
```

## 🔧 维护

### 定期清理

```python
# 清理24小时前的音频文件
from app.utils.audio_file_helper import audio_file_helper
cleaned_count = audio_file_helper.cleanup_old_audio_files(max_age_hours=24)
```

### 监控

1. **磁盘空间监控**: 定期检查上传目录大小
2. **文件数量监控**: 防止文件数量过多影响性能
3. **上传频率监控**: 检测异常上传行为

## 📝 更新日志

- **v1.0.0**: 初始实现，支持音频和头像文件上传
- **v1.1.0**: 集成流式语音识别，支持音频文件保存
- **v1.2.0**: 添加文件清理和安全增强功能
