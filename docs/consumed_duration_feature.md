# 已陪伴时长功能设计文档

## 概述

基于固定期限的会员制度，我们设计了"已陪伴时长"功能来显示用户从购买会员到现在经过的时间。

**核心原则：**
- 聊天功能不消耗时长
- 会员基于购买时间的固定期限（如30天、90天）
- 已陪伴时长 = 从购买会员到现在经过的时间

## 会员机制

### 1. 会员类型
- 用户购买套餐后，获得固定期限的会员资格
- 在会员期内可以无限制聊天
- 会员到期后无法继续使用

### 2. 字段含义

| 字段名 | 含义 | 计算方式 |
|--------|------|----------|
| `total_duration` | **总购买时长** | 累计购买的所有套餐时长之和 |
| `consumed_duration` | **已陪伴时长** | 总购买时长 - 剩余时长（计算属性） |
| `remaining_duration` | **剩余时长** | 基于到期时间实时计算 |

### 3. 计算逻辑

```python
# 已陪伴时长（计算属性，无需数据库字段）
consumed_duration = total_duration - remaining_duration

# 剩余时长（基于到期时间）
remaining_duration = max(0, expiry_date - current_time)
```

## API 变更

### 1. 用户信息响应

在 `AIReliefUserInfo` schema 中添加新字段：

```json
{
  "totalDuration": 259200,      // 总购买时长(秒) - 3天
  "consumedDuration": 10800,    // 已陪伴时长(秒) - 3小时
  "companionDays": 1,           // 已陪伴天数 - 1天
  "duration": 248400            // 剩余时长(秒) - 约69小时
}
```

### 2. 已陪伴天数计算规则

根据您的需求：
- 已使用时长 > 0：显示 1天
- 已使用时长 > 24小时：显示 2天
- 以此类推...

实现逻辑：
```python
def companion_days(self) -> int:
    if self.consumed_duration <= 0:
        return 0
    # 按24小时为一天，向上取整
    return max(1, (self.consumed_duration + 24 * 60 * 60 - 1) // (24 * 60 * 60))
```

## 使用方法

### 1. 获取用户陪伴信息

```python
user = await AIReliefUser.get(user_id="xxx")

print(f"总购买时长: {user.total_duration}秒")
print(f"已陪伴时长: {user.consumed_duration}秒")  # 计算属性
print(f"已陪伴天数: {user.companion_days}天")      # 计算属性
print(f"剩余时长: {user.remaining_duration}秒")    # 基于到期时间
```

### 2. 会员状态检查

```python
user = await AIReliefUser.get(user_id="xxx")

if user.is_membership_active:
    print("会员有效，可以聊天")
else:
    print("会员已过期，需要续费")
```

## 数据迁移

**无需数据库迁移！**

由于 `consumed_duration` 现在是计算属性，不需要额外的数据库字段。现有的数据结构已经足够支持新功能。

## 前端显示优化

### 1. 已陪伴时长显示

- 使用 `companionDays` 字段显示已陪伴天数
- 统一使用向上取整的天数计算方式

### 2. 示例代码

```typescript
// 前端显示逻辑
const companionText = computed(() => {
  return `已陪伴您${userInfo.companionDays}天`
})
```

## 测试

运行测试脚本验证功能：

```bash
python test_consumed_duration.py
```

测试覆盖：
- 字段添加和计算逻辑
- 时长消耗功能
- API响应格式
- 已陪伴天数计算

## 注意事项

1. **向后兼容性**：现有API保持兼容，只是添加了新字段
2. **数据一致性**：确保 `consumed_duration` 不超过 `total_duration`
3. **实时性**：`remaining_duration` 仍然基于到期时间实时计算
4. **性能**：新增字段为整型，对查询性能影响很小
