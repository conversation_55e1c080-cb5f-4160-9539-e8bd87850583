# 语音识别配置说明

## 概述

AI Relief 支持多种语音识别引擎，可以通过配置灵活切换。所有语音识别都在服务器端进行，确保识别质量和一致性。

## 支持的识别引擎

### 1. 火山引擎 (volcano) - 推荐
- **特点**: 高精度、支持多种音频格式、词级别时间戳
- **适用场景**: 生产环境
- **配置要求**: 需要火山引擎账号和API密钥

### 2. 微信识别 (wechat)
- **特点**: 依赖微信客户端，仅支持微信环境
- **适用场景**: 特殊需求场景
- **注意**: 当前版本已禁用客户端识别，统一使用服务器端

### 3. 禁用识别 (disabled)
- **特点**: 完全关闭语音识别功能
- **适用场景**: 测试环境或不需要语音功能的场景

## 配置方法

### 环境变量配置

在 `.env` 文件中设置：

```bash
# 设置语音识别引擎
VOICE_RECOGNITION_ENGINE=volcano

# 火山引擎配置（当使用volcano引擎时）
VOLCENGINE_APP_ID=your_app_id
VOLCENGINE_ACCESS_KEY=your_access_key
VOLCENGINE_SECRET_KEY=your_secret_key
VOLCENGINE_CLUSTER=volcengine_streaming_common
```

### 配置选项说明

| 配置项 | 可选值 | 说明 |
|--------|--------|------|
| `VOICE_RECOGNITION_ENGINE` | `volcano` | 使用火山引擎（推荐） |
| | `wechat` | 使用微信识别（需客户端支持） |
| | `disabled` | 禁用语音识别 |

## 火山引擎配置

### 1. 获取API密钥

1. 访问 [火山引擎控制台](https://console.volcengine.com/)
2. 开通语音识别服务
3. 获取 APP_ID、ACCESS_KEY、SECRET_KEY

### 2. 配置参数

```bash
VOLCENGINE_APP_ID=**********
VOLCENGINE_ACCESS_KEY=your_access_key
VOLCENGINE_SECRET_KEY=your_secret_key
VOLCENGINE_CLUSTER=volcengine_streaming_common
```

### 3. 支持的音频格式

- WAV (推荐)
- MP3
- M4A
- AMR

## API接口

### 获取语音识别状态

```http
GET /api/v1/airelief/chat/voice-recognition/status
```

**响应示例**:

```json
{
  "success": true,
  "data": {
    "engine": "volcano",
    "configured": true,
    "provider": "volcengine",
    "status": "active",
    "supported_formats": ["wav", "mp3", "m4a"],
    "max_file_size_mb": 10,
    "supported_languages": ["zh-CN", "en-US"],
    "features": {
      "real_time": false,
      "streaming": false,
      "word_timestamps": true,
      "confidence_scores": true,
      "punctuation": true
    }
  }
}
```

## 故障排除

### 1. 识别失败

**问题**: 语音识别返回失败
**解决方案**:
- 检查API密钥配置是否正确
- 确认网络连接正常
- 检查音频格式是否支持

### 2. 配置未生效

**问题**: 修改配置后未生效
**解决方案**:
- 重启应用服务
- 检查环境变量是否正确设置
- 查看应用日志确认配置加载情况

### 3. Mock模式

当火山引擎未配置时，系统会自动使用Mock模式：
- 返回模拟的识别结果
- 用于开发和测试
- 不会产生实际费用

## 最佳实践

1. **生产环境**: 使用 `volcano` 引擎，配置真实的API密钥
2. **开发环境**: 可以使用 `volcano` 的Mock模式或 `disabled`
3. **测试环境**: 建议使用 `disabled` 避免不必要的API调用
4. **监控**: 定期检查语音识别状态API，确保服务正常

## 更新日志

### v1.1.0
- 统一使用服务器端语音识别
- 移除客户端微信语音识别
- 添加配置化引擎选择
- 优化识别流程和错误处理
