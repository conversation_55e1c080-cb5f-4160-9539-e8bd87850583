{"name": "airelief-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:with-check": "vue-tsc -b && vite build", "build:dev-with-check": "vue-tsc -b && vite build --mode development", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write src/", "format:check": "prettier --check src/"}, "dependencies": {"axios": "^1.7.9", "pinia": "^2.3.1", "vant": "^4.9.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.11.0", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^9.32.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.4.2", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^0.27.5", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}