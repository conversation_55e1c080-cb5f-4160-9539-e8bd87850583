export default {
  plugins: {
    /*
    'postcss-px-to-viewport-8-plugin': {
      viewportWidth: 375, // 设计稿宽度，使用标准移动端宽度
      viewportHeight: 667, // 设计稿高度
      unitPrecision: 3, // 转换后的精度，即小数点位数
      viewportUnit: 'vw', // 指定需要转换成的视窗单位，建议使用vw
      selectorBlackList: [
        '.ignore', 
        '.hairlines',
        '.van-', // 排除所有Vant组件
        '.van-toast', // 特别排除Toast组件
        '.van-loading', // 排除Loading组件
        '.van-overlay', // 排除Overlay组件
        '.van-popup' // 排除Popup组件
      ], // 指定不转换为视窗单位的类
      minPixelValue: 1, // 小于或等于1px不转换为视窗单位
      mediaQuery: false, // 不在媒体查询中转换px，保持桌面端样式
      exclude: [
        /node_modules/, // 排除node_modules文件夹下的文件
        /vant/i // 排除vant相关文件
      ], 
      include: [/src/], // 只转换src目录下的文件
      // 关键配置：只在移动端应用转换
      propList: ['*'],
      replace: true,
      filterRule: (rule) => {
        // 如果规则包含桌面端媒体查询，则不转换
        if (rule.parent && rule.parent.params && rule.parent.params.includes('min-width')) {
          return false
        }
        return true
      }
    },
    */
  },
}
