# 模拟数据脚本使用说明

## 概述

这些脚本用于为 AIRelief 后台管理系统创建和管理模拟测试数据，特别是用于测试充值管理页面的功能。

## 脚本列表

### 1. create_mock_orders.py
创建模拟订单数据的主脚本

**功能**：
- 创建模拟用户（15个）
- 创建支付套餐（5个：体验包、周卡、月卡、季度卡、年卡）
- 创建优惠券（4个：折扣券和时长券）
- 创建模拟订单（100个，包含各种状态）

**生成的数据**：
- **用户数据**：包含微信OpenID、昵称、手机号等
- **套餐数据**：不同时长和价格的套餐
- **优惠券数据**：折扣券和时长券
- **订单数据**：包含所有订单状态的完整订单信息

### 2. clean_mock_data.py
清理模拟数据的脚本

**功能**：
- 清理所有优惠券使用记录
- 清理所有订单
- 清理所有优惠券
- 清理所有套餐
- 清理测试用户（只清理昵称以"测试用户"开头的用户）

## 使用方法

### 创建模拟数据

```bash
# 进入项目根目录
cd /Users/<USER>/Documents/ryuCodeLand/AIRelief-backend

# 运行创建脚本
python scripts/create_mock_orders.py
```

### 清理模拟数据

```bash
# 进入项目根目录
cd /Users/<USER>/Documents/ryuCodeLand/AIRelief-backend

# 运行清理脚本
python scripts/clean_mock_data.py
```

## 生成的数据详情

### 用户数据 (15个)
```
- 昵称: 测试用户1, 测试用户2, ...
- 手机号: 随机生成的138开头手机号
- 性别: 随机 (0=未知, 1=女性, 2=男性)
- 剩余时长: 0-30天随机
```

### 套餐数据 (5个)
```
1. 体验包 - 3天 - ¥9.9 (原价¥19.9)
2. 周卡 - 7天 - ¥29.9 (原价¥39.9)
3. 月卡 - 30天 - ¥99.9 (原价¥129.9)
4. 季度卡 - 90天 - ¥269.9 (原价¥389.9)
5. 年卡 - 365天 - ¥999.9 (原价¥1299.9)
```

### 优惠券数据 (4个)
```
1. WELCOME10 - 折扣券 - ¥10优惠 - 最大使用100次
2. SAVE20 - 折扣券 - ¥20优惠 - 最大使用50次
3. VIP50 - 折扣券 - ¥50优惠 - 最大使用20次
4. DURATION7 - 时长券 - 7天时长 - 最大使用30次
```

### 订单数据 (100个)
**状态分布**：
- 待支付 (pending): 10%
- 已支付 (paid): 70%
- 支付失败 (failed): 5%
- 已取消 (cancelled): 10%
- 已退款 (refunded): 3%
- 已过期 (expired): 2%

**订单特征**：
- 订单号: AIR + 时间戳 + 随机数
- 创建时间: 最近30天内随机
- 支付方式: 微信支付/支付宝随机
- 优惠券使用: 30%概率使用优惠券
- 微信支付信息: 已支付订单包含完整的微信支付信息

## 测试场景

### 1. 订单列表测试
- 验证不同状态的订单显示
- 测试搜索和筛选功能
- 验证分页功能

### 2. 订单详情测试
- 查看完整的订单信息
- 验证用户信息显示
- 检查微信支付信息
- 确认优惠券信息

### 3. 统计功能测试
- 验证订单统计数据
- 检查支付成功率计算
- 测试收入统计
- 验证每日统计数据

### 4. 操作功能测试
- 取消待支付订单
- 申请已支付订单退款
- 验证状态转换逻辑

## 注意事项

1. **数据安全**：清理脚本会删除所有相关数据，请谨慎使用
2. **数据库连接**：确保数据库配置正确
3. **依赖检查**：确保所有必要的Python包已安装
4. **权限要求**：确保有数据库写入权限

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `app/settings/config.py` 中的数据库配置
   - 确保数据库服务正在运行

2. **导入错误**
   - 确保在项目根目录运行脚本
   - 检查Python路径设置

3. **权限错误**
   - 确保有数据库写入权限
   - 检查文件系统权限

### 调试模式

如果遇到问题，可以在脚本中添加更多调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

可以根据需要修改脚本：

1. **调整数据量**：修改 `count` 参数
2. **自定义状态分布**：修改 `statuses` 权重
3. **添加新的测试场景**：扩展数据生成逻辑
4. **自定义时间范围**：修改日期生成逻辑

现在您可以使用这些脚本来创建完整的测试数据，验证后台管理系统的充值管理功能！
