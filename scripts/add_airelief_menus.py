#!/usr/bin/env python3
"""
添加AIRelief菜单到数据库的脚本
使用方法：python scripts/add_airelief_menus.py
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.init_app import init_db
from app.models.admin import Menu
from app.schemas.menus import MenuType


async def add_airelief_menus():
    """添加AIRelief菜单到数据库"""
    try:
        # 初始化数据库连接
        await init_db()
        print("数据库连接成功")
        
        # 检查AIRelief菜单是否已存在
        existing_menu = await Menu.filter(name="AI Relief 管理").first()
        if existing_menu:
            print("AIRelief菜单已存在，跳过创建")
            return
        
        print("开始创建AIRelief菜单...")
        
        # 创建AIRelief一级菜单
        airelief_parent = await Menu.create(
            menu_type=MenuType.CATALOG,
            name="AI Relief 管理",
            path="/airelief",
            order=2,  # 在系统管理之后
            parent_id=0,
            icon="icon-park-outline:robot",
            is_hidden=False,
            component="Layout",
            keepalive=False,
            redirect="/airelief/user",
        )
        print(f"创建一级菜单: {airelief_parent.name} (ID: {airelief_parent.id})")
        
        # 创建AIRelief二级菜单
        airelief_children = [
            Menu(
                menu_type=MenuType.MENU,
                name="用户管理",
                path="user",
                order=1,
                parent_id=airelief_parent.id,
                icon="mdi:account-group",
                is_hidden=False,
                component="/airelief/user",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="充值管理",
                path="recharge",
                order=2,
                parent_id=airelief_parent.id,
                icon="mdi:cash-multiple",
                is_hidden=False,
                component="/airelief/recharge",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="时长管理",
                path="duration",
                order=3,
                parent_id=airelief_parent.id,
                icon="mdi:clock-outline",
                is_hidden=False,
                component="/airelief/duration",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="协议管理",
                path="agreement",
                order=4,
                parent_id=airelief_parent.id,
                icon="mdi:file-document-outline",
                is_hidden=False,
                component="/airelief/agreement",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="聊天数据",
                path="chat",
                order=5,
                parent_id=airelief_parent.id,
                icon="mdi:chat-outline",
                is_hidden=False,
                component="/airelief/chat",
                keepalive=False,
            ),
            Menu(
                menu_type=MenuType.MENU,
                name="兑换码管理",
                path="redeem",
                order=6,
                parent_id=airelief_parent.id,
                icon="mdi:ticket-outline",
                is_hidden=False,
                component="/airelief/redeem",
                keepalive=False,
            ),
        ]
        
        # 批量创建二级菜单
        await Menu.bulk_create(airelief_children)
        print(f"创建 {len(airelief_children)} 个二级菜单")
        
        # 删除旧的AI-Relief用户管理菜单（如果存在）
        old_menu = await Menu.filter(name="AI-Relief用户管理").first()
        if old_menu:
            await old_menu.delete()
            print("删除旧的AI-Relief用户管理菜单")
        
        print("✅ AIRelief菜单创建完成！")
        print("\n菜单结构:")
        print("📁 AI Relief 管理")
        for menu in airelief_children:
            print(f"  ├── {menu.name}")
            
    except Exception as e:
        print(f"❌ 创建菜单失败: {e}")
        raise
    finally:
        # 关闭数据库连接
        from tortoise import Tortoise
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(add_airelief_menus()) 