#!/usr/bin/env python3
"""
添加初始协议数据到数据库的脚本
使用方法：python scripts/add_initial_agreements.py
"""

import asyncio
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.init_app import init_db
from app.models.airelief import Agreement


# 协议内容模板
PRIVACY_POLICY_CONTENT = """# 隐私政策

## 1. 信息收集
我们收集您在使用服务时提供的信息，包括但不限于：
- 注册信息（手机号码等）
- 使用记录和日志信息
- 设备信息

## 2. 信息使用
我们使用收集的信息用于：
- 提供和改进服务
- 用户身份验证
- 客服支持

## 3. 信息保护
我们采取合理的安全措施保护您的个人信息：
- 数据加密传输
- 访问权限控制
- 定期安全审计

## 4. 信息共享
除法律要求外，我们不会与第三方共享您的个人信息。

## 5. 用户权利
您有权：
- 查看个人信息
- 更正错误信息
- 删除个人信息

## 6. 联系我们
如有疑问，请联系我们：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx

最后更新：2025年6月"""

USER_AGREEMENT_CONTENT = """# 用户服务协议

## 1. 服务说明
愈言空间是一个AI心理健康服务平台，为用户提供：
- AI心理咨询对话
- 情绪疏导服务
- 心理健康资讯

## 2. 用户责任
用户在使用服务时应当：
- 提供真实、准确的信息
- 遵守法律法规
- 不得滥用服务

## 3. 服务规范
- 服务时间：7x24小时
- 响应时间：实时对话
- 服务质量：持续优化

## 4. 隐私保护
我们严格保护用户隐私：
- 对话内容加密存储
- 不会泄露用户信息
- 遵循隐私政策

## 5. 费用说明
- 基础服务免费
- 高级功能按时长收费
- 支持多种支付方式

## 6. 免责声明
- AI服务不能替代专业医疗
- 紧急情况请寻求专业帮助
- 服务仅供参考

## 7. 协议变更
我们保留修改本协议的权利，变更后将及时通知用户。

最后更新：2025年6月"""

PAYMENT_AGREEMENT_CONTENT = """# 充值服务协议

## 1. 充值说明
用户可通过以下方式进行充值：
- 微信支付
- 支付宝
- 银行卡支付

## 2. 充值规则
- 充值金额实时到账
- 充值记录可查询
- 支持多种充值套餐

## 3. 时长计费
- 按分钟计费
- 不同套餐享受不同优惠
- 时长永久有效

## 4. 退款政策
- 未使用时长支持退款
- 退款将扣除手续费
- 退款周期为3-7个工作日

## 5. 发票服务
- 支持电子发票
- 发票内容为"技术服务费"
- 发票申请请联系客服

## 6. 风险提示
- 请妥善保管账户信息
- 充值前请确认金额
- 如有异常请及时联系客服

最后更新：2025年6月"""

DISCLAIMER_CONTENT = """# 免责声明

## 1. 服务性质
愈言空间提供的是AI辅助心理健康服务，不能替代专业的心理治疗或医疗服务。

## 2. 专业建议
- 如需专业心理治疗，请咨询专业心理医生
- 紧急情况请拨打相关求助热线
- 严重心理问题请及时就医

## 3. 服务限制
- AI回复仅供参考
- 不保证解决所有问题
- 服务质量持续改进中

## 4. 责任限制
在法律允许的范围内，我们不承担因使用服务而产生的任何间接、偶然或特殊损害的责任。

## 5. 紧急联系
如遇紧急情况，请联系：
- 全国心理危机干预热线：400-161-9995
- 北京危机干预热线：400-161-9995
- 上海心理援助热线：021-64387250

## 6. 适用法律
本声明适用中华人民共和国法律。

最后更新：2025年6月"""


async def add_initial_agreements():
    """添加初始协议数据到数据库"""
    try:
        # 初始化数据库连接
        await init_db()
        print("数据库连接成功")
        
        # 检查是否已存在协议数据
        existing_count = await Agreement.all().count()
        if existing_count > 0:
            print(f"数据库中已存在 {existing_count} 个协议，是否要继续添加？")
            response = input("输入 'y' 继续，其他键退出: ")
            if response.lower() != 'y':
                print("操作已取消")
                return
        
        print("开始创建初始协议数据...")
        
        # 准备协议数据
        agreements_data = [
            {
                "type": Agreement.TYPE_PRIVACY_POLICY,
                "title": "隐私政策",
                "content": PRIVACY_POLICY_CONTENT,
                "version": "1.0",
                "status": Agreement.STATUS_ACTIVE,
                "effective_date": datetime.now()
            },
            {
                "type": Agreement.TYPE_USER_AGREEMENT,
                "title": "用户服务协议",
                "content": USER_AGREEMENT_CONTENT,
                "version": "1.0", 
                "status": Agreement.STATUS_ACTIVE,
                "effective_date": datetime.now()
            },
            {
                "type": Agreement.TYPE_PAYMENT_AGREEMENT,
                "title": "充值服务协议",
                "content": PAYMENT_AGREEMENT_CONTENT,
                "version": "1.0",
                "status": Agreement.STATUS_ACTIVE,
                "effective_date": datetime.now()
            },
            {
                "type": Agreement.TYPE_DISCLAIMER,
                "title": "免责声明",
                "content": DISCLAIMER_CONTENT,
                "version": "1.0",
                "status": Agreement.STATUS_ACTIVE,
                "effective_date": datetime.now()
            }
        ]
        
        # 创建协议
        created_count = 0
        for agreement_data in agreements_data:
            # 检查是否已存在相同类型的生效协议
            existing = await Agreement.filter(
                type=agreement_data["type"], 
                status=Agreement.STATUS_ACTIVE
            ).first()
            
            if existing:
                print(f"⚠️  {agreement_data['title']} 已存在生效版本，跳过创建")
                continue
            
            # 创建协议
            agreement = await Agreement.create(**agreement_data)
            print(f"✅ 创建协议: {agreement.title} (ID: {agreement.id})")
            created_count += 1
        
        print(f"\n🎉 协议创建完成！共创建 {created_count} 个协议")
        
        # 显示创建结果
        if created_count > 0:
            print("\n创建的协议:")
            all_agreements = await Agreement.filter(status=Agreement.STATUS_ACTIVE).all()
            for agreement in all_agreements:
                print(f"- {agreement.title} (类型: {agreement.type}, 版本: {agreement.version})")
            
    except Exception as e:
        print(f"❌ 创建协议失败: {e}")
        raise
    finally:
        # 关闭数据库连接
        from tortoise import Tortoise
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(add_initial_agreements()) 