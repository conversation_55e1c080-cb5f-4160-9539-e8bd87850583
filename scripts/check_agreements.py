#!/usr/bin/env python3
"""
检查数据库中的协议数据
使用方法：python scripts/check_agreements.py
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.init_app import init_db
from app.models.airelief import Agreement


async def check_agreements():
    """检查数据库中的协议数据"""
    try:
        # 初始化数据库连接
        await init_db()
        print("数据库连接成功")
        
        # 查询所有协议
        agreements = await Agreement.all()
        
        if not agreements:
            print("❌ 数据库中没有任何协议数据")
            return
        
        print(f"✅ 数据库中共有 {len(agreements)} 个协议:")
        print("-" * 80)
        
        for agreement in agreements:
            print(f"ID: {agreement.id}")
            print(f"类型: {agreement.type}")
            print(f"标题: {agreement.title}")
            print(f"版本: {agreement.version}")
            print(f"状态: {agreement.status}")
            print(f"生效时间: {agreement.effective_date}")
            print(f"创建时间: {agreement.created_at}")
            print(f"更新时间: {agreement.updated_at}")
            print(f"内容长度: {len(agreement.content) if agreement.content else 0} 字符")
            print("-" * 80)
        
        # 检查特定类型的协议
        print("\n按类型统计:")
        types = [Agreement.TYPE_USER_AGREEMENT, Agreement.TYPE_PRIVACY_POLICY, 
                Agreement.TYPE_PAYMENT_AGREEMENT, Agreement.TYPE_DISCLAIMER]
        
        for agreement_type in types:
            count = await Agreement.filter(type=agreement_type).count()
            active_count = await Agreement.filter(type=agreement_type, status=Agreement.STATUS_ACTIVE).count()
            
            type_labels = {
                Agreement.TYPE_USER_AGREEMENT: "用户协议",
                Agreement.TYPE_PRIVACY_POLICY: "隐私政策", 
                Agreement.TYPE_PAYMENT_AGREEMENT: "充值协议",
                Agreement.TYPE_DISCLAIMER: "免责声明"
            }
            
            label = type_labels.get(agreement_type, agreement_type)
            print(f"{label}: 总数 {count}, 生效中 {active_count}")
            
    except Exception as e:
        print(f"❌ 检查协议失败: {e}")
        raise
    finally:
        # 关闭数据库连接
        from tortoise import Tortoise
        await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(check_agreements()) 