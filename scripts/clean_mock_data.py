#!/usr/bin/env python3
"""
清理模拟数据的脚本
用于清除测试数据
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from app.models.airelief import AIReliefUser, PaymentPackage, Order, Coupon, CouponUsage
from app.settings.config import settings


async def init_db():
    """初始化数据库连接"""
    await Tortoise.init(config=settings.TORTOISE_ORM)


async def clean_mock_data():
    """清理模拟数据"""
    print("开始清理模拟数据...")
    
    try:
        # 清理优惠券使用记录
        coupon_usage_count = await CouponUsage.all().count()
        await CouponUsage.all().delete()
        print(f"清理优惠券使用记录: {coupon_usage_count} 条")
        
        # 清理订单
        order_count = await Order.all().count()
        await Order.all().delete()
        print(f"清理订单: {order_count} 条")
        
        # 清理优惠券
        coupon_count = await Coupon.all().count()
        await Coupon.all().delete()
        print(f"清理优惠券: {coupon_count} 条")
        
        # 清理套餐
        package_count = await PaymentPackage.all().count()
        await PaymentPackage.all().delete()
        print(f"清理套餐: {package_count} 条")
        
        # 清理用户 (只清理测试用户)
        test_users = await AIReliefUser.filter(nickname__startswith="测试用户").all()
        test_user_count = len(test_users)
        for user in test_users:
            await user.delete()
        print(f"清理测试用户: {test_user_count} 个")
        
        print("\n✅ 模拟数据清理完成!")
        
    except Exception as e:
        print(f"❌ 清理数据失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    try:
        # 初始化数据库
        await init_db()
        print("数据库连接成功")
        
        # 确认清理
        confirm = input("\n⚠️  确定要清理所有模拟数据吗？这个操作不可逆！(y/N): ")
        if confirm.lower() in ['y', 'yes']:
            await clean_mock_data()
        else:
            print("取消清理操作")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
    
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        print("\n数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
