#!/usr/bin/env python3
"""
创建模拟订单数据的脚本
用于测试后台管理系统的充值管理页面
"""

import asyncio
import random
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from app.models.airelief import AIReliefUser, PaymentPackage, Order, Coupon, CouponUsage
from app.settings.config import settings


async def init_db():
    """初始化数据库连接"""
    await Tortoise.init(config=settings.TORTOISE_ORM)


async def create_mock_users(count=10):
    """创建模拟用户"""
    users = []
    for i in range(count):
        user = await AIReliefUser.create(
            wechat_openid=f"mock_openid_{i}_{uuid.uuid4().hex[:8]}",
            nickname=f"测试用户{i+1}",
            phone=f"138{random.randint(10000000, 99999999)}",
            gender=random.choice([0, 1, 2]),
            duration=random.randint(0, 86400 * 30),  # 0-30天的时长
            is_active=True
        )
        users.append(user)
        print(f"创建用户: {user.nickname} (ID: {user.user_id})")
    
    return users


async def create_mock_packages():
    """创建模拟套餐"""
    packages_data = [
        {"name": "体验包", "duration": "3天", "duration_seconds": 3*24*3600, "price": Decimal("9.9"), "original_price": Decimal("19.9")},
        {"name": "周卡", "duration": "7天", "duration_seconds": 7*24*3600, "price": Decimal("29.9"), "original_price": Decimal("39.9")},
        {"name": "月卡", "duration": "30天", "duration_seconds": 30*24*3600, "price": Decimal("99.9"), "original_price": Decimal("129.9")},
        {"name": "季度卡", "duration": "90天", "duration_seconds": 90*24*3600, "price": Decimal("269.9"), "original_price": Decimal("389.9")},
        {"name": "年卡", "duration": "365天", "duration_seconds": 365*24*3600, "price": Decimal("999.9"), "original_price": Decimal("1299.9")},
    ]
    
    packages = []
    for data in packages_data:
        # 检查是否已存在
        existing = await PaymentPackage.filter(name=data["name"]).first()
        if existing:
            packages.append(existing)
            print(f"套餐已存在: {existing.name}")
        else:
            package = await PaymentPackage.create(**data)
            packages.append(package)
            print(f"创建套餐: {package.name} - ¥{package.price}")
    
    return packages


async def create_mock_coupons():
    """创建模拟优惠券"""
    coupons_data = [
        {"code": "WELCOME10", "type": "discount", "value": Decimal("10.0"), "max_uses": 100},
        {"code": "SAVE20", "type": "discount", "value": Decimal("20.0"), "max_uses": 50},
        {"code": "VIP50", "type": "discount", "value": Decimal("50.0"), "max_uses": 20},
        {"code": "DURATION7", "type": "duration", "value": Decimal("7.0"), "max_uses": 30},
    ]
    
    coupons = []
    now = datetime.now()
    
    for data in coupons_data:
        # 检查是否已存在
        existing = await Coupon.filter(code=data["code"]).first()
        if existing:
            coupons.append(existing)
            print(f"优惠券已存在: {existing.code}")
        else:
            coupon = await Coupon.create(
                **data,
                valid_from=now - timedelta(days=30),
                valid_until=now + timedelta(days=30),
                is_active=True
            )
            coupons.append(coupon)
            print(f"创建优惠券: {coupon.code} - {coupon.type} ¥{coupon.value}")
    
    return coupons


async def create_mock_orders(users, packages, coupons, count=50):
    """创建模拟订单"""
    statuses = [
        ("pending", 0.1),    # 10% 待支付
        ("paid", 0.7),       # 70% 已支付
        ("failed", 0.05),    # 5% 支付失败
        ("cancelled", 0.1),  # 10% 已取消
        ("refunded", 0.03),  # 3% 已退款
        ("expired", 0.02),   # 2% 已过期
    ]
    
    payment_methods = ["wechat", "alipay"]
    
    orders = []
    
    for i in range(count):
        # 随机选择用户和套餐
        user = random.choice(users)
        package = random.choice(packages)
        
        # 随机决定是否使用优惠券 (30% 概率)
        coupon = random.choice(coupons) if random.random() < 0.3 else None
        
        # 计算金额
        original_amount = package.price
        discount_amount = Decimal("0")
        
        if coupon and coupon.type == "discount":
            discount_amount = min(coupon.value, original_amount)
        
        final_amount = max(Decimal("0.01"), original_amount - discount_amount)
        
        # 随机选择状态
        status_weights = [weight for _, weight in statuses]
        status = random.choices([s for s, _ in statuses], weights=status_weights)[0]
        
        # 生成订单号
        timestamp = int((datetime.now() - timedelta(days=random.randint(0, 30))).timestamp())
        order_id = f"AIR{timestamp}{random.randint(1000, 9999)}"
        
        # 创建时间 (最近30天内)
        created_at = datetime.now() - timedelta(
            days=random.randint(0, 30),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59)
        )
        
        # 支付时间 (如果已支付)
        pay_time = None
        if status in ["paid", "refunded"]:
            pay_time = created_at + timedelta(minutes=random.randint(1, 30))
        
        # 微信支付相关字段
        wechat_transaction_id = None
        wechat_prepay_id = None
        wechat_bank_type = None
        wechat_cash_fee = None
        
        if status in ["paid", "refunded"]:
            wechat_transaction_id = f"4200{random.randint(************, ************)}"
            wechat_prepay_id = f"wx{random.randint(*****************, *****************)}"
            wechat_bank_type = random.choice(["CMC", "ICBC", "CCB", "ABC", "COMM"])
            wechat_cash_fee = int(final_amount * 100)  # 转换为分
        
        order = await Order.create(
            order_id=order_id,
            user=user,
            package=package,
            package_name=package.name,
            amount=final_amount,
            original_amount=original_amount,
            discount_amount=discount_amount,
            coupon_code=coupon.code if coupon else None,
            status=status,
            payment_method=random.choice(payment_methods),
            wechat_transaction_id=wechat_transaction_id,
            wechat_prepay_id=wechat_prepay_id,
            wechat_bank_type=wechat_bank_type,
            wechat_cash_fee=wechat_cash_fee,
            pay_time=pay_time,
            created_at=created_at,
            updated_at=created_at
        )
        
        # 如果使用了优惠券，创建使用记录
        if coupon and status in ["paid", "refunded"]:
            await CouponUsage.create(
                coupon=coupon,
                user=user,
                order=order,
                used_at=pay_time or created_at
            )
            
            # 更新优惠券使用次数
            coupon.used_count += 1
            await coupon.save()
        
        orders.append(order)
        print(f"创建订单: {order.order_id} - {user.nickname} - {package.name} - ¥{final_amount} - {status}")
    
    return orders


async def main():
    """主函数"""
    print("开始创建模拟数据...")
    
    try:
        # 初始化数据库
        await init_db()
        print("数据库连接成功")
        
        # 创建模拟数据
        print("\n1. 创建模拟用户...")
        users = await create_mock_users(15)
        
        print("\n2. 创建模拟套餐...")
        packages = await create_mock_packages()
        
        print("\n3. 创建模拟优惠券...")
        coupons = await create_mock_coupons()
        
        print("\n4. 创建模拟订单...")
        orders = await create_mock_orders(users, packages, coupons, 100)
        
        print(f"\n✅ 模拟数据创建完成!")
        print(f"   - 用户: {len(users)} 个")
        print(f"   - 套餐: {len(packages)} 个")
        print(f"   - 优惠券: {len(coupons)} 个")
        print(f"   - 订单: {len(orders)} 个")
        
        # 统计订单状态
        print("\n📊 订单状态统计:")
        for status in ["pending", "paid", "failed", "cancelled", "refunded", "expired"]:
            count = await Order.filter(status=status).count()
            print(f"   - {status}: {count} 个")
        
    except Exception as e:
        print(f"❌ 创建模拟数据失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        print("\n数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
