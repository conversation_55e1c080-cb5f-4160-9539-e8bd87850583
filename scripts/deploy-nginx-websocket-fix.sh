#!/bin/bash

# Nginx WebSocket 配置部署脚本
# 安全地更新 Nginx 配置以支持 WebSocket

set -e  # 遇到错误立即退出

echo "=== Nginx WebSocket 配置部署脚本 ==="
echo "开始时间: $(date)"
echo ""

# 检查是否以 root 权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请以 root 权限运行此脚本"
    echo "使用: sudo bash deploy-nginx-websocket-fix.sh"
    exit 1
fi

# 配置文件路径
NGINX_CONFIG="/etc/nginx/sites-available/framework_django"
BACKUP_DIR="/root/nginx-backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/framework_django_$TIMESTAMP.conf"

echo "1. 创建备份目录..."
mkdir -p "$BACKUP_DIR"

echo "2. 备份当前配置..."
if [ -f "$NGINX_CONFIG" ]; then
    cp "$NGINX_CONFIG" "$BACKUP_FILE"
    echo "✅ 配置已备份到: $BACKUP_FILE"
else
    echo "❌ 未找到配置文件: $NGINX_CONFIG"
    exit 1
fi

echo "3. 测试当前 Nginx 配置..."
if nginx -t; then
    echo "✅ 当前配置语法正确"
else
    echo "❌ 当前配置语法错误，请先修复"
    exit 1
fi

echo "4. 应用新配置..."
if [ -f "nginx-config-fixed.conf" ]; then
    cp "nginx-config-fixed.conf" "$NGINX_CONFIG"
    echo "✅ 新配置已应用"
else
    echo "❌ 未找到修复后的配置文件: nginx-config-fixed.conf"
    exit 1
fi

echo "5. 测试新配置..."
if nginx -t; then
    echo "✅ 新配置语法正确"
else
    echo "❌ 新配置语法错误，正在恢复备份..."
    cp "$BACKUP_FILE" "$NGINX_CONFIG"
    echo "✅ 已恢复备份配置"
    exit 1
fi

echo "6. 重新加载 Nginx..."
if systemctl reload nginx; then
    echo "✅ Nginx 重新加载成功"
else
    echo "❌ Nginx 重新加载失败，正在恢复备份..."
    cp "$BACKUP_FILE" "$NGINX_CONFIG"
    systemctl reload nginx
    echo "✅ 已恢复备份配置"
    exit 1
fi

echo "7. 验证 Nginx 状态..."
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx 运行正常"
else
    echo "❌ Nginx 未运行，请检查日志"
    systemctl status nginx
    exit 1
fi

echo ""
echo "=== 部署完成 ==="
echo "✅ WebSocket 配置已成功部署"
echo "📁 备份文件: $BACKUP_FILE"
echo "🔧 如需回滚，运行: cp $BACKUP_FILE $NGINX_CONFIG && systemctl reload nginx"
echo ""
echo "现在可以测试 WebSocket 连接："
echo "curl -i -N -H \"Connection: Upgrade\" -H \"Upgrade: websocket\" -H \"Sec-WebSocket-Version: 13\" -H \"Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==\" https://www.airelief.cn:8997/api/v1/airelief/chat/ws/TEST123"
echo ""
echo "完成时间: $(date)"
