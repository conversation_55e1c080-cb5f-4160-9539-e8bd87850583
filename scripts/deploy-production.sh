#!/bin/bash

# ========================================
# 生产环境部署脚本
# ========================================
# 
# 此脚本用于将头像上传功能部署到生产环境
# 使用前请确保已按照 docs/AVATAR_UPLOAD_DEPLOYMENT.md 完成配置
#

set -e  # 遇到错误立即退出

echo "🚀 开始部署头像上传功能到生产环境..."

# 检查必要文件是否存在
echo "📋 检查部署前置条件..."

if [ ! -f ".env.production" ]; then
    echo "❌ 错误: .env.production 文件不存在"
    echo "请复制 .env.production.example 并配置正确的环境变量"
    exit 1
fi

# 检查环境变量配置
if ! grep -q "VITE_API_BASE_URL=https://" .env.production; then
    echo "❌ 错误: VITE_API_BASE_URL 未正确配置"
    echo "请在 .env.production 中设置正确的API服务器地址"
    exit 1
fi

if grep -q "VITE_ENABLE_MOCK=true" .env.production; then
    echo "❌ 错误: 生产环境中模拟API仍然启用"
    echo "请在 .env.production 中设置 VITE_ENABLE_MOCK=false"
    exit 1
fi

echo "✅ 环境配置检查通过"

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 运行类型检查
echo "🔍 运行类型检查..."
npm run type-check

# 运行测试（如果有）
if [ -f "package.json" ] && grep -q "\"test\"" package.json; then
    echo "🧪 运行测试..."
    npm run test
fi

# 构建生产版本
echo "🏗️ 构建生产版本..."
npm run build

echo "✅ 构建完成"

# 验证构建结果
if [ ! -d "dist" ]; then
    echo "❌ 错误: 构建失败，dist 目录不存在"
    exit 1
fi

echo "📊 构建统计:"
du -sh dist/
echo "文件数量: $(find dist -type f | wc -l)"

# 部署提示
echo ""
echo "🎉 构建成功！"
echo ""
echo "📋 部署检查清单:"
echo "  ✅ 环境变量已配置"
echo "  ✅ 模拟API已禁用"
echo "  ✅ 代码构建成功"
echo ""
echo "🚀 下一步操作:"
echo "  1. 将 dist/ 目录部署到Web服务器"
echo "  2. 确保API服务器已部署并可访问"
echo "  3. 测试头像上传功能"
echo "  4. 监控错误日志和性能指标"
echo ""
echo "📖 详细部署文档: docs/AVATAR_UPLOAD_DEPLOYMENT.md"
