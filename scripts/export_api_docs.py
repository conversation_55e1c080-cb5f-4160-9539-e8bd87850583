#!/usr/bin/env python3
"""
导出FastAPI项目的API文档 - 专门针对 /api/v1/airelief 路径
支持导出为JSON、HTML、Markdown等格式，包含完整的数据结构
"""

import json
import sys
import asyncio
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app import app


def filter_airelief_paths(openapi_schema: Dict[str, Any]) -> Dict[str, Any]:
    """过滤出 /api/v1/airelief 相关的路径"""
    filtered_paths = {}

    for path, methods in openapi_schema.get('paths', {}).items():
        if '/api/v1/airelief' in path:
            filtered_paths[path] = methods

    # 创建新的schema，只包含airelief相关的内容
    filtered_schema = openapi_schema.copy()
    filtered_schema['paths'] = filtered_paths

    return filtered_schema


async def export_openapi_json(output_path: str = "docs/airelief_openapi.json"):
    """导出AI-Relief OpenAPI JSON文档"""
    try:
        # 获取OpenAPI schema
        openapi_schema = app.openapi()

        # 过滤出airelief相关的路径
        filtered_schema = filter_airelief_paths(openapi_schema)

        # 确保输出目录存在
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_schema, f, indent=2, ensure_ascii=False)

        print(f"✅ AI-Relief OpenAPI JSON文档已导出到: {output_file.absolute()}")
        return str(output_file.absolute())

    except Exception as e:
        print(f"❌ 导出AI-Relief OpenAPI JSON失败: {e}")
        return None


async def export_swagger_html(output_path: str = "docs/airelief_swagger.html"):
    """导出AI-Relief Swagger UI HTML文档"""
    try:
        # 获取OpenAPI schema
        openapi_schema = app.openapi()

        # 过滤出airelief相关的路径
        filtered_schema = filter_airelief_paths(openapi_schema)

        # 更新标题
        filtered_schema['info']['title'] = "AI-Relief API 文档"

        # Swagger UI HTML模板
        swagger_html = f"""<!DOCTYPE html>
<html>
<head>
    <title>{filtered_schema.get('info', {}).get('title', 'AI-Relief API Documentation')}</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
    <style>
        html {{
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }}
        *, *:before, *:after {{
            box-sizing: inherit;
        }}
        body {{
            margin:0;
            background: #fafafa;
        }}
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {{
            const ui = SwaggerUIBundle({{
                spec: {json.dumps(filtered_schema)},
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout"
            }});
        }};
    </script>
</body>
</html>"""

        # 确保输出目录存在
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # 写入HTML文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(swagger_html)

        print(f"✅ AI-Relief Swagger HTML文档已导出到: {output_file.absolute()}")
        return str(output_file.absolute())

    except Exception as e:
        print(f"❌ 导出AI-Relief Swagger HTML失败: {e}")
        return None


def format_schema_properties(properties: Dict[str, Any], required: list = None) -> str:
    """格式化Schema属性为Markdown表格"""
    if not properties:
        return ""

    required = required or []
    table_rows = ["| 字段名 | 类型 | 必需 | 描述 |", "|--------|------|------|------|"]

    for prop_name, prop_info in properties.items():
        prop_type = prop_info.get('type', 'unknown')
        if prop_type == 'array' and 'items' in prop_info:
            items_type = prop_info['items'].get('type', 'unknown')
            prop_type = f"array[{items_type}]"
        elif '$ref' in prop_info:
            ref_name = prop_info['$ref'].split('/')[-1]
            prop_type = f"object({ref_name})"

        is_required = "是" if prop_name in required else "否"
        description = prop_info.get('description', '')

        table_rows.append(f"| {prop_name} | {prop_type} | {is_required} | {description} |")

    return "\n".join(table_rows)


async def export_markdown_docs(output_path: str = "docs/airelief_api_docs.md"):
    """导出AI-Relief Markdown格式的API文档，包含完整数据结构"""
    try:
        # 获取OpenAPI schema
        openapi_schema = app.openapi()

        # 过滤出airelief相关的路径
        filtered_schema = filter_airelief_paths(openapi_schema)

        # 构建Markdown内容
        markdown_content = []

        # 标题和描述
        info = filtered_schema.get('info', {})
        markdown_content.append("# AI-Relief API 文档")
        markdown_content.append(f"\n**版本**: {info.get('version', 'N/A')}")
        markdown_content.append(f"\n**生成时间**: {asyncio.get_event_loop().time()}")
        markdown_content.append("\n**描述**: AI-Relief 项目的完整API文档，包含所有接口和数据结构定义")

        # 数据结构部分
        components = filtered_schema.get('components', {})
        schemas = components.get('schemas', {})

        if schemas:
            markdown_content.append("\n## 数据结构定义\n")

            for schema_name, schema_info in schemas.items():
                markdown_content.append(f"### {schema_name}")

                if schema_info.get('description'):
                    markdown_content.append(f"\n{schema_info['description']}")

                properties = schema_info.get('properties', {})
                required = schema_info.get('required', [])

                if properties:
                    markdown_content.append("\n**字段说明**:")
                    markdown_content.append(format_schema_properties(properties, required))

                markdown_content.append("\n---\n")

        # 接口列表部分
        markdown_content.append("\n## 接口列表\n")

        # 按标签分组
        paths = filtered_schema.get('paths', {})
        grouped_paths = {}

        for path, methods in paths.items():
            for method, details in methods.items():
                if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                    tags = details.get('tags', ['未分类'])
                    tag = tags[0] if tags else '未分类'

                    if tag not in grouped_paths:
                        grouped_paths[tag] = []

                    grouped_paths[tag].append({
                        'path': path,
                        'method': method.upper(),
                        'details': details
                    })

        # 输出分组的接口
        for tag, endpoints in grouped_paths.items():
            markdown_content.append(f"### {tag}\n")

            for endpoint in endpoints:
                path = endpoint['path']
                method = endpoint['method']
                details = endpoint['details']

                markdown_content.append(f"#### {method} {path}")

                # 接口描述
                if details.get('summary'):
                    markdown_content.append(f"**摘要**: {details['summary']}")
                if details.get('description'):
                    markdown_content.append(f"**描述**: {details['description']}")

                # 参数
                if details.get('parameters'):
                    markdown_content.append("\n**参数**:")
                    for param in details['parameters']:
                        param_info = f"- `{param['name']}` ({param.get('in', 'query')})"
                        if param.get('required'):
                            param_info += " *必需*"
                        if param.get('description'):
                            param_info += f": {param['description']}"
                        markdown_content.append(param_info)

                # 请求体
                if details.get('requestBody'):
                    markdown_content.append("\n**请求体**:")
                    request_body = details['requestBody']
                    if request_body.get('description'):
                        markdown_content.append(f"- 描述: {request_body['description']}")

                    content = request_body.get('content', {})
                    for content_type, content_info in content.items():
                        markdown_content.append(f"- 内容类型: `{content_type}`")

                        # 如果有schema引用，显示数据结构
                        schema = content_info.get('schema', {})
                        if '$ref' in schema:
                            ref_name = schema['$ref'].split('/')[-1]
                            markdown_content.append(f"- 数据结构: {ref_name}")

                # 响应
                if details.get('responses'):
                    markdown_content.append("\n**响应**:")
                    for status_code, response in details['responses'].items():
                        response_info = f"- `{status_code}`"
                        if response.get('description'):
                            response_info += f": {response['description']}"

                        # 如果有响应内容，显示数据结构
                        content = response.get('content', {})
                        for content_type, content_info in content.items():
                            schema = content_info.get('schema', {})
                            if '$ref' in schema:
                                ref_name = schema['$ref'].split('/')[-1]
                                response_info += f" (数据结构: {ref_name})"

                        markdown_content.append(response_info)

                markdown_content.append("\n---\n")

        # 确保输出目录存在
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # 写入Markdown文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))

        print(f"✅ AI-Relief Markdown文档已导出到: {output_file.absolute()}")
        return str(output_file.absolute())

    except Exception as e:
        print(f"❌ 导出AI-Relief Markdown文档失败: {e}")
        return None


async def main():
    """主函数"""
    print("🚀 开始导出AI-Relief API文档...")

    # 导出不同格式的文档
    results = []

    # 导出OpenAPI JSON
    json_result = await export_openapi_json()
    if json_result:
        results.append(json_result)

    # 导出Swagger HTML
    html_result = await export_swagger_html()
    if html_result:
        results.append(html_result)

    # 导出Markdown
    md_result = await export_markdown_docs()
    if md_result:
        results.append(md_result)

    print(f"\n📋 导出完成! 共生成 {len(results)} 个AI-Relief文档文件:")
    for result in results:
        print(f"  - {result}")

    print("\n💡 提示:")
    print("  - 可以直接在浏览器中打开 airelief_swagger.html 文件查看交互式文档")
    print("  - airelief_openapi.json 可以导入到 Postman 或其他API工具中")
    print("  - airelief_api_docs.md 包含完整的数据结构和接口文档")
    print("  - 所有文档只包含 /api/v1/airelief 路径下的接口")


if __name__ == "__main__":
    asyncio.run(main())
