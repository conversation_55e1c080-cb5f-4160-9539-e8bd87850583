#!/usr/bin/env python3
"""
初始化支付套餐数据
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.init_app import init_db
from app.models.airelief import PaymentPackage


async def init_payment_packages():
    """初始化支付套餐数据"""
    
    # 初始化数据库连接
    await init_db()
    
    print("开始初始化支付套餐数据...")
    
    # 检查是否已有数据
    existing_count = await PaymentPackage.all().count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 个套餐，跳过初始化")
        from tortoise import Tortoise
        await Tortoise.close_connections()
        return
    
    # 创建套餐数据
    packages = [
        {
            "name": "月卡",
            "duration": "30天",
            "duration_seconds": 30 * 24 * 3600,  # 30天转换为秒
            "price": 12.00,
            "original_price": 30.00,
            "tag": "限时特惠",
            "is_active": True,
            "sort_order": 1,
        },
        {
            "name": "季度卡",
            "duration": "90天",
            "duration_seconds": 90 * 24 * 3600,  # 90天转换为秒
            "price": 37.00,
            "original_price": 90.00,
            "tag": "",
            "is_active": True,
            "sort_order": 2,
        },
        {
            "name": "年卡",
            "duration": "365天", 
            "duration_seconds": 365 * 24 * 3600,  # 365天转换为秒
            "price": 100.00,
            "original_price": 270.00,
            "tag": "",
            "is_active": True,
            "sort_order": 3,
        }
    ]
    
    # 批量创建套餐
    created_packages = []
    for pkg_data in packages:
        try:
            pkg = await PaymentPackage.create(**pkg_data)
            created_packages.append(pkg)
            print(f"✓ 创建套餐: {pkg.name} - ¥{pkg.price}")
        except Exception as e:
            print(f"✗ 创建套餐失败: {pkg_data['name']} - {e}")
    
    print(f"\n成功创建 {len(created_packages)} 个支付套餐")
    
    # 关闭数据库连接
    from tortoise import Tortoise
    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(init_payment_packages()) 