server {
    listen 443 ssl;
    listen 8997 ssl;
    server_name www.airelief.cn;



    ssl_certificate /etc/letsencrypt/live/www.airelief.cn/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.airelief.cn/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    # ssl_stapling on;
    # ssl_stapling_verify on;

    location = /favicon.ico { access_log off; log_not_found off; }
    
    # 微信域名验证文件
    location = /MP_verify_iQMHfWsivcn1naON.txt {
        proxy_pass http://127.0.0.1:9999/MP_verify_iQMHfWsivcn1naON.txt;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /var/www/framework_django/static/;
        autoindex on;
    }
    
    # FastAPI API路径配置
    location /api/ {
        proxy_pass http://127.0.0.1:9999/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 微信公众号消息接口（传统路径）
    location /wx {
        proxy_pass http://127.0.0.1:9999/api/v1/airelief/auth/wx;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 前端应用代理
    location /AIrelief/ {
        proxy_pass http://127.0.0.1:8999/AIrelief/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # 后台管理应用代理
    location /airelief-admin/ {
        proxy_pass http://127.0.0.1:3100/airelief-admin/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        
        # 支持WebSocket连接
        proxy_set_header Sec-WebSocket-Extensions $http_sec_websocket_extensions;
        proxy_set_header Sec-WebSocket-Key $http_sec_websocket_key;
        proxy_set_header Sec-WebSocket-Version $http_sec_websocket_version;
    }
    
    # FastAPI文档路径
    location /docs {
        proxy_pass http://127.0.0.1:9999/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /openapi.json {
        proxy_pass http://127.0.0.1:9999/openapi.json;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Django应用（默认路径）
    location / {
        include /etc/nginx/proxy_params;
        proxy_pass http://unix:/var/www/framework_django/framework_django.sock;
    }
}

server {
    listen 80;
    server_name www.airelief.cn;

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt;
    }
    
    # 微信域名验证文件（HTTP 80端口）
    location = /MP_verify_iQMHfWsivcn1naON.txt {
        proxy_pass http://127.0.0.1:9999/MP_verify_iQMHfWsivcn1naON.txt;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 微信公众号消息接口（HTTP 80端口）
    location /wx {
        proxy_pass http://127.0.0.1:9999/api/v1/airelief/auth/wx;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # FastAPI API路径配置（HTTP 80端口）
    location /api/ {
        proxy_pass http://127.0.0.1:9999/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # FastAPI文档路径（HTTP 80端口）
    location /docs {
        proxy_pass http://127.0.0.1:9999/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}