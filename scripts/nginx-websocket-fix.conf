# Nginx WebSocket 配置修复方案
# 需要在现有的 Nginx 配置中添加 WebSocket 支持

# 在 server 块中添加以下配置：

server {
    listen 8997 ssl;
    server_name www.airelief.cn;
    
    # SSL 配置（保持现有配置）
    # ssl_certificate /path/to/your/cert.pem;
    # ssl_certificate_key /path/to/your/key.pem;
    
    # 添加 WebSocket 支持的 map 指令（在 http 块中）
    # map $http_upgrade $connection_upgrade {
    #     default upgrade;
    #     '' close;
    # }
    
    # 普通 HTTP 请求代理（保持现有配置）
    location / {
        proxy_pass http://127.0.0.1:9999;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 专门为 WebSocket 连接添加的配置
    location /api/v1/airelief/chat/ws/ {
        proxy_pass http://127.0.0.1:9999;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 特定配置
        proxy_read_timeout 86400;  # 24小时超时
        proxy_send_timeout 86400;  # 24小时超时
        proxy_connect_timeout 60s; # 连接超时
        
        # 禁用缓存
        proxy_buffering off;
        proxy_cache off;
    }
}

# 完整的 http 块配置示例：
# http {
#     # 添加 WebSocket 升级映射
#     map $http_upgrade $connection_upgrade {
#         default upgrade;
#         '' close;
#     }
#     
#     # 其他配置...
#     
#     server {
#         # 上面的 server 配置
#     }
# }

# 配置说明：
# 1. proxy_http_version 1.1 - 使用 HTTP/1.1 协议
# 2. proxy_set_header Upgrade $http_upgrade - 传递 Upgrade 头
# 3. proxy_set_header Connection $connection_upgrade - 传递 Connection 头
# 4. proxy_read_timeout/proxy_send_timeout - 设置长连接超时
# 5. proxy_buffering off - 禁用缓冲，实现实时通信

# 应用步骤：
# 1. 备份现有 Nginx 配置
# 2. 在 http 块中添加 map 指令
# 3. 在 server 块中添加 WebSocket location 配置
# 4. 重新加载 Nginx 配置：sudo nginx -s reload
# 5. 测试 WebSocket 连接
