#!/usr/bin/env python3
"""
刷新API权限的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tortoise import Tortoise
from app.settings.config import settings
from app.controllers.api import api_controller
from app.models.admin import Api, Role
from loguru import logger


async def refresh_apis():
    """刷新API权限"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 刷新API
        await api_controller.refresh_api()
        logger.info("API权限刷新完成")
        
        # 查找新添加的AI-Relief相关API
        airelief_apis = await Api.filter(path__startswith="/api/v1/airelief/user/")
        logger.info(f"找到 {len(airelief_apis)} 个AI-Relief用户管理API")
        
        for api in airelief_apis:
            logger.info(f"API: {api.method} {api.path} - {api.summary}")
        
        # 为管理员角色分配新的API权限
        admin_role = await Role.get_or_none(name="管理员")
        if admin_role and airelief_apis:
            # 获取管理员当前的API权限
            current_apis = await admin_role.apis.all()
            current_api_paths = {(api.method, api.path) for api in current_apis}
            
            # 添加新的API权限
            new_apis = []
            for api in airelief_apis:
                if (api.method, api.path) not in current_api_paths:
                    new_apis.append(api)
            
            if new_apis:
                await admin_role.apis.add(*new_apis)
                logger.info(f"为管理员角色添加了 {len(new_apis)} 个新的API权限")
            else:
                logger.info("管理员角色已拥有所有AI-Relief用户管理API权限")
        
    except Exception as e:
        logger.error(f"刷新API权限失败: {e}")
        raise
    finally:
        await Tortoise.close_connections()


async def main():
    """主函数"""
    logger.info("开始刷新API权限...")
    await refresh_apis()
    logger.info("完成")


if __name__ == "__main__":
    asyncio.run(main())
