import asyncio
import os
import shutil
from tortoise import Tortoise
from app.settings.config import settings
from app.log import logger

async def setup_database():
    """完整的数据库设置流程"""
    
    # 步骤1：清理现有迁移文件
    logger.info("清理现有迁移文件...")
    if os.path.exists("migrations"):
        shutil.rmtree("migrations")
        logger.info("已删除migrations目录")
    
    # 步骤2：初始化Tortoise ORM
    logger.info("初始化Tortoise ORM...")
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    # 步骤3：生成数据库表结构
    logger.info("生成数据库表结构...")
    await Tortoise.generate_schemas(safe=True)
    logger.info("表结构生成完成")
    
    # 步骤4：关闭数据库连接
    await Tortoise.close_connections()
    
    # 步骤5：输出成功消息
    logger.info("数据库设置完成！")
    logger.info("现在你可以运行以下命令初始化Aerich:")
    logger.info("aerich init -t app.settings.TORTOISE_ORM")
    logger.info("aerich migrate --name 'init'")

if __name__ == "__main__":
    asyncio.run(setup_database())