#!/bin/bash

# 系统资源监控脚本
# 基于系统检测分析过程创建
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 函数：打印分隔线
print_separator() {
    echo -e "${BLUE}============================================${NC}"
}

# 函数：打印标题
print_title() {
    echo -e "${CYAN}🔍 $1${NC}"
    print_separator
}

# 函数：获取内存使用百分比
get_mem_usage_percent() {
    local total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
    local used=$((total - available))
    echo $((used * 100 / total))
}

# 函数：获取负载平均值的状态
get_load_status() {
    local load=$1
    local cores=$2
    local threshold=$(echo "$cores * 2" | bc -l 2>/dev/null || echo $((cores * 2)))
    
    if (( $(echo "$load > $threshold" | bc -l 2>/dev/null || [ ${load%.*} -gt $threshold ]) )); then
        echo -e "${RED}高负载${NC}"
    elif (( $(echo "$load > $cores" | bc -l 2>/dev/null || [ ${load%.*} -gt $cores ]) )); then
        echo -e "${YELLOW}中等负载${NC}"
    else
        echo -e "${GREEN}正常${NC}"
    fi
}

# 函数：格式化字节数
format_bytes() {
    local bytes=$1
    if [ $bytes -ge 1073741824 ]; then
        echo "$(echo "scale=1; $bytes/1073741824" | bc -l 2>/dev/null || awk "BEGIN {printf \"%.1f\", $bytes/1073741824}")GB"
    elif [ $bytes -ge 1048576 ]; then
        echo "$(echo "scale=1; $bytes/1048576" | bc -l 2>/dev/null || awk "BEGIN {printf \"%.1f\", $bytes/1048576}")MB"
    else
        echo "$(echo "scale=1; $bytes/1024" | bc -l 2>/dev/null || awk "BEGIN {printf \"%.1f\", $bytes/1024}")KB"
    fi
}

# 主监控函数
main_monitor() {
    clear
    echo -e "${PURPLE}╔═══════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                 🖥️  系统资源监控报告                  ║${NC}"
    echo -e "${PURPLE}║                   $(date '+%Y-%m-%d %H:%M:%S')                    ║${NC}"
    echo -e "${PURPLE}╚═══════════════════════════════════════════════════════╝${NC}"
    echo ""

    # 1. 系统基本信息
    print_title "系统基本信息"
    echo -e "🖥️  主机名: $(hostname)"
    echo -e "⏰ 系统运行时间: $(uptime | awk -F'up ' '{print $2}' | awk -F',' '{print $1}')"
    echo -e "👥 当前用户数: $(who | wc -l)"
    echo -e "🔧 内核版本: $(uname -r)"
    echo -e "💻 CPU核心数: $(nproc)"
    echo ""

    # 2. 内存使用情况
    print_title "内存使用情况"
    
    # 从/proc/meminfo获取详细信息
    local mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_free=$(grep MemFree /proc/meminfo | awk '{print $2}')
    local mem_available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
    local mem_cached=$(grep "^Cached:" /proc/meminfo | awk '{print $2}')
    local mem_buffers=$(grep Buffers /proc/meminfo | awk '{print $2}')
    local swap_total=$(grep SwapTotal /proc/meminfo | awk '{print $2}')
    local swap_free=$(grep SwapFree /proc/meminfo | awk '{print $2}')
    
    local mem_used=$((mem_total - mem_available))
    local mem_usage_percent=$(get_mem_usage_percent)
    local swap_used=$((swap_total - swap_free))
    local swap_usage_percent=0
    
    if [ $swap_total -gt 0 ]; then
        swap_usage_percent=$((swap_used * 100 / swap_total))
    fi
    
    echo -e "📊 总内存: $(format_bytes $((mem_total * 1024)))"
    echo -e "💾 已用内存: $(format_bytes $((mem_used * 1024))) (${mem_usage_percent}%)"
    echo -e "🆓 可用内存: $(format_bytes $((mem_available * 1024)))"
    echo -e "💰 空闲内存: $(format_bytes $((mem_free * 1024)))"
    echo -e "📝 缓存内存: $(format_bytes $((mem_cached * 1024)))"
    echo -e "🔄 交换空间: $(format_bytes $((swap_total * 1024))) (已用: ${swap_usage_percent}%)"
    
    # 内存状态评估
    if [ $mem_usage_percent -gt 90 ]; then
        echo -e "⚠️  内存状态: ${RED}危险 - 内存使用率过高${NC}"
    elif [ $mem_usage_percent -gt 80 ]; then
        echo -e "⚠️  内存状态: ${YELLOW}警告 - 内存使用率偏高${NC}"
    else
        echo -e "✅ 内存状态: ${GREEN}正常${NC}"
    fi
    echo ""

    # 3. CPU负载情况
    print_title "CPU负载情况"
    local load_info=$(uptime | awk -F'load average:' '{print $2}' | sed 's/^ *//')
    local load_1min=$(echo $load_info | awk -F', ' '{print $1}')
    local load_5min=$(echo $load_info | awk -F', ' '{print $2}')
    local load_15min=$(echo $load_info | awk -F', ' '{print $3}')
    local cpu_cores=$(nproc)
    
    echo -e "🔥 1分钟负载: $load_1min $(get_load_status $load_1min $cpu_cores)"
    echo -e "🔥 5分钟负载: $load_5min $(get_load_status $load_5min $cpu_cores)"
    echo -e "🔥 15分钟负载: $load_15min $(get_load_status $load_15min $cpu_cores)"
    echo -e "💻 CPU核心数: $cpu_cores"
    echo ""

    # 4. 磁盘使用情况
    print_title "磁盘使用情况"
    df -h | grep -E "^(/dev/|tmpfs)" | while read line; do
        local usage=$(echo $line | awk '{print $5}' | sed 's/%//')
        local mount=$(echo $line | awk '{print $6}')
        local size=$(echo $line | awk '{print $2}')
        local used=$(echo $line | awk '{print $3}')
        local avail=$(echo $line | awk '{print $4}')
        
        if [ $usage -gt 90 ]; then
            echo -e "💾 $mount: $used/$size (${RED}$usage%${NC}) - 剩余: $avail"
        elif [ $usage -gt 80 ]; then
            echo -e "💾 $mount: $used/$size (${YELLOW}$usage%${NC}) - 剩余: $avail"
        else
            echo -e "💾 $mount: $used/$size (${GREEN}$usage%${NC}) - 剩余: $avail"
        fi
    done
    echo ""

    # 5. 最高内存占用进程
    print_title "内存占用TOP 8进程"
    echo -e "${YELLOW}PID     用户    %MEM   内存(MB)  进程名${NC}"
    ps aux --sort=-%mem --no-headers | head -8 | while read line; do
        local user=$(echo $line | awk '{print $1}')
        local pid=$(echo $line | awk '{print $2}')
        local mem_percent=$(echo $line | awk '{print $4}')
        local mem_kb=$(echo $line | awk '{print $6}')
        local mem_mb=$((mem_kb / 1024))
        local command=$(echo $line | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}' | cut -c1-50)
        
        printf "%-7s %-7s %-6s %-9s %s\n" "$pid" "$user" "$mem_percent%" "${mem_mb}MB" "$command"
    done
    echo ""

    # 6. 最高CPU占用进程
    print_title "CPU占用TOP 8进程"
    echo -e "${YELLOW}PID     用户    %CPU   进程名${NC}"
    ps aux --sort=-%cpu --no-headers | head -8 | while read line; do
        local user=$(echo $line | awk '{print $1}')
        local pid=$(echo $line | awk '{print $2}')
        local cpu_percent=$(echo $line | awk '{print $3}')
        local command=$(echo $line | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}' | cut -c1-50)
        
        printf "%-7s %-7s %-6s %s\n" "$pid" "$user" "$cpu_percent%" "$command"
    done
    echo ""

    # 7. Django进程检查
    print_title "Django应用进程分析"
    local django_count=$(ps aux | grep -i django | grep -v grep | grep -v systemctl | grep -v journalctl | wc -l)
    
    if [ $django_count -gt 0 ]; then
        echo -e "🐍 发现 ${YELLOW}$django_count${NC} 个Django相关进程"
        
        # 按路径分组统计
        echo -e "\n📁 按应用路径分组:"
        ps aux | grep -i django | grep -v grep | grep -v systemctl | grep -v journalctl | awk '{print $11}' | sort | uniq -c | while read count path; do
            echo -e "   $path: ${YELLOW}$count${NC} 个进程"
        done
        
        # 计算Django进程总内存使用
        local django_mem=$(ps aux | grep -i django | grep -v grep | grep -v systemctl | grep -v journalctl | awk '{sum += $6} END {printf "%.1f", sum/1024}')
        echo -e "\n💾 Django进程总内存使用: ${YELLOW}${django_mem}MB${NC}"
        
        # 检查是否有重复进程
        local unique_apps=$(ps aux | grep -i django | grep -v grep | grep -v systemctl | grep -v journalctl | awk '{print $11}' | sort | uniq | wc -l)
        if [ $unique_apps -lt $django_count ]; then
            echo -e "⚠️  ${RED}检测到重复的Django应用，建议优化${NC}"
        fi
    else
        echo -e "✅ 未发现Django进程"
    fi
    echo ""

    # 8. 网络端口监听
    print_title "网络端口监听情况"
    echo -e "${YELLOW}端口    协议   进程${NC}"
    ss -tlnp | grep LISTEN | awk '{print $4, $1, $7}' | sed 's/.*://g' | sed 's/users:(("//g' | sed 's/",.*//g' | sort -n | head -10 | while read port proto process; do
        printf "%-7s %-6s %s\n" "$port" "$proto" "$process"
    done
    echo ""

    # 9. 系统建议
    print_title "系统优化建议"
    
    if [ $mem_usage_percent -gt 85 ]; then
        echo -e "🔴 ${RED}内存使用率过高 (${mem_usage_percent}%)，建议:${NC}"
        echo -e "   • 检查并关闭不必要的进程"
        echo -e "   • 考虑增加系统内存"
        echo -e "   • 优化应用程序内存使用"
    fi
    
    if [ $django_count -gt 10 ]; then
        echo -e "🔶 ${YELLOW}Django进程过多 ($django_count个)，建议:${NC}"
        echo -e "   • 检查是否有重复部署的应用"
        echo -e "   • 合并或停止不必要的应用实例"
    fi
    
    local high_load=$(echo "$load_15min > $(nproc)" | bc -l 2>/dev/null || [ ${load_15min%.*} -gt $(nproc) ])
    if [ "$high_load" = "1" ] 2>/dev/null || [ ${load_15min%.*} -gt $(nproc) ] 2>/dev/null; then
        echo -e "🔴 ${RED}系统负载过高，建议:${NC}"
        echo -e "   • 检查CPU密集型进程"
        echo -e "   • 考虑优化应用性能"
        echo -e "   • 必要时升级硬件配置"
    fi
    
    echo -e "✅ ${GREEN}监控完成 - $(date '+%H:%M:%S')${NC}"
    print_separator
}

# 函数：实时监控模式
realtime_monitor() {
    while true; do
        main_monitor
        echo -e "\n${CYAN}实时监控中... 按 Ctrl+C 退出${NC}"
        sleep 5
    done
}

# 函数：显示帮助信息
show_help() {
    echo -e "${CYAN}系统资源监控脚本使用说明:${NC}"
    echo ""
    echo -e "${YELLOW}用法:${NC}"
    echo -e "  $0 [选项]"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo -e "  -h, --help     显示此帮助信息"
    echo -e "  -r, --realtime 实时监控模式 (每5秒刷新)"
    echo -e "  -o, --output   输出到文件"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo -e "  $0                    # 执行一次监控"
    echo -e "  $0 -r                 # 实时监控模式"
    echo -e "  $0 -o report.txt      # 输出到文件"
}

# 主程序
case "$1" in
    -h|--help)
        show_help
        ;;
    -r|--realtime)
        realtime_monitor
        ;;
    -o|--output)
        if [ -z "$2" ]; then
            echo -e "${RED}错误: 请指定输出文件名${NC}"
            exit 1
        fi
        main_monitor > "$2"
        echo -e "${GREEN}监控报告已保存到: $2${NC}"
        ;;
    "")
        main_monitor
        ;;
    *)
        echo -e "${RED}未知选项: $1${NC}"
        show_help
        exit 1
        ;;
esac 