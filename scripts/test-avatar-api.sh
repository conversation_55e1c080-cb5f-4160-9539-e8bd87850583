#!/bin/bash

# ========================================
# 头像上传API测试脚本
# ========================================
# 
# 此脚本用于测试生产环境的头像上传API是否正常工作
# 使用前请确保已获得有效的JWT token
#

# 配置
API_BASE_URL="${1:-https://your-api-server.com}"
JWT_TOKEN="${2:-your-jwt-token}"
TEST_IMAGE="${3:-test-avatar.jpg}"

if [ "$#" -lt 2 ]; then
    echo "使用方法: $0 <API_BASE_URL> <JWT_TOKEN> [TEST_IMAGE]"
    echo ""
    echo "示例:"
    echo "  $0 https://api.example.com eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... test.jpg"
    echo ""
    exit 1
fi

echo "🧪 开始测试头像上传API..."
echo "API地址: $API_BASE_URL"
echo "测试图片: $TEST_IMAGE"
echo ""

# 检查测试图片是否存在
if [ ! -f "$TEST_IMAGE" ]; then
    echo "❌ 测试图片不存在: $TEST_IMAGE"
    echo "请提供一个有效的图片文件"
    exit 1
fi

# 检查图片文件大小
FILE_SIZE=$(stat -f%z "$TEST_IMAGE" 2>/dev/null || stat -c%s "$TEST_IMAGE" 2>/dev/null)
MAX_SIZE=$((5 * 1024 * 1024))  # 5MB

if [ "$FILE_SIZE" -gt "$MAX_SIZE" ]; then
    echo "❌ 测试图片过大: $(($FILE_SIZE / 1024 / 1024))MB (最大5MB)"
    exit 1
fi

echo "✅ 测试图片大小: $(($FILE_SIZE / 1024))KB"

# 测试API连通性
echo "🔍 测试API连通性..."
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE_URL/api/user/profile" \
    -H "Authorization: Bearer $JWT_TOKEN")

if [ "$HTTP_CODE" != "200" ]; then
    echo "❌ API连通性测试失败 (HTTP $HTTP_CODE)"
    echo "请检查API地址和JWT token是否正确"
    exit 1
fi

echo "✅ API连通性正常"

# 测试头像上传
echo "📤 测试头像上传..."
RESPONSE=$(curl -s -w "\n%{http_code}" "$API_BASE_URL/api/user/avatar" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -F "avatar=@$TEST_IMAGE")

# 分离响应体和状态码
HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
RESPONSE_BODY=$(echo "$RESPONSE" | head -n -1)

echo "HTTP状态码: $HTTP_CODE"
echo "响应内容: $RESPONSE_BODY"

# 检查响应
case $HTTP_CODE in
    200)
        echo "✅ 头像上传成功!"
        
        # 尝试解析avatarUrl
        if command -v jq >/dev/null 2>&1; then
            AVATAR_URL=$(echo "$RESPONSE_BODY" | jq -r '.avatarUrl // empty')
            if [ -n "$AVATAR_URL" ]; then
                echo "头像URL: $AVATAR_URL"
                
                # 测试头像URL是否可访问
                echo "🔍 测试头像URL可访问性..."
                AVATAR_HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$AVATAR_URL")
                if [ "$AVATAR_HTTP_CODE" = "200" ]; then
                    echo "✅ 头像URL可正常访问"
                else
                    echo "⚠️ 头像URL访问异常 (HTTP $AVATAR_HTTP_CODE)"
                fi
            fi
        fi
        ;;
    400)
        echo "❌ 请求错误: 文件格式不支持或参数错误"
        ;;
    401)
        echo "❌ 认证失败: JWT token无效或已过期"
        ;;
    413)
        echo "❌ 文件过大: 超出5MB限制"
        ;;
    429)
        echo "❌ 请求过于频繁: 触发频率限制"
        ;;
    500)
        echo "❌ 服务器内部错误"
        ;;
    *)
        echo "❌ 未知错误 (HTTP $HTTP_CODE)"
        ;;
esac

echo ""
echo "🏁 测试完成"

# 返回适当的退出码
if [ "$HTTP_CODE" = "200" ]; then
    exit 0
else
    exit 1
fi
