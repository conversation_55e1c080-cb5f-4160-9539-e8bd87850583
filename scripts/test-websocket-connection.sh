#!/bin/bash

# WebSocket 连接测试脚本
# 用于验证 Nginx WebSocket 代理配置是否正确

echo "=== WebSocket 连接测试脚本 ==="
echo "测试时间: $(date)"
echo ""

# 测试用户ID
USER_ID="TEST_USER_$(date +%s)"
echo "测试用户ID: $USER_ID"
echo ""

# 1. 测试本地 WebSocket 连接
echo "1. 测试本地 WebSocket 连接 (http://localhost:9999)..."
timeout 5 curl -i -N \
  -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Version: 13" \
  -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
  "http://localhost:9999/api/v1/airelief/chat/ws/$USER_ID" 2>/dev/null | head -10

echo ""
echo "本地连接测试完成"
echo ""

# 2. 测试 HTTPS WebSocket 连接
echo "2. 测试 HTTPS WebSocket 连接 (https://www.airelief.cn:8997)..."
timeout 5 curl -i -N \
  -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Version: 13" \
  -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
  "https://www.airelief.cn:8997/api/v1/airelief/chat/ws/$USER_ID" 2>/dev/null | head -10

echo ""
echo "HTTPS 连接测试完成"
echo ""

# 3. 测试普通 HTTP API
echo "3. 测试普通 HTTP API..."
HTTP_RESPONSE=$(curl -s -w "HTTP_CODE:%{http_code}" "https://www.airelief.cn:8997/api/v1/airelief/chat/test-ai-connection")
echo "API 响应: $HTTP_RESPONSE"
echo ""

# 4. 检查 Nginx 错误日志（如果有权限）
echo "4. 检查可能的错误信息..."
if [ -f "/var/log/nginx/error.log" ]; then
    echo "最近的 Nginx 错误日志:"
    tail -5 /var/log/nginx/error.log 2>/dev/null || echo "无法读取 Nginx 错误日志"
else
    echo "未找到 Nginx 错误日志文件"
fi
echo ""

# 5. 检查端口监听状态
echo "5. 检查端口监听状态..."
echo "端口 9999 (后端服务):"
netstat -tlnp | grep :9999 || echo "端口 9999 未监听"
echo "端口 8997 (Nginx 代理):"
netstat -tlnp | grep :8997 || echo "端口 8997 未监听"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "如果本地连接成功但 HTTPS 连接失败，说明需要修复 Nginx WebSocket 配置"
echo "请参考 nginx-websocket-fix.conf 文件中的配置说明"
