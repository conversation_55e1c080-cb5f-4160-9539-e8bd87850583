
#!/usr/bin/env python3
"""
支付套餐API测试脚本
使用方法：python scripts/test_payment_api.py
"""
import asyncio
import json
import os
import sys
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import httpx
from app.core.init_app import init_db
from app.models.airelief import PaymentPackage
from tortoise import Tortoise


class PaymentAPITester:
    """支付套餐API测试器"""
    
    def __init__(self, base_url="http://localhost:9999"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(base_url=base_url)
    
    async def check_existing_data(self):
        """检查现有数据"""
        print("检查数据库中的现有套餐...")

        # 获取所有套餐
        all_packages = await PaymentPackage.all()
        active_packages = await PaymentPackage.filter(is_active=True)

        print(f"📊 数据库统计:")
        print(f"  总套餐数: {len(all_packages)}")
        print(f"  激活套餐数: {len(active_packages)}")
        print(f"  未激活套餐数: {len(all_packages) - len(active_packages)}")

        if len(all_packages) == 0:
            print("⚠️ 数据库中没有套餐数据，请先运行初始化脚本:")
            print("   python scripts/init_payment_packages.py")
            return False

        # 显示套餐详情
        print("\n📦 现有套餐列表:")
        for pkg in all_packages:
            status = "✅激活" if pkg.is_active else "❌未激活"
            print(f"  {pkg.id}. {pkg.name} - {pkg.duration} - ¥{pkg.price} - {status}")

        return True
    
    async def test_get_active_packages(self):
        """测试获取激活的支付套餐"""
        print("\n📦 测试获取激活的支付套餐...")
        
        try:
            response = await self.client.get("/api/v1/airelief/payment/packages")
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 验证响应结构
                assert "code" in data, "响应缺少code字段"
                assert "msg" in data, "响应缺少msg字段"
                assert "data" in data, "响应缺少data字段"
                assert data["code"] == 200, f"期望code为200，实际为{data['code']}"
                
                packages = data["data"]
                assert isinstance(packages, list), "data字段应该是列表"
                
                # 应该只返回激活的套餐（3个）
                assert len(packages) == 3, f"期望返回3个激活套餐，实际返回{len(packages)}个"
                
                # 验证套餐字段
                for pkg in packages:
                    required_fields = ["id", "name", "duration", "duration_seconds", 
                                     "price", "is_active", "sort_order"]
                    for field in required_fields:
                        assert field in pkg, f"套餐缺少{field}字段"
                    
                    assert pkg["is_active"] is True, "返回的套餐应该都是激活状态"
                
                # 验证排序
                sort_orders = [pkg["sort_order"] for pkg in packages]
                assert sort_orders == sorted(sort_orders), "套餐应该按sort_order排序"
                
                print("✅ 获取激活套餐测试通过")
                return True
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            return False
    
    async def test_get_all_packages(self):
        """测试获取所有支付套餐"""
        print("\n📦 测试获取所有支付套餐...")
        
        try:
            response = await self.client.get("/api/v1/airelief/payment/packages/all")
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 验证响应结构
                assert data["code"] == 200, f"期望code为200，实际为{data['code']}"
                
                packages = data["data"]
                assert isinstance(packages, list), "data字段应该是列表"
                
                # 应该返回所有套餐（包括未激活的，共4个）
                assert len(packages) == 4, f"期望返回4个套餐，实际返回{len(packages)}个"
                
                # 验证包含激活和未激活的套餐
                active_count = sum(1 for pkg in packages if pkg["is_active"])
                inactive_count = sum(1 for pkg in packages if not pkg["is_active"])
                assert active_count == 3, f"期望3个激活套餐，实际{active_count}个"
                assert inactive_count == 1, f"期望1个未激活套餐，实际{inactive_count}个"
                
                print("✅ 获取所有套餐测试通过")
                return True
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            return False
    
    async def test_data_validation(self):
        """测试数据验证"""
        print("\n🔍 测试数据验证...")
        
        try:
            response = await self.client.get("/api/v1/airelief/payment/packages")
            
            if response.status_code == 200:
                data = response.json()
                packages = data["data"]
                
                for pkg in packages:
                    # 验证数据类型
                    assert isinstance(pkg["id"], int), f"id应该是整数，实际为{type(pkg['id'])}"
                    assert isinstance(pkg["name"], str), f"name应该是字符串，实际为{type(pkg['name'])}"
                    assert isinstance(pkg["duration"], str), f"duration应该是字符串，实际为{type(pkg['duration'])}"
                    assert isinstance(pkg["duration_seconds"], int), f"duration_seconds应该是整数，实际为{type(pkg['duration_seconds'])}"
                    assert isinstance(pkg["price"], (int, float)), f"price应该是数字，实际为{type(pkg['price'])}"
                    assert isinstance(pkg["is_active"], bool), f"is_active应该是布尔值，实际为{type(pkg['is_active'])}"
                    assert isinstance(pkg["sort_order"], int), f"sort_order应该是整数，实际为{type(pkg['sort_order'])}"
                    
                    # 验证业务逻辑
                    assert pkg["price"] > 0, f"价格应该大于0，实际为{pkg['price']}"
                    assert pkg["duration_seconds"] > 0, f"时长秒数应该大于0，实际为{pkg['duration_seconds']}"
                    
                    if pkg["original_price"] is not None:
                        assert pkg["original_price"] >= pkg["price"], f"原价应该大于等于现价"
                
                print("✅ 数据验证测试通过")
                return True
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 数据验证异常: {str(e)}")
            return False
    
    async def test_api_availability(self):
        """测试API可用性"""
        print("\n🌐 测试API可用性...")
        
        endpoints = [
            "/api/v1/airelief/payment/packages",
            "/api/v1/airelief/payment/packages/all"
        ]
        
        results = []
        for endpoint in endpoints:
            try:
                response = await self.client.get(endpoint)
                if response.status_code == 200:
                    print(f"✅ {endpoint} - 可用")
                    results.append(True)
                else:
                    print(f"❌ {endpoint} - 状态码: {response.status_code}")
                    results.append(False)
            except Exception as e:
                print(f"❌ {endpoint} - 异常: {str(e)}")
                results.append(False)
        
        return all(results)
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行支付套餐API测试...")
        
        # 初始化数据库
        await init_db()
        
        # 设置测试数据
        await self.setup_test_data()
        
        # 运行测试
        test_results = []
        
        test_results.append(await self.test_api_availability())
        test_results.append(await self.test_get_active_packages())
        test_results.append(await self.test_get_all_packages())
        test_results.append(await self.test_data_validation())
        
        # 统计结果
        passed = sum(test_results)
        total = len(test_results)
        
        print(f"\n📊 测试结果统计:")
        print(f"总测试数: {total}")
        print(f"通过数: {passed}")
        print(f"失败数: {total - passed}")
        print(f"通过率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("🎉 所有测试通过！")
        else:
            print("⚠️ 部分测试失败，请检查上述错误信息")
        
        # 关闭连接
        await self.client.aclose()
        await Tortoise.close_connections()
        
        return passed == total


async def main():
    """主函数"""
    print("=" * 60)
    print("📦 AIRelief 支付套餐API测试脚本")
    print("=" * 60)
    
    # 检查服务是否运行
    print("🔍 检查API服务状态...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:9999/api/v1/base/test", timeout=5.0)
            if response.status_code == 200:
                print("✅ API服务运行正常")
            else:
                print(f"⚠️ API服务响应异常，状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到API服务: {str(e)}")
        print("请确保API服务已启动 (python run.py)")
        return
    
    # 运行测试
    tester = PaymentAPITester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎊 测试完成，所有测试通过！")
    else:
        print("\n💥 测试完成，存在失败的测试项")


if __name__ == "__main__":
    asyncio.run(main())
