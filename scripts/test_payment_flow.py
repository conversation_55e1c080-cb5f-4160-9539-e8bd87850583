#!/usr/bin/env python3
"""
支付流程完整测试脚本
支持Mock模式和真实模式测试
"""

import asyncio
import httpx
import json
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.mock_wechat_pay_service import MockWeChatPayService
from app.models.airelief import AIReliefUser, PaymentPackage, Coupon, Order
from tortoise import Tortoise
from app.settings.config import settings


class PaymentFlowTester:
    """支付流程测试器"""
    
    def __init__(self, base_url="http://localhost:9999", use_mock=True):
        self.base_url = base_url
        self.use_mock = use_mock
        self.client = httpx.AsyncClient(timeout=30.0)
        self.mock_service = MockWeChatPayService() if use_mock else None
        
    async def setup_test_data(self):
        """准备测试数据"""
        print("\n🔧 准备测试数据...")
        
        try:
            # 创建测试用户
            user_data = {
                "user_id": "test_pay_user",  # 修正长度为12字符，符合16字符限制
                "nickname": "支付测试用户",
                "phone": "13800138000",
                "wechat_openid": "test_openid_payment_123",
                "is_new_user": True,
                "duration": 0,
                "total_duration": 0
            }
            
            # 检查用户是否已存在
            user = await AIReliefUser.get_or_none(user_id=user_data["user_id"])
            if not user:
                user = await AIReliefUser.create(**user_data)
                print(f"✅ 创建测试用户: {user.user_id}")
            else:
                print(f"✅ 使用现有测试用户: {user.user_id}")
            
            # 创建测试套餐
            package_data = {
                "name": "支付测试套餐",
                "duration": "30分钟",
                "duration_seconds": 1800,
                "price": 0.01 if self.use_mock else 9.90,  # Mock模式使用0.01元
                "original_price": 19.90,
                "tag": "测试",
                "is_active": True,
                "sort_order": 999
            }
            
            package = await PaymentPackage.get_or_none(name=package_data["name"])
            if not package:
                package = await PaymentPackage.create(**package_data)
                print(f"✅ 创建测试套餐: {package.name} - {package.price}元")
            else:
                print(f"✅ 使用现有测试套餐: {package.name} - {package.price}元")
            
            # 创建测试优惠券
            coupon_data = {
                "code": "TEST_DISCOUNT_NEW",
                "type": "discount",
                "value": 3.00,
                "description": "支付测试专用优惠券",
                "max_uses": 100,
                "used_count": 0,
                "valid_from": datetime.now(),
                "valid_until": datetime.now() + timedelta(days=30),
                "is_active": True
            }
            
            coupon = await Coupon.get_or_none(code=coupon_data["code"])
            if not coupon:
                coupon = await Coupon.create(**coupon_data)
                print(f"✅ 创建测试优惠券: {coupon.code} - 减{coupon.value}元")
            else:
                print(f"✅ 使用现有测试优惠券: {coupon.code} - 减{coupon.value}元")
            
            return {
                "user": user,
                "package": package,
                "coupon": coupon
            }
            
        except Exception as e:
            print(f"❌ 准备测试数据失败: {str(e)}")
            return None
    
    async def test_api_availability(self):
        """测试API可用性"""
        print("\n🌐 测试API可用性...")
        
        endpoints = [
            "/api/v1/airelief/payment/packages",
            "/api/v1/airelief/payment/packages/all"
        ]
        
        for endpoint in endpoints:
            try:
                response = await self.client.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    print(f"✅ {endpoint} - 可用")
                else:
                    print(f"❌ {endpoint} - 状态码: {response.status_code}")
                    return False
            except Exception as e:
                print(f"❌ {endpoint} - 异常: {str(e)}")
                return False
        
        return True
    
    async def test_get_packages(self):
        """测试获取支付套餐"""
        print("\n📦 测试获取支付套餐...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/airelief/payment/packages")
            
            if response.status_code == 200:
                data = response.json()
                packages = data["data"]
                print(f"✅ 获取到 {len(packages)} 个激活套餐")
                
                # 显示套餐信息
                for pkg in packages:
                    print(f"   - {pkg['name']}: {pkg['price']}元 ({pkg['duration']})")
                
                return packages
            else:
                print(f"❌ 获取套餐失败: {response.status_code}")
                print(f"响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取套餐异常: {str(e)}")
            return None
    
    async def test_create_order(self, user_id, package_id, coupon_code=None):
        """测试创建支付订单"""
        print(f"\n💰 测试创建支付订单...")
        print(f"   用户ID: {user_id}")
        print(f"   套餐ID: {package_id}")
        print(f"   优惠券: {coupon_code or '无'}")
        
        try:
            # 使用查询参数而不是JSON body
            params = {
                "user_id": user_id,
                "package_id": package_id
            }
            if coupon_code:
                params["coupon_code"] = coupon_code
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/airelief/payment/create-order",
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                order_info = data["data"]
                order_id = order_info["order_id"]
                pay_params = order_info["pay_params"]
                
                print(f"✅ 订单创建成功:")
                print(f"   订单号: {order_id}")
                print(f"   AppId: {pay_params['appId']}")
                print(f"   时间戳: {pay_params['timeStamp']}")
                print(f"   随机字符串: {pay_params['nonceStr']}")
                print(f"   Package: {pay_params['package']}")
                print(f"   签名类型: {pay_params['signType']}")
                print(f"   签名: {pay_params['paySign'][:20]}...")
                
                return order_id
            else:
                print(f"❌ 创建订单失败: {response.status_code}")
                print(f"响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 创建订单异常: {str(e)}")
            return None
    
    async def test_query_order(self, order_id):
        """测试查询订单状态"""
        print(f"\n🔍 测试查询订单状态: {order_id}")
        
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/airelief/payment/orders/{order_id}/status"
            )
            
            if response.status_code == 200:
                data = response.json()
                order_info = data["data"]
                
                print(f"✅ 订单状态查询成功:")
                print(f"   订单号: {order_info['order_id']}")
                print(f"   状态: {order_info['status']}")
                print(f"   金额: {order_info['amount']}元")
                print(f"   支付时间: {order_info.get('pay_time', '未支付')}")
                
                return order_info
            else:
                print(f"❌ 查询订单失败: {response.status_code}")
                print(f"响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 查询订单异常: {str(e)}")
            return None
    
    async def test_mock_payment(self, order_id):
        """测试Mock支付（仅Mock模式）"""
        if not self.use_mock:
            print("⚠️ 非Mock模式，跳过Mock支付测试")
            return False
        
        print(f"\n🧪 测试Mock支付: {order_id}")
        
        try:
            # 使用Mock服务模拟支付成功
            success = await self.mock_service.mock_payment_success(order_id)
            
            if success:
                print("✅ Mock支付成功")
                return True
            else:
                print("❌ Mock支付失败")
                return False
                
        except Exception as e:
            print(f"❌ Mock支付异常: {str(e)}")
            return False
    
    async def test_payment_callback(self, order_id):
        """测试支付回调"""
        print(f"\n📞 测试支付回调: {order_id}")
        
        try:
            if self.use_mock:
                # Mock模式：直接传订单号
                callback_data = order_id
            else:
                # 真实模式：构造微信支付回调数据
                callback_data = {
                    "event_type": "TRANSACTION.SUCCESS",
                    "resource": {
                        "out_trade_no": order_id,
                        "transaction_id": f"wx_test_{order_id}",
                        "trade_state": "SUCCESS",
                        "bank_type": "CMB_CREDIT",
                        "amount": {"total": 1}  # 0.01元 = 1分
                    }
                }
            
            headers = {
                "Content-Type": "application/json",
                "Wechatpay-Signature": "test_signature",
                "Wechatpay-Timestamp": str(int(datetime.now().timestamp())),
                "Wechatpay-Nonce": "test_nonce",
                "Wechatpay-Serial": "test_serial"
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/airelief/payment/notify",
                json=callback_data if not self.use_mock else None,
                data=callback_data if self.use_mock else None,
                headers=headers
            )
            
            print(f"回调响应状态: {response.status_code}")
            print(f"回调响应内容: {response.text}")
            
            # 检查响应是否成功
            if response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == "SUCCESS":
                    print("✅ 支付回调处理成功")
                    return True
            
            print("❌ 支付回调处理失败")
            return False
            
        except Exception as e:
            print(f"❌ 支付回调异常: {str(e)}")
            return False
    
    async def test_complete_flow(self):
        """测试完整支付流程"""
        print("=" * 60)
        print(f"🚀 开始完整支付流程测试 ({'Mock模式' if self.use_mock else '真实模式'})")
        print("=" * 60)
        
        # 1. 准备测试数据
        test_data = await self.setup_test_data()
        if not test_data:
            print("❌ 测试数据准备失败，终止测试")
            return False
        
        user = test_data["user"]
        package = test_data["package"]
        coupon = test_data["coupon"]
        
        # 2. 测试API可用性
        if not await self.test_api_availability():
            print("❌ API不可用，终止测试")
            return False
        
        # 3. 获取支付套餐
        packages = await self.test_get_packages()
        if not packages:
            print("❌ 获取支付套餐失败，终止测试")
            return False
        
        # 4. 创建支付订单（不使用优惠券）
        print("\n" + "=" * 40)
        print("测试场景1: 普通支付订单")
        print("=" * 40)
        
        order_id_1 = await self.test_create_order(user.user_id, package.id)
        if not order_id_1:
            print("❌ 创建普通订单失败")
            return False
        
        # 5. 查询订单状态（待支付）
        order_status_1 = await self.test_query_order(order_id_1)
        if not order_status_1 or order_status_1["status"] != "pending":
            print("❌ 订单状态异常")
            return False
        
        # 6. 模拟支付成功
        if self.use_mock:
            payment_success_1 = await self.test_mock_payment(order_id_1)
        else:
            payment_success_1 = await self.test_payment_callback(order_id_1)
        
        if not payment_success_1:
            print("❌ 支付处理失败")
            return False
        
        # 7. 再次查询订单状态（已支付）
        await asyncio.sleep(1)  # 等待状态更新
        final_status_1 = await self.test_query_order(order_id_1)
        if not final_status_1 or final_status_1["status"] != "paid":
            print("❌ 支付后订单状态未正确更新")
            return False
        
        # 8. 创建支付订单（使用优惠券）
        print("\n" + "=" * 40)
        print("测试场景2: 使用优惠券支付")
        print("=" * 40)
        
        order_id_2 = await self.test_create_order(user.user_id, package.id, coupon.code)
        if not order_id_2:
            print("❌ 创建优惠券订单失败")
            return False
        
        # 9. 模拟第二个订单支付成功
        if self.use_mock:
            payment_success_2 = await self.test_mock_payment(order_id_2)
        else:
            payment_success_2 = await self.test_payment_callback(order_id_2)
        
        if not payment_success_2:
            print("❌ 优惠券订单支付处理失败")
            return False
        
        # 10. 验证最终结果
        await asyncio.sleep(1)
        final_status_2 = await self.test_query_order(order_id_2)
        if not final_status_2 or final_status_2["status"] != "paid":
            print("❌ 优惠券订单支付后状态未正确更新")
            return False
        
        # 11. 验证用户时长是否正确增加
        await self.verify_user_duration(user.user_id, package.duration_seconds * 2)
        
        print("\n" + "=" * 60)
        print("🎉 完整支付流程测试成功！")
        print("=" * 60)
        print(f"✅ 普通订单: {order_id_1} - 状态: {final_status_1['status']}")
        print(f"✅ 优惠券订单: {order_id_2} - 状态: {final_status_2['status']}")
        
        return True
    
    async def verify_user_duration(self, user_id, expected_duration):
        """验证用户时长是否正确增加"""
        print(f"\n⏰ 验证用户时长...")
        
        try:
            user = await AIReliefUser.get(user_id=user_id)
            print(f"用户总时长: {user.total_duration}秒")
            print(f"预期增加: {expected_duration}秒")
            
            if user.total_duration >= expected_duration:
                print("✅ 用户时长增加正确")
                return True
            else:
                print("❌ 用户时长增加不正确")
                return False
                
        except Exception as e:
            print(f"❌ 验证用户时长异常: {str(e)}")
            return False
    
    async def test_error_scenarios(self):
        """测试错误场景"""
        print("\n" + "=" * 40)
        print("🔍 测试错误场景")
        print("=" * 40)
        
        # 测试不存在的用户
        print("\n测试1: 不存在的用户")
        result = await self.test_create_order("nonexistent_user", 1)
        if result is None:
            print("✅ 正确处理不存在的用户")
        else:
            print("❌ 未正确处理不存在的用户")
        
        # 测试不存在的套餐
        print("\n测试2: 不存在的套餐")
        result = await self.test_create_order("test_pay_user", 99999)
        if result is None:
            print("✅ 正确处理不存在的套餐")
        else:
            print("❌ 未正确处理不存在的套餐")
        
        # 测试无效优惠券
        print("\n测试3: 无效优惠券")
        result = await self.test_create_order("test_pay_user", 1, "INVALID_COUPON")
        if result is None:
            print("✅ 正确处理无效优惠券")
        else:
            print("❌ 未正确处理无效优惠券")
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def init_database():
    """初始化数据库连接"""
    await Tortoise.init(config=settings.TORTOISE_ORM)


async def close_database():
    """关闭数据库连接"""
    await Tortoise.close_connections()


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="支付流程测试脚本")
    parser.add_argument("--real", action="store_true", help="使用真实支付模式（默认为Mock模式）")
    parser.add_argument("--url", default="http://localhost:9999", help="API服务地址")
    parser.add_argument("--error-test", action="store_true", help="只运行错误场景测试")
    
    args = parser.parse_args()
    
    use_mock = not args.real
    
    print("支付流程测试脚本")
    print(f"模式: {'Mock模式' if use_mock else '真实模式'}")
    print(f"API地址: {args.url}")
    print()
    
    # 初始化数据库
    await init_database()
    
    try:
        # 检查API服务是否运行
        print("🔍 检查API服务状态...")
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{args.url}/api/v1/base/test", timeout=5.0)
            if response.status_code == 200:
                print("✅ API服务运行正常")
            else:
                print(f"⚠️ API服务响应异常，状态码: {response.status_code}")
        
        # 创建测试器
        tester = PaymentFlowTester(base_url=args.url, use_mock=use_mock)
        
        try:
            if args.error_test:
                # 只运行错误场景测试
                await tester.test_error_scenarios()
            else:
                # 运行完整流程测试
                success = await tester.test_complete_flow()
                
                if success:
                    # 如果完整流程成功，再测试错误场景
                    await tester.test_error_scenarios()
                    print("\n🎊 所有测试完成！")
                else:
                    print("\n💥 测试存在失败项")
                    
        finally:
            await tester.close()
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
    finally:
        await close_database()


if __name__ == "__main__":
    asyncio.run(main()) 