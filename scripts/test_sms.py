#!/usr/bin/env python3
"""
修正后的短信服务测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.utils.sms import sms_service


async def test_sms_simple():
    """简单测试短信发送"""
    print("🔧 开始测试修正后的短信服务...")
    
    # 测试手机号
    test_phone = "18194065858"  # 请替换为您的真实手机号
    
    print(f"📱 测试手机号: {test_phone}")
    
    # 测试发送验证码
    print("\n📨 发送验证码...")
    result = await sms_service.send_verification_code(test_phone)
    
    if result['success']:
        print(f"✅ 验证码发送成功!")
        print(f"📝 消息: {result['message']}")
        print(f"⏰ 过期时间: {result['data']['expires_in']}秒")
        
        # 显示生成的验证码（仅用于测试）
        if test_phone in sms_service._verification_codes:
            code_info = sms_service._verification_codes[test_phone]
            print(f"🔑 生成的验证码: {code_info['code']} (仅测试显示)")
        
    else:
        print(f"❌ 验证码发送失败: {result['message']}")
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    print("=" * 50)
    print("📨 AIRelief 短信服务测试 (修正版)")
    print("=" * 50)
    
    # 显示配置
    print(f"🔧 短信API地址: {sms_service.api_url}")
    print(f"🔧 API账号: {sms_service.secret_name}")
    print(f"🔧 短信签名: '{sms_service.sign_name}'")
    print(f"🔧 验证码长度: {sms_service.code_length}位")
    print(f"🔧 验证码过期时间: {sms_service.code_expire_minutes}分钟")
    
    print("\n⚠️  注意：这将发送真实短信并产生费用！")
    
    asyncio.run(test_sms_simple())
