<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue'
import { useUserStore } from '@/stores/user'
import { useWebSocketStore } from '@/stores/websocket'

const userStore = useUserStore()
const wsStore = useWebSocketStore()

// 简单的页面卸载处理
const handleBeforeUnload = () => {
  console.log('页面即将关闭，断开WebSocket连接')
  wsStore.disconnect()
}

// 页面隐藏处理（更可靠的离开检测）
const handlePageHide = () => {
  console.log('页面隐藏，断开WebSocket连接')
  wsStore.disconnect()
}

// 监听用户登录状态变化
watch(
  () => userStore.userInfo?.user_id,
  (newUserId, oldUserId) => {
    if (newUserId && newUserId !== oldUserId) {
      // 用户登录或切换，初始化WebSocket连接
      console.log('用户登录，初始化WebSocket连接')
      wsStore.initWebSocket(newUserId)
    } else if (!newUserId && oldUserId) {
      // 用户登出，断开WebSocket连接
      console.log('用户登出，断开WebSocket连接')
      wsStore.disconnect()
    }
  },
  { immediate: true }
)

onMounted(async () => {
  // 应用启动时初始化用户状态
  if (userStore.token) {
    await userStore.fetchUserInfo()
  }

  // 只监听最关键的两个事件
  window.addEventListener('beforeunload', handleBeforeUnload)
  window.addEventListener('pagehide', handlePageHide)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)
  window.removeEventListener('pagehide', handlePageHide)

  // 断开WebSocket连接
  wsStore.disconnect()
})
</script>

<template>
  <div id="app">
    <router-view />
  </div>
</template>


