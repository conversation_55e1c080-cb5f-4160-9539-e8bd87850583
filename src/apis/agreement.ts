import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'

// 协议类型定义
export interface PublicAgreementResponse {
  type: string
  title: string
  content: string
  version: string
  effective_date?: string
  updated_at: string
}

// 协议类型常量
export const AGREEMENT_TYPES = {
  USER_AGREEMENT: 'user_agreement',
  PRIVACY_POLICY: 'privacy_policy',
  PAYMENT_AGREEMENT: 'payment_agreement',
  DISCLAIMER: 'disclaimer',
} as const

export type AgreementType = typeof AGREEMENT_TYPES[keyof typeof AGREEMENT_TYPES]

/**
 * 获取公开协议内容
 * @param agreementType 协议类型
 */
export const getPublicAgreement = (agreementType: AgreementType): Promise<PublicAgreementResponse> => {
  return request.get(`${API_ENDPOINTS.GET_PUBLIC_AGREEMENT}/${agreementType}`)
}

/**
 * 获取用户服务协议
 */
export const getUserAgreement = (): Promise<PublicAgreementResponse> => {
  return getPublicAgreement(AGREEMENT_TYPES.USER_AGREEMENT)
}

/**
 * 获取隐私政策
 */
export const getPrivacyPolicy = (): Promise<PublicAgreementResponse> => {
  return getPublicAgreement(AGREEMENT_TYPES.PRIVACY_POLICY)
}

/**
 * 获取充值协议
 */
export const getPaymentAgreement = (): Promise<PublicAgreementResponse> => {
  return getPublicAgreement(AGREEMENT_TYPES.PAYMENT_AGREEMENT)
}

/**
 * 获取免责声明
 */
export const getDisclaimer = (): Promise<PublicAgreementResponse> => {
  return getPublicAgreement(AGREEMENT_TYPES.DISCLAIMER)
}
