import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'
import type { LoginResponse, UserInfo, AgreementStatus } from '@/types/api'

/**
 * 获取微信授权URL
 * @param scope 授权作用域 (可选，默认使用后端配置的snsapi_userinfo)
 * @param state 状态参数
 */
export const getWechatAuthUrl = (
  scope?: string,
  state?: string
): Promise<{ auth_url: string; scope: string; state?: string }> => {
  const params: Record<string, string> = {}
  if (scope) params.scope = scope
  if (state) params.state = state
  
  return request.get(API_ENDPOINTS.WECHAT_AUTH_URL, {
    params: Object.keys(params).length > 0 ? params : undefined
  })
}

/**
 * 微信登录
 * @param code 微信授权码
 */
export const wechatLogin = (code: string): Promise<LoginResponse> => {
  return request.post(API_ENDPOINTS.WECHAT_LOGIN, { code })
}

/**
 * 获取用户基本信息（用于认证相关场景）
 */
export const getUserInfo = (): Promise<UserInfo> => {
  return request.get(API_ENDPOINTS.USER_PROFILE)
}

/**
 * 退出登录
 */
export const logout = (): Promise<void> => {
  return request.post(API_ENDPOINTS.LOGOUT)
}

/**
 * 获取协议状态
 */
export const getAgreementStatus = (): Promise<AgreementStatus> => {
  return request.get(API_ENDPOINTS.AGREEMENT_STATUS)
}

/**
 * 更新协议同意状态
 */
export const updateAgreementStatus = (status: AgreementStatus): Promise<void> => {
  return request.post(API_ENDPOINTS.AGREEMENT_STATUS, status)
}

/**
 * 获取微信JS-SDK配置
 * @param url 当前页面URL
 */
export const getWechatJSApiConfig = (url: string): Promise<{
  appId: string;
  timestamp: string;
  nonceStr: string;
  signature: string;
  jsApiList: string[];
}> => {
  return request.get(API_ENDPOINTS.WECHAT_JSAPI_CONFIG, {
    params: { url }
  })
}

// 为了兼容现有代码，保留这些导出
export const login = wechatLogin