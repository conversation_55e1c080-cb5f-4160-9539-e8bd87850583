// ========================================
// 新用户试用激活API
// ========================================

import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'
import type { TrialActivationResponse } from '@/types/api'

/**
 * 激活新用户3天免费试用
 * POST /api/user/activate-trial
 */
export const activateNewUserTrial = (): Promise<TrialActivationResponse> => {
  return request({
    url: API_ENDPOINTS.ACTIVATE_TRIAL,
    method: 'POST'
  })
}

// ========================================
// 生产环境实现说明
// ========================================
//
// POST /api/user/activate-trial
// - 验证JWT token
// - 检查用户是否为新用户
// - 更新用户表：增加3天试用时长，设置过期时间
// - 将用户标记为非新用户
// - 返回更新后的用户信息
// 