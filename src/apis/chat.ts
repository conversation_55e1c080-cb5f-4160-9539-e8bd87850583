import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'
import type { ChatMessage } from '@/types/api'

/**
 * 获取聊天历史记录
 * @param page 页码
 * @param pageSize 每页数量
 */
export const getChatHistory = (
  page = 1,
  pageSize = 20
): Promise<{ messages: ChatMessage[]; total: number }> => {
  return request.get(API_ENDPOINTS.CHAT_HISTORY, { params: { page, pageSize } })
}

/**
 * 发送消息
 * @param content 消息内容
 * @param type 消息类型 (text/audio)
 */
export const sendMessage = (
  content: string,
  type: 'text' | 'audio' = 'text'
): Promise<ChatMessage> => {
  return request.post(API_ENDPOINTS.SEND_MESSAGE, { content, type })
}

/**
 * 上传语音文件
 * @param file 语音文件
 */
export const uploadAudio = (file: File): Promise<{ url: string; duration: number }> => {
  const formData = new FormData()
  formData.append('audio', file)
  return request.post(API_ENDPOINTS.UPLOAD_AUDIO, formData)
}

/**
 * 获取用户消息历史
 * @param userId 用户ID
 * @param limit 限制数量
 */
export const getMessageHistory = (
  userId: string,
  limit = 10
): Promise<{
  success: boolean;
  data?: {
    messages: any[];
    total_count: number;
  };
  message?: string;
}> => {
  return request.get(`/api/v1/airelief/chat/message-history/${userId}`, {
    params: { limit }
  })
}

/**
 * 获取会话历史
 * @param userId 用户ID
 * @param page 页码
 * @param pageSize 每页数量
 */
export const getSessionHistory = (
  userId: string,
  page = 1,
  pageSize = 10
): Promise<{
  success: boolean;
  data?: {
    sessions: any[];
    total: number;
  };
  message?: string;
}> => {
  return request.get(`/api/v1/airelief/chat/session-history/${userId}`, {
    params: { page, page_size: pageSize }
  })
}

// 导出聊天API对象
export const chatApi = {
  getChatHistory,
  sendMessage,
  uploadAudio,
  getMessageHistory,
  getSessionHistory
}