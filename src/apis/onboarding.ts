import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'
import type { OnboardingMessage } from '@/types/api'

/**
 * 获取引导消息
 */
export const getOnboardingMessages = (): Promise<OnboardingMessage[]> => {
  return request.get(API_ENDPOINTS.ONBOARDING_MESSAGES)
}

/**
 * 领取新人福利
 */
export const claimNewUserBenefit = (): Promise<{ message: string; duration: number }> => {
  return request.post(API_ENDPOINTS.CLAIM_BENEFIT)
}