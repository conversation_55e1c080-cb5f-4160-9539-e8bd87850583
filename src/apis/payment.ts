import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'
import { isWechatBrowser, callWechatPay } from '@/utils/wechat'
import type { 
  PaymentPackage, 
  CreateOrderRequest, 
  CreateOrderResponse,
  OrderStatusResponse,
  UserOrdersResponse,
  CouponValidateResponse,
  CouponPaymentValidateResponse,
  CouponRedeemResponse
} from '@/types/api'

/**
 * 获取支付套餐列表（前台）
 */
export const getPaymentPackages = (): Promise<PaymentPackage[]> => {
  return request.get(API_ENDPOINTS.PAYMENT_PACKAGES).then(res => {
    // 响应拦截器已经解包了数据
    if (Array.isArray(res)) {
      return res
    }
    return res?.data || res
  })
}

/**
 * 获取所有支付套餐（后台管理）
 */
export const getAllPaymentPackages = (): Promise<PaymentPackage[]> => {
  return request.get(API_ENDPOINTS.PAYMENT_PACKAGES_ALL).then(res => {
    if (Array.isArray(res)) {
      return res
    }
    return res?.data || res
  })
}

/**
 * 验证优惠券
 * @param couponCode 优惠券代码
 */
export const validateCoupon = (couponCode: string): Promise<CouponValidateResponse> => {
  const requestData = { code: couponCode }
  return request.post(API_ENDPOINTS.VALIDATE_COUPON, requestData).then(res => res.data || res)
}

/**
 * 验证支付时使用的优惠券
 * @param couponCode 优惠券代码
 * @param userId 用户ID
 * @param packageId 套餐ID
 */
export const validateCouponForPayment = (
  couponCode: string, 
  userId: string, 
  packageId: number
): Promise<CouponPaymentValidateResponse> => {
  const params = new URLSearchParams({
    code: couponCode,
    user_id: userId,
    package_id: packageId.toString()
  })
  return request.post(`${API_ENDPOINTS.VALIDATE_COUPON_FOR_PAYMENT}?${params}`).then(res => res.data || res)
}

/**
 * 兑换优惠券
 * @param couponCode 优惠券代码
 * @param userId 用户ID
 */
export const redeemCoupon = (couponCode: string, userId: string): Promise<CouponRedeemResponse> => {
  const requestData = { 
    code: couponCode,
    user_id: userId 
  }
  return request.post(API_ENDPOINTS.USE_COUPON, requestData).then(res => res.data || res)
}

/**
 * 创建支付订单并调起微信支付
 * @param userId 用户ID
 * @param packageId 套餐ID
 * @param couponCode 优惠券代码（可选）
 */
export const createOrder = async (
  userId: string, 
  packageId: number, 
  couponCode?: string
): Promise<{ orderId: string; success: boolean }> => {
  // 检查是否在微信环境中
  if (!isWechatBrowser()) {
    throw new Error('请在微信客户端中打开')
  }

  // 准备请求数据
  const requestData: CreateOrderRequest = {
    user_id: userId,
    package_id: packageId,
    coupon_code: couponCode
  }

  try {
    // 创建订单
    const response = await request.post<CreateOrderResponse>(API_ENDPOINTS.CREATE_ORDER, requestData)
    const orderData = response.data || response

    if (!orderData.order_id || !orderData.pay_params) {
      throw new Error('订单创建失败，返回数据不完整')
    }

    // 调起微信支付
    try {
      await callWechatPay(orderData.pay_params)
      return {
        orderId: orderData.order_id,
        success: true
      }
    } catch (payError: any) {
      console.error('微信支付失败:', payError)
      throw new Error(`支付失败: ${payError?.message || '未知错误'}`)
    }
  } catch (error: any) {
    console.error('创建订单失败:', error)
    throw new Error(error?.message || '创建订单失败')
  }
}

/**
 * 创建订单但不调起支付（用于特殊场景）
 * @param userId 用户ID
 * @param packageId 套餐ID
 * @param couponCode 优惠券代码（可选）
 */
export const createOrderOnly = async (
  userId: string, 
  packageId: number, 
  couponCode?: string
): Promise<CreateOrderResponse> => {
  const requestData: CreateOrderRequest = {
    user_id: userId,
    package_id: packageId,
    coupon_code: couponCode
  }

  const response = await request.post<CreateOrderResponse>(API_ENDPOINTS.CREATE_ORDER, requestData)
  return response.data || response
}

/**
 * 查询订单支付状态
 * @param orderId 订单ID
 */
export const getOrderStatus = (orderId: string): Promise<OrderStatusResponse> => {
  return request.get(`${API_ENDPOINTS.ORDER_STATUS}/${orderId}/status`).then(res => res.data || res)
}

/**
 * 获取订单详情
 * @param orderId 订单ID
 */
export const getOrderDetail = (orderId: string): Promise<any> => {
  return request.get(`${API_ENDPOINTS.ORDER_STATUS}/${orderId}`).then(res => res.data || res)
}

/**
 * 获取用户订单列表
 * @param userId 用户ID
 * @param page 页码
 * @param size 每页数量
 */
export const getUserOrders = (
  userId: string, 
  page: number = 1, 
  size: number = 10
): Promise<UserOrdersResponse> => {
  const params = new URLSearchParams({
    user_id: userId,
    page: page.toString(),
    size: size.toString()
  })
  
  return request.get(`${API_ENDPOINTS.USER_ORDERS}?${params}`).then(res => res.data || res)
}

/**
 * 获取支付历史（兼容旧版本接口名称）
 */
export const getPaymentHistory = (userId: string, page: number = 1, size: number = 10) => {
  return getUserOrders(userId, page, size)
}

/**
 * 获取支付记录（兼容旧版本接口名称）
 */
export const getPaymentRecords = (userId: string, page: number = 1, size: number = 10) => {
  return getUserOrders(userId, page, size)
}

// =============== 后台管理接口 ===============

/**
 * 获取所有订单（后台管理）
 */
export const getAdminOrders = (params: {
  page?: number;
  size?: number;
  status?: string;
  payment_method?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
} = {}) => {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString())
    }
  })
  
  const queryString = searchParams.toString()
  const url = queryString ? `${API_ENDPOINTS.ADMIN_ORDERS_ALL}?${queryString}` : API_ENDPOINTS.ADMIN_ORDERS_ALL
  
  return request.get(url).then(res => res.data || res)
}

/**
 * 获取订单详情（后台管理）
 */
export const getAdminOrderDetail = (orderId: string) => {
  return request.get(`${API_ENDPOINTS.ADMIN_ORDER_DETAIL}/${orderId}`).then(res => res.data || res)
}

/**
 * 更新订单状态（后台管理）
 */
export const updateOrderStatus = (orderId: string, newStatus: string, reason?: string) => {
  const requestData = {
    new_status: newStatus,
    reason
  }
  return request.put(`${API_ENDPOINTS.ADMIN_UPDATE_ORDER_STATUS}/${orderId}/status`, requestData).then(res => res.data || res)
}

/**
 * 取消订单（用户操作）
 */
export const cancelOrder = (orderId: string, reason?: string) => {
  const requestData = reason ? { reason } : {}
  return request.post(`${API_ENDPOINTS.ORDER_STATUS}/${orderId}/cancel`, requestData).then(res => res.data || res)
}

/**
 * 获取订单统计（后台管理）
 */
export const getOrderStatistics = (startDate?: string, endDate?: string) => {
  const params = new URLSearchParams()
  if (startDate) params.append('start_date', startDate)
  if (endDate) params.append('end_date', endDate)

  const queryString = params.toString()
  const url = queryString ? `${API_ENDPOINTS.ADMIN_ORDER_STATISTICS}?${queryString}` : API_ENDPOINTS.ADMIN_ORDER_STATISTICS

  return request.get(url).then(res => res.data || res)
}
