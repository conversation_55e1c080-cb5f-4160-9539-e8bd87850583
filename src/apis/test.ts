import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'

// 健康检查响应类型
export interface HealthCheckResponse {
  status: string
  timestamp: string
  uptime: string
}

// 测试连接响应类型
export interface TestConnectionResponse {
  message: string
  timestamp: string
  status: string
  version: string
}

/**
 * 后端健康检查
 */
export const healthCheck = (): Promise<HealthCheckResponse> => {
  return request.get(API_ENDPOINTS.HEALTH_CHECK)
}

/**
 * 测试后端连接
 */
export const testConnection = (): Promise<TestConnectionResponse> => {
  return request.get(API_ENDPOINTS.TEST_CONNECTION)
} 