import request from '@/utils/request'
import { API_ENDPOINTS } from '@/constants/apiConstants'
import type {
  UserProfile,
  UserUpdateRequest,
  SendSmsCodeRequest,
  BindPhoneRequest,
  BindWeChatRequest,
  UploadAvatarResponse
} from '@/types/api'

/**
 * 获取用户信息
 */
export const getUserProfile = (): Promise<UserProfile> => {
  return request.get(API_ENDPOINTS.USER_PROFILE)
}

/**
 * 更新用户信息
 * @param data 用户信息
 */
export const updateUserProfile = (data: UserUpdateRequest): Promise<UserProfile> => {
  return request.put(API_ENDPOINTS.UPDATE_PROFILE, data)
}

/**
 * 上传头像
 * @param file 头像文件
 *
 * ========================================
 * 生产环境API规范 - 服务器端实现要求
 * ========================================
 *
 * 接口地址: POST /api/user/avatar
 * 请求格式: multipart/form-data
 * 请求参数: { avatar: File }
 * 响应格式: { avatarUrl: string }
 *
 * 服务器端要求:
 * - 验证JWT token (Authorization: Bearer {token})
 * - 验证文件类型 (image/jpeg, image/png, image/gif, image/webp)
 * - 验证文件大小 (最大5MB)
 * - 图片处理 (压缩、格式转换、尺寸调整)
 * - 存储到CDN或对象存储
 * - 返回可访问的图片URL
 *
 * 错误响应:
 * - 400: 文件格式不支持
 * - 401: 未授权
 * - 413: 文件大小超出限制
 * - 429: 请求过于频繁
 * - 500: 服务器内部错误
 */
export const uploadAvatar = (file: File): Promise<UploadAvatarResponse> => {
  const formData = new FormData()
  formData.append('avatar', file)
  return request.post(API_ENDPOINTS.UPLOAD_AVATAR, formData)
}

/**
 * 发送手机验证码
 * @param phone 手机号
 */
export const sendSmsCode = (phone: string): Promise<{ message: string }> => {
  return request.post(API_ENDPOINTS.SEND_SMS_CODE, { phone })
}

/**
 * 绑定手机号
 * @param phone 手机号
 * @param code 验证码
 */
export const bindPhoneNumber = (phone: string, code: string): Promise<{ message: string }> => {
  return request.post(API_ENDPOINTS.BIND_PHONE, { phone, code })
}

/**
 * 解绑手机号
 */
export const unbindPhoneNumber = (): Promise<{ message: string }> => {
  return request.post(API_ENDPOINTS.UNBIND_PHONE)
}

/**
 * 绑定微信
 * @param wechatCode 微信授权码
 */
export const bindWechat = (wechatCode: string): Promise<{ message: string }> => {
  return request.post(API_ENDPOINTS.BIND_WECHAT, { wechat_code: wechatCode })
}

/**
 * 解绑微信
 */
export const unbindWechat = (): Promise<{ message: string }> => {
  return request.post(API_ENDPOINTS.UNBIND_WECHAT)
}

/**
 * 注销账号
 */
export const deactivateAccount = (): Promise<{ message: string }> => {
  return request.post(API_ENDPOINTS.DEACTIVATE_ACCOUNT)
}