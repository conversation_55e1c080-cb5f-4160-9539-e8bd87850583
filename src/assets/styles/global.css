/* ======================================
   AI Relief 应用主题样式
   ====================================== */

/* 主题变量 */
:root {
    /* 主色调 */
    --van-primary-color: #6c5ce7;
    --van-success-color: #4CAF50;
    --van-danger-color: #ee0a24;
    --van-warning-color: #ff976a;

    /* 文本颜色 */
    --van-text-color: #333333;
    --van-text-color-2: #666666;
    --van-text-color-3: #999999;

    /* 背景颜色 */
    --van-background-color: #FAFAFACC;
    --van-background-color-light: #fafafa;

    /* 边框颜色 */
    --van-border-color: #ebedf0;

    /* 自定义颜色 */
    --ai-primary: #5F59FF;
    --ai-primary-light: #f0e6ff;
    --ai-primary-dark: #4a44cc;
    --ai-card-bg: #f0e6ff;

    /* 渐变背景颜色 */
    --ai-gradient-top-color: rgba(192, 209, 255, 0.6);
    --ai-gradient-bottom-color: #EEE1FF;

    /* 间距 */
    --ai-spacing-xs: 4px;
    --ai-spacing-sm: 8px;
    --ai-spacing-md: 12px;
    --ai-spacing-lg: 16px;
    --ai-spacing-xl: 20px;
    --ai-spacing-xxl: 24px;

    /* 圆角 */
    --ai-radius-sm: 8px;
    --ai-radius-md: 12px;
    --ai-radius-lg: 16px;
    --ai-radius-xl: 20px;
    --ai-radius-full: 9999px;

    /* 阴影 */
    --ai-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --ai-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --ai-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* 字体 */
    --ai-font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    --ai-font-family-fancy: 'Reenie Beanie', cursive, var(--ai-font-family);
}

/* 全局基础样式 */
body {
    font-family: var(--ai-font-family);
    color: var(--van-text-color);
    background-color: var(--van-background-color);
    line-height: 1.5;
    margin: 0;
    padding: 0;
}

* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* ======================================
   渐变背景样式
   ====================================== */

/* 渐变背景容器 */
.gradient-background {
    position: relative;
    overflow: hidden;
    background: linear-gradient(180deg, #F0F0FF 0%, #F6F8FF 100%);
}

/* 背景椭圆装饰 */
.gradient-background::before,
.gradient-background::after {
    content: '';
    position: absolute;
    z-index: 0;
    pointer-events: none;
}

/* 左上椭圆 - 按Figma设计稿 (414px基准) */
.gradient-background::before {
    width: 373px;
    height: 257px;
    left: -90px;
    top: -137px;
    background: var(--ai-gradient-top-color);
    filter: blur(62px);
    border-radius: 50%;
}

/* 右上椭圆 - 按Figma设计稿 (414px基准) */
.gradient-background::after {
    width: 237px;
    height: 237px;
    right: -90px;
    top: -105px;
    background: var(--ai-gradient-bottom-color);
    filter: blur(58px);
    border-radius: 50%;
}

/* 确保内容在背景之上 */
.gradient-background > * {
    position: relative;
    z-index: 1;
}

/* 渐变背景变体 - 居中 */
.gradient-background-centered::before,
.gradient-background-centered::after {
    top: 50%;
    transform: translateY(-50%);
}

/* 渐变背景变体 - 底部 */
.gradient-background-bottom::before {
    top: auto;
    bottom: -137px;
}

.gradient-background-bottom::after {
    top: auto;
    bottom: -105px;
}

/* ======================================
   Vant 组件样式覆盖
   ====================================== */

/* 按钮 */
.van-button--primary {
    background-color: var(--ai-primary);
    border-color: var(--ai-primary);
    border-radius: var(--ai-radius-sm);
}

.van-button--primary.van-button--plain {
    color: var(--ai-primary);
}

/* 导航栏 */
.van-nav-bar {
    --van-nav-bar-background: transparent;
    --van-nav-bar-arrow-size: 20px;
    --van-nav-bar-icon-color: #000000;
    --van-nav-bar-text-color: #000000;
}

.van-nav-bar__title {
    color: var(--van-text-color);
}

.van-nav-bar__text {
    color: var(--ai-primary);
}

/* 单元格 */
.van-cell {
    background-color: white;
}

/* 复选框 */
.van-checkbox__icon {
    font-size: 16px !important;
}

.van-checkbox__icon .van-icon {
    border-color: #0344E9 !important;
}

.van-checkbox__icon--checked .van-icon {
    background-color: #0344E9 !important;
    border-color: #0344E9 !important;
}

.van-checkbox__label {
    margin-left: 8px !important;
    color: #999999 !important;
}

/* 标签 */
.van-tag--primary {
    background-color: var(--ai-primary);
}

/* 输入框 */
.van-field__control:focus {
    color: var(--van-text-color);
}

/* Toast 组件样式修复 */
.van-toast {
    font-size: 14px !important;
}

.van-toast .van-icon {
    font-size: 36px !important;
    width: 36px !important;
    height: 36px !important;
}

.van-toast--success .van-icon,
.van-toast--fail .van-icon,
.van-toast--loading .van-icon {
    font-size: 36px !important;
    width: 36px !important;
    height: 36px !important;
}

/* 对话框 */
.van-dialog {
    --van-dialog-width: 280px;
    --van-dialog-font-size: 18px;
    --van-dialog-has-title-message-text-color: #90949E;
}

.van-dialog__header {
    padding: 26px 24px 8px;
}


.van-dialog__footer {
    padding: 0;
    border-top: 1px solid var(--van-border-color);
    display: flex;
}

.van-dialog__footer .van-button {
    border: none;
    border-radius: 0;
    font-size: 16px;
    font-weight: 400;
    height: 48px;
    flex: 1;
    background: transparent;
}

.van-dialog__footer .van-button:not(:last-child) {
    border-right: 1px solid var(--van-border-color);
}

.van-dialog__footer .van-button--default {
    color: var(--van-text-color-2);
}

.van-dialog__footer .van-button--primary {
    color: var(--ai-primary);
    font-weight: 500;
}

/* ======================================
   自定义卡片样式
   ====================================== */

.ai-card {
    background-color: var(--ai-card-bg);
    border-radius: var(--ai-radius-lg);
    padding: var(--ai-spacing-lg);
    margin-bottom: var(--ai-spacing-lg);
    position: relative;
    overflow: hidden;
}

.ai-card__title {
    font-size: 20px;
    font-weight: bold;
    color: var(--ai-primary);
    margin-bottom: var(--ai-spacing-sm);
}

.ai-card__content {
    font-size: 14px;
    color: var(--van-text-color-2);
    margin-bottom: var(--ai-spacing-md);
}

.ai-card__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(108, 92, 231, 0.3);
    padding-top: var(--ai-spacing-md);
    margin-top: var(--ai-spacing-md);
}

/* ======================================
   布局工具类
   ====================================== */

/* 容器 */
.page-container {
    position: relative;
    min-height: 100vh;
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--van-background-color);
}

.content-container {
    padding: var(--ai-spacing-lg);
    flex: 1;
    overflow-y: auto;
}

/* Flex 布局 */
.flex {
    display: flex;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.flex-1 {
    flex: 1;
}

.flex-wrap {
    flex-wrap: wrap;
}

/* 间距 */
.gap-sm { gap: var(--ai-spacing-sm); }
.gap-md { gap: var(--ai-spacing-md); }
.gap-lg { gap: var(--ai-spacing-lg); }

/* 边距 */
.m-0 { margin: 0; }
.mt-sm { margin-top: var(--ai-spacing-sm); }
.mt-md { margin-top: var(--ai-spacing-md); }
.mt-lg { margin-top: var(--ai-spacing-lg); }
.mt-xl { margin-top: var(--ai-spacing-xl); }
.mb-sm { margin-bottom: var(--ai-spacing-sm); }
.mb-md { margin-bottom: var(--ai-spacing-md); }
.mb-lg { margin-bottom: var(--ai-spacing-lg); }
.mb-xl { margin-bottom: var(--ai-spacing-xl); }
.ml-sm { margin-left: var(--ai-spacing-sm); }
.ml-md { margin-left: var(--ai-spacing-md); }
.mr-sm { margin-right: var(--ai-spacing-sm); }
.mr-md { margin-right: var(--ai-spacing-md); }

/* 内边距 */
.p-0 { padding: 0; }
.p-sm { padding: var(--ai-spacing-sm); }
.p-md { padding: var(--ai-spacing-md); }
.p-lg { padding: var(--ai-spacing-lg); }
.p-xl { padding: var(--ai-spacing-xl); }

/* 文本对齐 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 文本样式 */
.text-primary { color: var(--ai-primary); }
.text-secondary { color: var(--van-text-color-2); }
.text-tertiary { color: var(--van-text-color-3); }

.text-sm { font-size: 14px; }
.text-md { font-size: 16px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }

.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: normal; }

/* 圆角 */
.rounded-sm { border-radius: var(--ai-radius-sm); }
.rounded-md { border-radius: var(--ai-radius-md); }
.rounded-lg { border-radius: var(--ai-radius-lg); }
.rounded-full { border-radius: var(--ai-radius-full); }

/* 阴影 */
.shadow-sm { box-shadow: var(--ai-shadow-sm); }
.shadow-md { box-shadow: var(--ai-shadow-md); }
.shadow-lg { box-shadow: var(--ai-shadow-lg); }

/* 透明导航栏 */
.transparent-nav {
    background-color: transparent !important;
}

.transparent-nav .van-nav-bar__title,
.transparent-nav .van-icon,
.transparent-nav .van-nav-bar__text {
    color: var(--van-text-color);
}

/* ======================================
   响应式设计
   ====================================== */

/* 响应式适配 */
@media (max-width: 767px) {
    .page-container {
        max-width: 100%;
    }
}

/* 桌面端适配：保持原始样式 */
@media (min-width: 768px) {
    .page-container {
        max-width: 414px;
        margin: 0 auto;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    /* 桌面端背景渐变保持原始尺寸 */
    .gradient-background::before {
        width: 373px !important;
        height: 257px !important;
        left: -90px !important;
        top: -137px !important;
        filter: blur(62px) !important;
    }

    .gradient-background::after {
        width: 237px !important;
        height: 237px !important;
        right: -90px !important;
        top: -105px !important;
        filter: blur(58px) !important;
    }
}

/* ======================================
   动画
   ====================================== */

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
    transition: transform 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
    transform: translateY(20px);
    opacity: 0;
}
