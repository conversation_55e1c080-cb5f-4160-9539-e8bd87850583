/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const showConfirmDialog: typeof import('vant/es')['showConfirmDialog']
  const showDialog: typeof import('vant/es')['showDialog']
  const showFailToast: typeof import('vant/es')['showFailToast']
  const showSuccessToast: typeof import('vant/es')['showSuccessToast']
  const showToast: typeof import('vant/es')['showToast']
  const useUserStore: typeof import('./stores/user')['useUserStore']
  const useWebSocketStore: typeof import('./stores/websocket')['useWebSocketStore']
}
