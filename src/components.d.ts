/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BasePopup: typeof import('./components/common/BasePopup.vue')['default']
    ChatHeader: typeof import('./components/business/Chat/ChatHeader.vue')['default']
    ChatInput: typeof import('./components/business/Chat/ChatInput.vue')['default']
    ChatInputWx: typeof import('./components/business/Chat/ChatInput-wx.vue')['default']
    EmptyState: typeof import('./components/common/EmptyState.vue')['default']
    LoadingIndicator: typeof import('./components/common/LoadingIndicator.vue')['default']
    MessageItem: typeof import('./components/business/Chat/MessageItem.vue')['default']
    NewUserBenefitPopup: typeof import('./components/business/Onboarding/NewUserBenefitPopup.vue')['default']
    PaymentIdentityCard: typeof import('./components/business/Profile/PaymentIdentityCard.vue')['default']
    ProfileSidebar: typeof import('./components/business/Profile/ProfileSidebar.vue')['default']
    RedeemCodeInput: typeof import('./components/business/Profile/RedeemCodeInput.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SidebarIdentityCard: typeof import('./components/business/Profile/SidebarIdentityCard.vue')['default']
    UserInfoCollectorPopup: typeof import('./components/business/Onboarding/UserInfoCollectorPopup.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCountDown: typeof import('vant/es')['CountDown']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDialog: typeof import('vant/es')['Dialog']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VerificationCodeInput: typeof import('./components/business/Profile/VerificationCodeInput.vue')['default']
  }
}
