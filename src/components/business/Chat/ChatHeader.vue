<script setup lang="ts">
import VanImage from 'vant/lib/image';
import VanBadge from 'vant/lib/badge';
import 'vant/lib/image/style';
import 'vant/lib/badge/style';
import aiAvatarImg from '@/assets/images/ai-avatar.png'

interface Props {
  title: string
  subtitle?: string
  isTyping?: boolean
  isOnline?: boolean
}

defineProps<Props>()
defineEmits<{
  'menu-click': []
}>()
</script>

<template>
  <div class="chat-header">
    <!-- 菜单图标 -->
    <div class="chat-header__menu" @click="$emit('menu-click')">
      <img src="@/assets/icon/chat-menu.svg" alt="菜单" class="menu-icon" />
    </div>

    <!-- 用户信息区域 -->
    <div class="chat-header__user-info">
      <!-- 头像容器 -->
      <div class="avatar-container">
        <van-badge :offset="[-4, 30]" :dot="isOnline" color="#00C853">
          <van-image
            round
            width="36px"
            height="36px"
            :src="aiAvatarImg"
            alt="头像"
          />
        </van-badge>
      </div>

      <!-- 文字信息 -->
      <div class="user-text">
        <div class="user-name">
          {{ isTyping ? '对方正在输入中' : title }}
        </div>
        <div class="user-subtitle">
          {{ subtitle }}
        </div>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="chat-header__divider"></div>
  </div>
</template>

<style scoped>
.chat-header {
  position: relative;
  width: 100%;
  height: 64px;
  background-color: transparent;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.chat-header__menu {
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon {
  width: 20px;
  height: 20px;
}

.chat-header__user-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 14px;
  flex: 1;
}

.avatar-container {
  position: relative;
  width: 36px;
  height: 36px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}


.user-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0;
}

.user-name {
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #222222;
  margin: 0;
}

.user-subtitle {
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 10px;
  line-height: 17px;
  color: #63636E;
  margin: 0;
}

.chat-header__divider {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0.5px;
  background: #F6F6F6;
}
</style>
