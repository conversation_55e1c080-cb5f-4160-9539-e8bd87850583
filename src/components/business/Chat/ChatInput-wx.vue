@ -1,5 +1,5 @@
<script setup lang="ts">
import { ref, watch, onUnmounted, computed } from 'vue'

// Props
interface Props {
@ -20,9 +20,6 @@ const props = withDefaults(defineProps<Props>(), {
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', message: string): void
  (e: 'send-audio', duration: number): void  // 发送语音消息
  (e: 'voice-start'): void  // 语音录音开始
  (e: 'voice-end'): void    // 语音录音结束
  (e: 'start-recording'): void  // 开始录音（传递给父组件处理）
  (e: 'stop-recording'): void   // 停止录音（传递给父组件处理）
  (e: 'cancel-recording'): void // 取消录音（传递给父组件处理）
@ -37,13 +34,8 @@ const inputValue = computed({
})
const inputMode = ref<'text' | 'voice'>('text') // 输入模式：文本或语音
const isRecording = ref(false) // 是否正在录音
const recordingTime = ref(0)

// 录音相关（目前仅用于样式演示）
let recordTimer: number | null = null
// TODO: 实际录音功能扩展时需要的变量
// let audioRecorder: MediaRecorder | null = null
// let audioChunks: Blob[] = []

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
@ -65,6 +57,7 @@ const toggleInputMode = (): void => {
const handleSend = () => {
  if (inputValue.value.trim()) {
    emit('send', inputValue.value)
  }
}

@ -79,57 +72,71 @@ const handleKeyDown = (event: KeyboardEvent) => {
}

// 开始录音
const startRecording = (): void => {
  if (inputMode.value !== 'voice' || props.disabled) return

  isRecording.value = true
  recordingTime.value = 0
  emit('voice-start')
  emit('start-recording')

  // 录音计时
  recordTimer = window.setInterval(() => {
    recordingTime.value++
    // 最长录音时间限制 (60秒)
    if (recordingTime.value >= 60) {
      stopRecording()
    }
  }, 1000)

  console.log('开始录音')
}

// 停止录音
const stopRecording = (): void => {
  if (inputMode.value !== 'voice' || !isRecording.value) return

  if (recordTimer) {
    clearInterval(recordTimer)
    recordTimer = null
  }

  const duration = recordingTime.value
  isRecording.value = false

  // 只有录音时长大于1秒才发送语音消息
  if (duration >= 1) {
    emit('send-audio', duration)
    emit('stop-recording')
  } else {
    console.log('录音时间太短，至少需要1秒')
    emit('cancel-recording')
  }

  emit('voice-end')
  console.log(`录音结束，时长: ${duration}s`)
}

// 组件卸载时清理
onUnmounted(() => {
  if (recordTimer) {
    clearInterval(recordTimer)
    recordTimer = null
  }
})
</script>

@ -187,17 +194,15 @@ onUnmounted(() => {
          <div
            class="voice-input-area"
            :class="{ 'recording': isRecording }"
            @touchstart="startRecording"
            @touchend="stopRecording"
            @mousedown="startRecording"
            @mouseup="stopRecording"
            @mouseleave="stopRecording"
          >
            <span v-if="!isRecording" class="voice-hint">按住 说话</span>
            <span v-else class="recording-hint">
              <span class="recording-text">录音中... {{ recordingTime }}s</span>
              <span class="release-text">松开发送</span>
            </span>
          </div>
        </template>
      </div>
@ -298,52 +303,37 @@ onUnmounted(() => {
}

.voice-input-area {
  flex: 1;
  height: 38px;
  background-color: #5F59FF;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  padding: 0 20px;
  position: relative;
}

.voice-input-area.recording {
  background-color: #979797;
  justify-content: space-between;
  padding: 0 24px;
  transform: scale(1.02);
}

.voice-hint {
  color: #F0EEF4;
  font-size: 16px;
  font-weight: 400;
  font-family: 'PingFang SC', sans-serif;
}

.recording-hint {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  color: #F0EEF4;
  font-size: 16px;
  font-weight: 400;
  font-family: 'PingFang SC', sans-serif;
}

.recording-text {
  text-align: left;
  flex-shrink: 0;
}

.release-text {
  text-align: right;
  flex-shrink: 0;
}
</style>