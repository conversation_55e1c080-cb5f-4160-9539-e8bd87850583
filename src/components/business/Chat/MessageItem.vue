<script setup lang="ts">
import { computed, ref } from 'vue'
import { showToast } from 'vant'
import type { ChatMessage } from '../../../types/api'
import { useUserStore } from '@/stores/user'
import { isWechatBrowser } from '@/utils/wechat'
import aiAvatarImg from '@/assets/images/ai-avatar.png'
import userAvatarImg from '@/assets/images/user-avtar.png'

interface Props {
  message: ChatMessage
}

const props = defineProps<Props>()
const userStore = useUserStore()

const isMessageFromAI = computed(() => props.message.role === 'assistant')
const isAudio = computed(() => props.message.type === 'audio')
const isDivider = computed(() => props.message.type === 'divider')
const isPlaying = ref(false)

// 处理消息内容中的换行符
const formattedContent = computed(() => {
  if (!props.message.content) return ''

  // 将 \n 转换为实际的换行符，并处理其他可能的换行符格式
  return props.message.content
    .replace(/\\n/g, '\n')  // 处理转义的换行符
    .replace(/\r\n/g, '\n') // 处理Windows换行符
    .replace(/\r/g, '\n')   // 处理Mac换行符
    .trim()
})

// 获取用户头像，优先使用store中的头像，否则使用默认头像
const userAvatar = computed(() => {
  return userStore.userInfo?.avatar || userAvatarImg
})

// 播放语音消息
const playAudio = () => {
  if (!isAudio.value) return

  // 调试：打印语音消息对象
  console.log('🎵 [MessageItem] 点击播放语音，消息对象:', props.message)
  console.log('🎵 [MessageItem] audioUrl:', props.message.audioUrl)
  console.log('🎵 [MessageItem] localId:', props.message.localId)
  console.log('🎵 [MessageItem] transcription:', props.message.transcription)
  console.log('🎵 [MessageItem] 是否微信环境:', isWechatBrowser())

  // 如果是微信环境且有localId，使用微信播放
  if (isWechatBrowser() && props.message.localId && window.wx) {
    try {
      if (isPlaying.value) {
        // 如果正在播放，则停止播放
        window.wx.stopVoice({
          localId: props.message.localId
        })
        isPlaying.value = false
        showToast('停止播放')
      } else {
        // 开始播放
        window.wx.playVoice({
          localId: props.message.localId
        })
        isPlaying.value = true
        showToast('开始播放语音')

        // 监听播放结束
        window.wx.onVoicePlayEnd({
          complete: (res: any) => {
            if (res.localId === props.message.localId) {
              isPlaying.value = false
              console.log('语音播放结束')
            }
          }
        })
      }
    } catch (error) {
      console.error('播放语音失败:', error)
      showToast('播放失败')
      isPlaying.value = false
    }
  } else if (props.message.audioUrl) {
    // 如果有audioUrl，使用HTML5 Audio播放
    try {
      console.log('🎵 [MessageItem] 使用HTML5 Audio播放:', props.message.audioUrl)
      isPlaying.value = true
      const audio = new Audio(props.message.audioUrl)

      audio.onloadstart = () => {
        console.log('🎵 [MessageItem] 音频开始加载')
      }

      audio.oncanplay = () => {
        console.log('🎵 [MessageItem] 音频可以播放')
      }

      audio.onplay = () => {
        console.log('🎵 [MessageItem] 音频开始播放')
      }

      audio.onended = () => {
        console.log('🎵 [MessageItem] 音频播放完成')
        isPlaying.value = false
      }

      audio.onerror = (error) => {
        console.error('🎵 [MessageItem] 音频播放错误:', error)
        showToast('播放失败')
        isPlaying.value = false
      }

      audio.play().then(() => {
        console.log('🎵 [MessageItem] 音频播放开始成功')
        showToast('开始播放语音')
      }).catch((error) => {
        console.error('🎵 [MessageItem] 音频播放启动失败:', error)
        showToast('播放失败')
        isPlaying.value = false
      })
    } catch (error) {
      console.error('播放语音失败:', error)
      showToast('播放失败')
      isPlaying.value = false
    }
  } else {
    // 显示转录文本或提示
    console.log('🎵 [MessageItem] 没有可播放的音频文件')
    if (props.message.transcription) {
      console.log('🎵 [MessageItem] 显示转录文本:', props.message.transcription)
      showToast(`语音内容: ${props.message.transcription}`)
    } else {
      console.log('🎵 [MessageItem] 语音文件不可用')
      showToast('语音文件不可用')
    }
  }
}
</script>

<template>
  <!-- 分割线样式 -->
  <div v-if="isDivider" class="message-divider"></div>

  <!-- 普通消息 -->
  <div v-else class="message-item" :class="{ 'message-item--ai': isMessageFromAI, 'message-item--user': !isMessageFromAI }">
    <div class="message-item__avatar">
      <van-image
        :src="isMessageFromAI ? aiAvatarImg : userAvatar"
        round
        width="40"
        height="40"
        fit="cover"
      />
    </div>
    <div class="message-item__content">
      <template v-if="isAudio">
        <div class="message-item__audio-wrapper">
          <div class="message-item__audio" @click="playAudio" :class="{ 'message-item__audio--playing': isPlaying }">
            <div class="message-item__audio-duration">{{ message.audioDuration }}″</div>
            <img
              src="@/assets/icon/chat-voiceMsg.svg"
              alt="语音消息"
              class="message-item__audio-icon"
              :class="{
                'message-item__audio-icon--right': !isMessageFromAI,
                'message-item__audio-icon--playing': isPlaying
              }"
            />
            <!-- 播放状态指示 -->
            <div v-if="isPlaying" class="message-item__audio-playing-indicator">
              <span class="playing-dot"></span>
              <span class="playing-dot"></span>
              <span class="playing-dot"></span>
            </div>
          </div>
          <!-- 显示语音转录文本 -->
          <div v-if="message.transcription" class="message-item__transcription">
            "{{ message.transcription }}"
          </div>
        </div>
      </template>
      <template v-else>
        <div class="message-item__text">{{ formattedContent }}</div>
      </template>
    </div>
  </div>
</template>

<style>
/* 分割线样式 */
.message-divider {
  position: relative;
  width: 90%;
  height: 0px;
  margin: 20px auto;
  border: 0.5px solid #e1e1e1;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  padding: 0 19px;
}

.message-item--ai {
  flex-direction: row;
}

.message-item--ai .message-item__content {
  background-color: #FFFFFF;
  border-radius: 0px 8px 8px 8px;
  margin-left: 8px;
  color: #222222;
}

.message-item--user {
  flex-direction: row-reverse;
}

.message-item--user .message-item__content {
  background-color: #5F59FF;
  border-radius: 12px 0px 12px 12px;
  margin-right: 8px;
  color: #FFFFFF;
}

.message-item__avatar {
  flex-shrink: 0;
}

/* 气泡长度，间距 */
.message-item__content {
  padding: 8px 12px;
  max-width: 75%;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-weight: 300;
  display: flex;
  align-items: center;
  min-height: 24px;
}

.message-item__text {
  word-break: break-word;
  line-height: 1.5;
  white-space: pre-line;
}

.message-item__audio-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.message-item__audio {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 72px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.message-item__audio:hover {
  opacity: 0.8;
}

.message-item__audio:active {
  opacity: 0.6;
}

.message-item__audio-icon {
  width: 18px;
  height: 20px;
}

.message-item__audio-icon--right {
  transform: scaleX(-1);
}

.message-item__audio-duration {
  font-size: 16px;
  line-height: 1.5;
}

.message-item--user .message-item__audio-duration {
  color: #FFFFFF;
}

.message-item--ai .message-item__audio-duration {
  color: #666;
}

.message-item__transcription {
  font-size: 12px;
  line-height: 1.4;
  opacity: 0.8;
  font-style: italic;
  margin-top: 2px;
  word-break: break-word;
}

.message-item--user .message-item__transcription {
  color: rgba(255, 255, 255, 0.8);
}

.message-item--ai .message-item__transcription {
  color: rgba(34, 34, 34, 0.6);
}

/* 播放状态样式 */
.message-item__audio--playing {
  background-color: rgba(95, 89, 255, 0.1);
  border-radius: 4px;
}

.message-item__audio-icon--playing {
  opacity: 0.8;
}

.message-item__audio-playing-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: 4px;
}

.playing-dot {
  width: 3px;
  height: 3px;
  background-color: #5F59FF;
  border-radius: 50%;
  animation: playing-pulse 1.4s infinite ease-in-out;
}

.playing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.playing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.playing-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes playing-pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style>