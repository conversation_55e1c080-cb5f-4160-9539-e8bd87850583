<script setup lang="ts">
import { But<PERSON> as <PERSON><PERSON><PERSON><PERSON> } from 'vant'
import { ref, watch, onMounted, onUnmounted } from 'vue'

interface Props {
  show: boolean
  countdownSeconds?: number
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'claim'): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  countdownSeconds: 5
})

const emit = defineEmits<Emits>()

// ===== 响应式数据 =====
const remainingTime = ref(props.countdownSeconds)
const timer = ref<number | null>(null)

// ===== 事件处理方法 =====
// 处理遮罩层点击
const handleOverlayClick = () => {
  emit('update:show', false)
  emit('close')
}

// 处理关闭按钮点击
const handleClose = () => {
  emit('update:show', false)
  emit('close')
}

// 处理领取按钮点击
const handleClaim = () => {
  emit('claim')
  emit('update:show', false)
}

// ===== 倒计时逻辑 =====
const startCountdown = () => {
  if (timer.value) {
    clearInterval(timer.value)
  }

  remainingTime.value = props.countdownSeconds

  timer.value = window.setInterval(() => {
    remainingTime.value--

    if (remainingTime.value <= 0) {
      clearInterval(timer.value as number)
      timer.value = null
    }
  }, 1000)
}

const stopCountdown = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

// ===== 生命周期 =====
onMounted(() => {
  if (props.show) {
    startCountdown()
  }
})

onUnmounted(() => {
  stopCountdown()
})

// 监听show变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    startCountdown()
  } else {
    stopCountdown()
  }
})
</script>

<template>
  <!-- 弹窗遮罩层 -->
  <div class="popup-overlay" v-if="show" @click="handleOverlayClick">
    <!-- 弹窗内容容器 -->
    <div class="popup-content" @click.stop>
      <!-- 原始 frame 结构 -->
      <div class="frame">
        <div class="group">
          <div class="overlap">
            <!-- 主背景 -->
            <div class="rectangle" />

            <!-- 礼品卡片区域 -->
            <div class="overlap-wrapper">
              <div class="overlap-group">
                <!-- 卡片阴影 -->
                <div class="div" />
                <!-- 旋转的礼品卡片 -->
                <div class="overlap-group-wrapper">
                  <div class="overlap-group-2">
                    <div class="rectangle-2" />
                    <img
                      class="vector"
                      alt="Vector"
                      src="@/assets/icon/gift-icon.svg"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 文本内容区域 -->
            <div class="group-wrapper">
              <div class="div-wrapper">
                <div class="overlap-group-3">
                  <div class="group-2">
                    <div class="text-wrapper">送你新人福利</div>
                    <div class="text-wrapper-2">情绪陪伴套餐！</div>
                    <div class="text-wrapper-3">祝君心理舒坦 笑口常开～</div>
                    <!-- 天数显示 -->
                    <div class="group-3">
                      <div class="group-4">
                        <div class="text-wrapper-4">天</div>
                        <div class="text-wrapper-5">3</div>
                      </div>
                    </div>
                  </div>
                  <!-- 装饰星星 -->
                  <svg class="rectangle-3" width="24" height="22" viewBox="0 0 24 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.6706 0L12.5699 4.3012C13.1917 7.2753 15.605 9.68953 18.8359 10.5694V10.5694V10.5694C15.6417 11.5431 13.3267 14.0441 12.8515 17.0347L12.1665 21.3463L11.2672 17.045C10.6454 14.0709 8.23201 11.6567 5.00118 10.7769V10.7769V10.7769C8.19537 9.80318 10.5104 7.30212 10.9855 4.31157L11.6706 0Z" fill="white"/>
                  </svg>
                </div>
              </div>
            </div>

            <!-- 领取按钮 -->
            <div class="frame-wrapper">
              <van-button
                type="primary"
                class="div-wrapper-2"
                @click="handleClaim"
              >
                <span class="text-wrapper-6">
                  {{ remainingTime > 0 ? `领取心意 (${remainingTime})` : '领取心意' }}
                </span>
              </van-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 关闭按钮 -->
      <img
        class="system-error-circle-line"
        src="@/assets/icon/popup-close-bottom.svg"
        alt="关闭"
        @click="handleClose"
      />
    </div>
  </div>
</template>

<style scoped>
/* ===== 遮罩层样式 ===== */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* ===== 弹窗内容容器 ===== */
.popup-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  transform-origin: center;
}

/* ===== 原始样式（保持不变） ===== */
.frame {
  background-color: transparent;
  height: 366px;
  position: relative;
  width: 380px;
}

.group {
  height: 358px;
  left: 27px;
  position: absolute;
  top: 6px;
  width: 356px;
}

.overlap {
  height: 358px;
  position: relative;
}

.rectangle {
  background-color: #5e59ff;
  border-radius: 8px;
  height: 358px;
  left: 0;
  position: absolute;
  top: 0;
  width: 326px;
}

.overlap-wrapper {
  height: 198px;
  left: 78px;
  position: absolute;
  top: 47px;
  width: 278px;
}

.overlap-group {
  height: 198px;
  position: relative;
}

.div {
  background-color: #5e59ff;
  box-shadow: 4px 0px 24px 4px #4f49e1;
  height: 183px;
  left: 0;
  position: absolute;
  top: 11px;
  width: 170px;
}

.overlap-group-wrapper {
  height: 155px;
  left: 75px;
  position: absolute;
  top: 21px;
  transform: rotate(15.00deg);
  width: 186px;
}

.overlap-group-2 {
  height: 155px;
  position: relative;
  width: 189px;
}

.rectangle-2 {
  background-color: #8783ff;
  border-radius: 12px;
  height: 155px;
  left: 0;
  position: absolute;
  top: 0;
  width: 186px;
}

.vector {
  height: 102px;
  left: 71px;
  position: absolute;
  top: 22px;
  transform: rotate(-15.00deg);
  width: 106px;
}

.group-wrapper {
  background-color: #5e59ff;
  height: 270px;
  left: 16px;
  position: absolute;
  top: 10px;
  width: 235px;
}

.div-wrapper {
  height: 222px;
  position: relative;
  top: 18px;
  width: 218px;
}

.overlap-group-3 {
  height: 222px;
  position: relative;
  width: 224px;
}

.group-2 {
  height: 208px;
  left: 0;
  position: absolute;
  top: 14px;
  width: 224px;
}

.text-wrapper {
  color: #ffffff;
  font-family: "PingFang SC-Medium", Helvetica;
  font-size: 28px;
  font-weight: 500;
  left: 0;
  letter-spacing: 0;
  line-height: 28px;
  position: absolute;
  text-align: center;
  top: 0;
  white-space: nowrap;
}

.text-wrapper-2 {
  color: #ffffff;
  font-family: "PingFang SC-Medium", Helvetica;
  font-size: 18px;
  font-weight: 500;
  left: 14px;
  letter-spacing: 0;
  line-height: 18px;
  position: absolute;
  text-align: center;
  top: 155px;
  white-space: nowrap;
}

.text-wrapper-3 {
  color: #ffffff;
  font-family: "PingFang SC-Medium", Helvetica;
  font-size: 18px;
  font-weight: 500;
  left: 14px;
  letter-spacing: 0;
  line-height: 18px;
  position: absolute;
  text-align: center;
  top: 190px;
  white-space: nowrap;
}

.group-3 {
  height: 92px;
  left: 20px;
  position: absolute;
  top: 49px;
  width: 82px;
}

.group-4 {
  height: 92px;
  position: relative;
  width: 86px;
}

.text-wrapper-4 {
  color: #ffffff;
  font-family: "PingFang SC-Medium", Helvetica;
  font-size: 18px;
  font-weight: 500;
  left: 64px;
  letter-spacing: 0;
  line-height: 18px;
  position: absolute;
  text-align: center;
  top: 61px;
  white-space: nowrap;
}

.text-wrapper-5 {
  color: #ffffff;
  font-family: "Arvo", Helvetica;
  font-size: 92px;
  font-weight: 700;
  left: 0;
  letter-spacing: -10.00px;
  line-height: 92px;
  position: absolute;
  text-align: center;
  top: 0;
  white-space: nowrap;
}

.rectangle-3 {
  height: 21px;
  left: 177px;
  position: absolute;
  top: 0;
  width: 24px;
}

.frame-wrapper {
  height: 48px;
  left: 24px;
  position: absolute;
  top: 286px;
  width: 278px;
}

.div-wrapper-2 {
  align-items: center;
  background-color: #ffffff !important;
  border-radius: 6px !important;
  display: flex;
  gap: 10px;
  justify-content: center;
  padding: 12px !important;
  position: relative;
  width: 278px !important;
  height: 48px !important;
  border: none !important;
}

.text-wrapper-6 {
  color: #5e59ff;
  font-family: "PingFang SC-Medium", Helvetica;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 24px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

/* ===== 关闭按钮样式 ===== */
.system-error-circle-line {
  width: 32px;
  height: 32px;
  cursor: pointer;
}

/* ===== 响应式适配 ===== */
@media (max-width: 480px) {
  .frame {
    transform: scale(0.85);
  }

  .popup-content {
    gap: 0px;
  }
}
</style>