<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant' // 假设 'vant' 提供 showToast，或者从 'vant/es/toast' 导入

// ==================== 类型定义 ====================
interface Props {
  show: boolean
}

interface UserInfo {
  gender: number // 性别：0=未知, 1=女性, 2=男性
  birthday: string
}

// ==================== Props & Emits ====================
defineProps<Props>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  'save': [data: UserInfo]
  'skip': []
}>()

// ==================== 响应式数据 ====================
// 性别选择，默认选择女生 (1=女性)
const gender = ref(1)

// 出生日期配置
const minDate = new Date(1950, 0, 1)
const maxDate = new Date() // 当前日期
// Vant 日期选择器的 v-model 需要字符串数组格式：['YYYY', 'MM', 'DD']
const currentDate = ref(['2000', '06', '15']) // 默认日期：2000年6月15日

// ==================== 业务逻辑 ====================
/**
 * 保存用户信息
 * 验证必填项并格式化数据后提交
 */
const saveUserInfo = () => {
  // 验证性别选择
  if (!gender.value) {
    showToast('请选择性别')
    return
  }

  // 格式化生日数据
  const birthday = formatBirthday()

  // 提交数据并关闭弹窗
  emit('save', {
    gender: gender.value,
    birthday
  })
  emit('update:show', false)
}

/**
 * 格式化生日数据
 * 将日期选择器的数组格式转换为 YYYY-MM-DD 字符串
 */
const formatBirthday = (): string => {
  const defaultBirthday = '2000-06-06' // 备用生日

  if (!currentDate.value || currentDate.value.length !== 3) {
    return defaultBirthday
  }

  const [yearStr, monthStr, dayStr] = currentDate.value
  const year = parseInt(yearStr, 10)
  const month = parseInt(monthStr, 10)
  const day = parseInt(dayStr, 10)

  // 确保月份和日期都是两位数
  return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
}

/**
 * 跳过用户信息收集
 */
const handleSkip = () => {
  emit('skip')
  emit('update:show', false)
}
</script>

<template>
  <!-- 用户信息收集弹窗 -->
  <van-popup
    :show="show"
    position="bottom"
    round
    :style="{ height: '70%' }"
    :close-on-click-overlay="false"
    @update:show="$emit('update:show', $event)"
  >
    <div class="user-info-popup">
      <!-- 弹窗头部 -->
      <header class="popup-header">
        <div class="header-content">
          <h2 class="greeting">HI, </h2>
          <h3 class="welcome">欢迎来到愈言空间！</h3>
          <p class="description">选择性别和生日，我们将为你提供个性化服务</p>
        </div>
        <button class="skip-button" type="button" @click="handleSkip">
          跳过
        </button>
      </header>

      <!-- 性别选择区域 -->
      <section class="gender-section">
        <van-radio-group
          v-model="gender"
          direction="horizontal"
          class="gender-options"
        >
          <!-- 女生选项 -->
          <van-radio :name="1" icon-size="0px" class="gender-option">
            <template #icon></template> <!-- 隐藏默认图标 -->
            <div class="gender-item" :class="{ 'is-selected': gender === 1 }">
              <div class="gender-icon">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.9333 22H17.3333V19.8667C22.1333 19.2 26 15.0667 26 10C26 4.53333 21.4667 0 16 0C10.5333 0 6 4.53333 6 10C6 15.0667 9.73333 19.2 14.6667 19.8667V22H9.06667C8.26667 22 7.73333 22.5333 7.73333 23.3333C7.73333 24.1333 8.26667 24.6667 9.06667 24.6667H14.6667V30.6667C14.6667 31.4667 15.2 32 16 32C16.8 32 17.3333 31.4667 17.3333 30.6667V24.6667H22.9333C23.7333 24.6667 24.2667 24.1333 24.2667 23.3333C24.2667 22.5333 23.6 22 22.9333 22ZM8.66667 10C8.66667 6 12 2.66667 16 2.66667C20 2.66667 23.3333 6 23.3333 10C23.3333 14 20 17.2 16 17.2C12 17.2 8.66667 14 8.66667 10Z"/>
                </svg>
              </div>
              <span class="gender-label">女生</span>
            </div>
          </van-radio>

          <!-- 男生选项 -->
          <van-radio :name="2" icon-size="0px" class="gender-option">
            <template #icon></template>
            <div class="gender-item" :class="{ 'is-selected': gender === 2 }">
              <div class="gender-icon">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M30 4.05932C30.0031 3.74995 29.8906 3.44682 29.6906 3.21557C29.475 2.96557 29.1656 2.80932 28.8344 2.7812H28.8219L21.4562 2.24682C20.7562 2.19682 20.1469 2.72182 20.0969 3.42182C20.0469 4.12182 20.5719 4.7312 21.2719 4.7812L25.8563 5.11245L20.4094 10.5406C18.4531 8.77807 15.8656 7.70307 13.025 7.70307C6.9375 7.70307 2 12.6406 2 18.7312C2 24.8218 6.9375 29.7562 13.025 29.7562C19.1125 29.7562 24.05 24.8187 24.05 18.7312C24.05 16.4 23.325 14.2343 22.0906 12.4531L27.2219 7.34057L26.9312 11.4656C26.8812 12.1656 27.4094 12.7718 28.1094 12.8218C28.1406 12.8249 28.1688 12.8249 28.2 12.8249C28.8625 12.8249 29.4188 12.3124 29.4656 11.6437L29.9906 4.13745C30 4.11245 30 4.08432 30 4.05932C30 4.06557 30 4.04995 30 4.05932ZM20.8438 22.0312C20.4156 23.0406 19.8031 23.95 19.025 24.7281C18.2437 25.5093 17.3375 26.1187 16.3281 26.5468C15.2844 26.9875 14.1719 27.2125 13.025 27.2125C11.8781 27.2125 10.7688 26.9875 9.725 26.5468C8.71563 26.1187 7.80625 25.5062 7.02812 24.7281C6.24687 23.9468 5.63437 23.0406 5.20937 22.0312C4.76875 20.9875 4.54375 19.875 4.54375 18.7281C4.54375 17.5812 4.76875 16.4718 5.20937 15.4249C5.6375 14.4156 6.25 13.5062 7.02812 12.7281C7.80937 11.9468 8.71563 11.3374 9.725 10.9093C10.7688 10.4687 11.8812 10.2437 13.025 10.2437C14.1719 10.2437 15.2812 10.4687 16.3281 10.9093C17.3375 11.3374 18.2469 11.9499 19.025 12.7281C19.8062 13.5093 20.4156 14.4156 20.8438 15.4249C21.2844 16.4687 21.5094 17.5812 21.5094 18.7281C21.5094 19.875 21.2875 20.9875 20.8438 22.0312Z"/>
                </svg>
              </div>
              <span class="gender-label">男生</span>
            </div>
          </van-radio>

          <!-- 其他选项 -->
          <van-radio :name="0" icon-size="0px" class="gender-option">
            <template #icon></template>
            <div class="gender-item" :class="{ 'is-selected': gender === 0 }">
              <div class="gender-icon">
                <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M26.8389 21.201L21.2949 18L26.8389 14.799C27.5574 14.385 27.8019 13.4685 27.3879 12.75C26.9739 12.033 26.0574 11.787 25.3389 12.201L19.7949 15.402V9C19.7949 8.1705 19.1229 7.5 18.2949 7.5C17.4654 7.5 16.7949 8.1705 16.7949 9V15.402L11.2494 12.201C10.5324 11.787 9.61586 12.033 9.20036 12.75C8.78786 13.4685 9.03236 14.385 9.74936 14.799L15.2949 18L9.74936 21.201C9.03236 21.615 8.78786 22.533 9.20036 23.25C9.61586 23.9685 10.5324 24.2145 11.2494 23.799L16.7949 20.5995V27C16.7949 27.8295 17.4654 28.5 18.2949 28.5C19.1229 28.5 19.7949 27.8295 19.7949 27V20.5995L25.3389 23.799C26.0574 24.2145 26.9739 23.9685 27.3879 23.25C27.8019 22.533 27.5574 21.615 26.8389 21.201"/>
                </svg>
              </div>
              <span class="gender-label">其他/保密</span>
            </div>
          </van-radio>
        </van-radio-group>
      </section>

      <!-- 日期选择区域 -->
      <section class="date-section">
        <van-date-picker
          v-model="currentDate"
          title="选择出生日期"
          :show-toolbar="false"
          :min-date="minDate"
          :max-date="maxDate"
        />
      </section>

      <!-- 底部操作区域 -->
      <footer class="popup-footer">
        <van-button
          block
          type="primary"
          size="large"
          @click="saveUserInfo"
        >
          保存个人信息
        </van-button>
      </footer>
    </div>
  </van-popup>
</template>

<style scoped>
/* ==================== CSS 变量定义 ==================== */
.user-info-popup {
  /* 主题色彩 */
  --primary-color: #5F59FF;
  --primary-gradient: linear-gradient(277.98deg, #5F59FF 4.58%, #7A59FF 100%);
  --secondary-color: #CBC9FF;
  /* 原始背景有两个线性渐变，第一个（纯白色）会覆盖第二个。
     假设目的是使用更复杂的渐变，或者只是白色。
     保留原始定义以确保"无视觉变化"，如果第一个渐变确实是预期的。
     如果首选纯白色或第二个渐变，可以简化此定义。 */
  --background-gradient: linear-gradient(0deg, #FFFFFF, #FFFFFF), linear-gradient(300.68deg, #EDF2FE 38.54%, #F2F1FE 104.29%);
  --selected-background: rgba(95, 89, 255, 0.05);
  --selected-highlight: #F0F0FF; /* 选中项目和日期选择器高亮条的颜色 */

  /* 文字颜色 */
  --text-primary: #222222;
  --text-secondary: #535353;
  --text-muted: rgba(0, 0, 0, 0.7);

  /* 尺寸规格 */
  --header-padding: 24px 32px;
  --section-padding: 16px;
  --gender-icon-size: 76px;
  --border-radius: 8px;
  --border-radius-circle: 50%;

  /* 阴影效果 */
  --footer-shadow: inset 0px 1px 0px #F0EEF4;

  /* Vant 选择器特定变量（如果 Vant 未覆盖则使用默认值） */
  --van-picker-item-height: 44px; /* 默认 Vant 选择器项目高度 */
}

/* ==================== 弹窗主体布局 ==================== */
.user-info-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-gradient);
}

/* ==================== 头部区域样式 ==================== */
.popup-header {
  padding: var(--header-padding);
  position: relative; /* 用于跳过按钮的绝对定位 */
}

.header-content {
  /* 如果跳过按钮较宽或需要更多空间，则在右侧提供一些空间 */
  padding-right: 32px; 
}

.greeting {
  font-size: 32px;
  font-weight: 600;
  margin: 0;
  display: inline;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.welcome {
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 4px 0;
  display: inline;
  color: var(--text-primary);
}

.description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.skip-button {
  position: absolute;
  top: 12px; /* 调整以便与文本更好地垂直对齐 */
  right: 16px; /* 调整以保持一致的内边距 */
  font-size: 14px;
  color: var(--text-secondary);
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px; /* 可点击区域 */
  transition: color 0.2s ease;
}

.skip-button:hover {
  color: var(--primary-color);
}

/* ==================== 性别选择区域样式 ==================== */
.gender-section {
  padding: 0px 0 32px 0;
}

.gender-options {
  display: flex;
  justify-content: space-around;
  padding: 0 var(--section-padding);
  gap: 8px;
}

.gender-option {
  flex: 0 0 auto;
}

.gender-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.gender-icon {
  width: var(--gender-icon-size);
  height: var(--gender-icon-size);
  border-radius: var(--border-radius-circle);
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(203, 201, 255, 0.1);
  border: 2px solid var(--secondary-color);
}

.gender-icon svg path {
  fill: var(--secondary-color);
}

.gender-label {
  font-size: 14px;
  color: var(--text-primary);
  text-align: center;
  font-weight: 400;
}

/* 性别项目的选中状态 */
.gender-item.is-selected .gender-icon {
  background: var(--selected-background);
  border-color: var(--primary-color);
}

.gender-item.is-selected .gender-icon svg path {
  fill: var(--primary-color);
}

.gender-item.is-selected .gender-label {
  color: var(--primary-color);
  font-weight: 500;
}

/* ==================== 日期选择区域样式 ==================== */
.date-section {
  flex: 1; /* 占用剩余的垂直空间 */
  overflow: hidden; /* 防止选择器内容溢出 */
  min-height: 200px; /* 确保选择器有足够的空间 */
  display: flex; /* 帮助在选择器自身高度小于区域高度时居中 */
  align-items: center; /* 如果可能，垂直居中选择器内容 */
}

/* 日期选择器样式自定义 */
:deep(.van-picker) {
  background-color: transparent; /* 通过选择器显示弹窗背景 */
  width: 100%; /* 确保选择器占用容器的全宽 */
  /* 这个 Vant CSS 变量设置选中项目文本区域的背景样式 */
  --van-picker-option-selected-background: var(--selected-highlight);
}

:deep(.van-picker-column__item) {
  color: var(--text-muted); /* 未选中项目的文字颜色 */
}

:deep(.van-picker-column__item--selected) {
  color: var(--text-primary); /* 选中项目的文字颜色 */
  font-weight: 500;
  font-size: 20px;
}

/* 
  选择器中"选中"行/条的自定义背景。
  这在选择级别上为所有列创建一个可视条。
  使用 Vant 的 `--van-picker-item-height`（默认为 44px）来确保条的高度与项目高度匹配并居中。
*/
:deep(.van-picker-column) {
  position: relative; /* 渐变正确放置所需 */
  background: linear-gradient(
    to bottom,
    transparent 0%,
    transparent calc(50% - (var(--van-picker-item-height, 44px) / 2)), /* 顶部透明部分 */
    var(--selected-highlight) calc(50% - (var(--van-picker-item-height, 44px) / 2)), /* 高亮条开始 */
    var(--selected-highlight) calc(50% + (var(--van-picker-item-height, 44px) / 2)), /* 高亮条结束 */
    transparent calc(50% + (var(--van-picker-item-height, 44px) / 2)), /* 底部透明部分 */
    transparent 100%
  );
}

/* ==================== 底部操作区域样式 ==================== */
.popup-footer {
  padding: var(--section-padding);
  background: #FFFFFF; /* 底部的纯色背景 */
  box-shadow: var(--footer-shadow); /* 顶部边框/阴影 */
}

/* 主要按钮样式自定义 */
:deep(.van-button--primary) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: var(--border-radius);
  height: 51px;
  font-size: 18px;
  font-weight: 500;
  transition: background-color 0.2s ease, border-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
}

:deep(.van-button--primary:hover) {
  background-color: #4A44E6; /* 悬停时稍深的色调 */
  border-color: #4A44E6;
  transform: translateY(-1px); /* 微妙的抬升效果 */
  box-shadow: 0 4px 12px rgba(95, 89, 255, 0.3); /* 悬停时增强阴影 */
}

:deep(.van-button--primary:active) {
  transform: translateY(0); /* 激活时重置抬升 */
  box-shadow: 0 2px 8px rgba(95, 89, 255, 0.2); /* 激活时更柔和的阴影 */
}
</style>