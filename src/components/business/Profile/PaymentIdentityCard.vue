<template>
  <div class="payment-identity-card" :class="{ 'payment-identity-card--expired': isExpired }">
    <!-- 主卡片区域 -->
    <div class="payment-identity-card__main">
      <!-- 背景装饰 -->
      <div class="payment-identity-card__decoration" />

      <!-- 内容区域 -->
      <div class="payment-identity-card__content">
        <!-- AI Relief 标题 -->
        <div class="payment-identity-card__header">
          <div class="payment-identity-card__title">
            AI Relief
            <div class="payment-identity-card__sparkle" />
          </div>
        </div>

        <!-- 陪伴信息 -->
        <div class="payment-identity-card__info">
          {{ companionText }}
        </div>
      </div>
    </div>

    <!-- 底部信息栏 -->
    <div class="payment-identity-card__footer">
      <div class="payment-identity-card__footer-content">
        <div class="payment-identity-card__remaining-label">剩余陪伴</div>
        <div class="payment-identity-card__remaining-value">
          <!-- 实时倒计时显示 -->
          <van-count-down
            v-if="remainingTimeDisplay?.type === 'time' && remainingTimeDisplay.value > 0"
            :time="remainingTimeDisplay.value * 1000"
            :format="countdownFormat"
            class="payment-identity-card__remaining-text"
            @finish="handleCountdownFinish"
          />
          <!-- 天数显示或已过期 -->
          <span v-else class="payment-identity-card__remaining-text">
            {{ remainingTimeDisplay?.text || '0天' }}
          </span>
          <van-icon
            v-if="showArrow"
            name="arrow"
            class="payment-identity-card__arrow"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface RemainingTimeDisplay {
  type: 'days' | 'time'
  value: number
  text: string
  companionDays?: number // 已陪伴天数
}

interface Props {
  remainingTimeDisplay?: RemainingTimeDisplay
  showArrow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  remainingTimeDisplay: () => ({ type: 'days', value: 0, text: '0天', companionDays: 0 }),
  showArrow: false
})

// 计算是否已过期（剩余时长为0）
const isExpired = computed(() => {
  return props.remainingTimeDisplay?.value === 0
})

// 计算倒计时格式
const countdownFormat = computed(() => {
  if (!props.remainingTimeDisplay?.value) return 'HH:mm:ss'

  const totalSeconds = props.remainingTimeDisplay.value
  const totalHours = totalSeconds / (60 * 60)

  if (totalHours > 24) {
    // 大于24小时，显示天数+时分秒格式
    return 'DD天 HH:mm:ss'
  } else {
    // 小于等于24小时，显示时分秒格式
    return 'HH:mm:ss'
  }
})

// 计算陪伴时长文本
const companionText = computed(() => {
  // 直接使用后端返回的companionDays字段
  const days = props.remainingTimeDisplay?.companionDays || 0
  return `已陪伴您${days}天`
})

// 处理倒计时结束
const handleCountdownFinish = () => {
  console.log('会员时长倒计时结束')
}
</script>

<style scoped>
.payment-identity-card {
  /* 装饰元素位置变量 */
  --decoration-left: 64%;
  --decoration-width: 40%;
  --decoration-height: 100%;
  --decoration-bottom: 0%;
  
  /* 灰色状态颜色变量 - 可调整 */
  --expired-primary-color: #9E9EB0;
  --expired-light-color: #F0F0F9;
  --expired-text-color: #9E9EB0;
  --expired-background-color: #fff;
  --expired-decoration-opacity: 0.3;
  
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  width: 100%;
  aspect-ratio: 382 / 167;
  flex: none;
  border: 1px solid #5F59FF;
}

/* 过期状态样式 */
.payment-identity-card--expired {
  border-color: var(--expired-primary-color);
}

.payment-identity-card__main {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: #F0F0FF;
  border-radius: 12px;
  padding: 0;
}

.payment-identity-card--expired .payment-identity-card__main {
  background: var(--expired-light-color);
}

.payment-identity-card__footer {
  position: absolute;
  width: 100%;
  height: 46px;
  left: 0;
  bottom: 0;
  background: #FFFFFF;
  border-radius: 0px 0px 12px 12px;
  padding: 12px 12px 12px 18px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.payment-identity-card--expired .payment-identity-card__footer {
  background: var(--expired-background-color);
}

/* 装饰背景 */
.payment-identity-card__decoration {
  position: absolute;
  left: var(--decoration-left);
  width: var(--decoration-width);
  height: var(--decoration-height);
  bottom: var(--decoration-bottom);
  background: url('@/assets/images/heart-2.svg') no-repeat center / contain;
  z-index: 0;
}

.payment-identity-card--expired .payment-identity-card__decoration {
  opacity: var(--expired-decoration-opacity);
  filter: grayscale(100%);
}

/* 内容区域 */
.payment-identity-card__content {
  position: relative;
  z-index: 1;
}

/* 标题区域 */
.payment-identity-card__header {
  position: absolute;
  width: 100px;
  height: 29px;
  left: 20px;
  top: 18px;
  z-index: 1;
}

.payment-identity-card__title {
  position: absolute;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: clamp(18px, 5.5vw, 22px);
  line-height: 1;
  display: flex;
  align-items: center;
  color: #5F59FF;
  left: 0;
  top: 7px;
  width: 100px;
  height: 22px;
}

.payment-identity-card--expired .payment-identity-card__title {
  color: var(--expired-primary-color);
}

/* Sparkle装饰 */
.payment-identity-card__sparkle {
  position: absolute;
  width: 16px;
  height: 14px;
  left: 82px;
  top: -8px;
  background-image: url('@/assets/icon/sparkle.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.payment-identity-card--expired .payment-identity-card__sparkle {
  filter: grayscale(100%);
  opacity: var(--expired-decoration-opacity);
}

/* 陪伴信息文本 */
.payment-identity-card__info {
  position: absolute;
  left: 20px;
  top: 58px;
  font-weight: 300;
  color: #8B87FF;
  z-index: 1;
}

.payment-identity-card--expired .payment-identity-card__info {
  color: var(--expired-text-color);
}

/* 底部信息栏内容 */
.payment-identity-card__footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 剩余陪伴标签 */
.payment-identity-card__remaining-label {
  font-weight: 350;
  color: #5F59FF;
  white-space: nowrap;
}

.payment-identity-card--expired .payment-identity-card__remaining-label {
  color: var(--expired-primary-color);
}

/* 剩余时间值容器 */
.payment-identity-card__remaining-value {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 剩余时间文本 */
.payment-identity-card__remaining-text {
  color: #5F59FF;
  white-space: nowrap;
}

.payment-identity-card--expired .payment-identity-card__remaining-text {
  color: var(--expired-text-color);
}

/* 箭头图标 */
.payment-identity-card__arrow {
  color: #5F59FF;
}

.payment-identity-card--expired .payment-identity-card__arrow {
  color: var(--expired-primary-color);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .payment-identity-card__decoration {
    left: var(--decoration-left);
    width: var(--decoration-width);
    height: var(--decoration-height);
    bottom: var(--decoration-bottom);
  }
}

@media (max-width: 360px) {
  .payment-identity-card__decoration {
    left: var(--decoration-left);
    width: var(--decoration-width);
    height: var(--decoration-height);
    bottom: var(--decoration-bottom);
  }
}

@media (max-width: 320px) {
  .payment-identity-card__decoration {
    left: var(--decoration-left);
    width: var(--decoration-width);
    height: var(--decoration-height);
    bottom: var(--decoration-bottom);
  }
}
</style>
