<template>
  <van-popup
    :show="modelValue"
    position="left"
    :style="{ width: '80%', maxWidth: '354px', height: '100%' }"
    @update:show="$emit('update:modelValue', $event)"
  >
    <div class="sidebar-content">
      <!-- 用户信息 -->
      <div class="user-profile">
        <div class="avatar-container">
          <van-image
            round
            width="88"
            height="88"
            :src="userInfo?.avatar || defaultAvatar"
            class="user-avatar"
            @click="$emit('edit-profile')"
          />
          <div class="edit-icon-container">
            <img src="@/assets/icon/edit.svg" alt="编辑" class="edit-icon" />
          </div>
        </div>
        <div class="user-info">
          <div class="username">{{ userInfo?.nickname || '未设置昵称' }}</div>
          <div class="user-id">ID：{{ userInfo?.user_id || '未知' }}</div>
          <div class="user-location">IP属地：{{ ipProvince }}</div>
        </div>
      </div>

      <!-- AI Relief 身份卡片 -->
      <div class="identity-card-container">
        <SidebarIdentityCard
          :companion-days="companionDays"
          :remaining-time-display="remainingTimeDisplay"
          @extend-subscription="$emit('extend-subscription')"
          @countdown-finish="handleCountdownFinish"
        />
      </div>

      <!-- 兑换码 -->
      <RedeemCodeInput 
        ref="redeemCodeInputRef"
        @coupon-validated="handleCouponValidated"
        @coupon-cleared="handleCouponCleared"
      />

      <!-- 设置选项 -->
      <div class="settings-options">
        <div class="option" @click="$emit('contact-support')">
          <van-icon name="service-o" color="#39359B" size="16" class="option-icon" />
          <span class="option-text">联系我们</span>
          <van-icon name="arrow" color="#39359B" size="16" class="option-arrow" />
        </div>
        <div class="option" @click="$emit('open-settings')">
          <van-icon name="setting-o" color="#39359B" size="16" class="option-icon" />
          <span class="option-text">设置</span>
          <van-icon name="arrow" color="#39359B" size="16" class="option-arrow" />
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import type { UserInfo } from '@/types/api'
import defaultAvatarImg from '@/assets/images/user-avtar.png'
import RedeemCodeInput from './RedeemCodeInput.vue'

interface RemainingTimeDisplay {
  type: 'days' | 'time'
  value: number
  text: string
  companionDays?: number // 已陪伴天数
}

// 定义组件的属性
interface Props {
  modelValue: boolean
  userInfo?: UserInfo | null
}

const props = defineProps<Props>()
const router = useRouter()

// 兑换码组件引用
const redeemCodeInputRef = ref()

// 定义组件的事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'extend-subscription'): void
  (e: 'contact-support'): void
  (e: 'open-settings'): void
  (e: 'edit-profile'): void
  (e: 'countdown-finish'): void
}>()

// 默认头像
const defaultAvatar = defaultAvatarImg

// 计算陪伴天数
const companionDays = computed(() => {
  // 直接使用后端返回的companionDays字段
  return props.userInfo?.companionDays || 0
})

// 计算IP属地省份
const ipProvince = computed(() => {
  const ipLocation = props.userInfo?.ipLocation
  if (!ipLocation || ipLocation === '未知') {
    return '未知'
  }
  
  // 从完整地理位置信息中提取省份
  // 格式通常是：中国 浙江 杭州 或 中国 北京市 海淀
  const parts = ipLocation.split(' ')
  if (parts.length >= 2) {
    // 返回第二部分，即省份
    return parts[1]
  }
  
  // 如果格式不符合预期，返回原始信息
  return ipLocation
})

// 计算剩余时长显示格式
const remainingTimeDisplay = computed((): RemainingTimeDisplay => {
  if (!props.userInfo?.duration) return {
    type: 'days',
    value: 0,
    text: '0天',
    companionDays: props.userInfo?.companionDays || 0
  }

  const totalSeconds = props.userInfo.duration
  const totalHours = totalSeconds / (60 * 60)

  if (totalHours > 24) {
    // 大于24小时，显示天数+剩余时间倒计时格式
    const days = Math.floor(totalSeconds / (24 * 60 * 60))
    const remainingSeconds = totalSeconds % (24 * 60 * 60)
    const hours = Math.floor(remainingSeconds / (60 * 60))
    const minutes = Math.floor((remainingSeconds % (60 * 60)) / 60)
    const seconds = Math.floor(remainingSeconds % 60)

    const timeText = `${days}天 + ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    return {
      type: 'time',
      value: totalSeconds,
      text: timeText,
      companionDays: props.userInfo?.companionDays || 0
    }
  } else {
    // 小于等于24小时，显示 hh:mm:ss 格式
    const hours = Math.floor(totalSeconds / (60 * 60))
    const minutes = Math.floor((totalSeconds % (60 * 60)) / 60)
    const seconds = Math.floor(totalSeconds % 60)

    const timeText = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    return {
      type: 'time',
      value: totalSeconds,
      text: timeText,
      companionDays: props.userInfo?.companionDays || 0
    }
  }
})

// 处理兑换码验证结果
const handleCouponValidated = (data: { valid: boolean; discount: number; expiry: string; message: string }) => {
  console.log('侧边栏兑换码验证结果:', data)
  
  // 如果兑换码验证成功，跳转到支付页面
  if (data.valid) {
    // 获取兑换码信息
    const couponInfo = redeemCodeInputRef.value?.getCouponInfo()
    const couponCode = couponInfo?.code || ''
    
    // 关闭侧边栏
    emit('update:modelValue', false)
    
    // 跳转到支付页面，传递兑换码信息
    router.push({
      name: 'Payment',
      query: {
        coupon_validated: 'true',
        coupon_code: couponCode,
        coupon_discount: data.discount.toString(),
        coupon_expiry: data.expiry,
        coupon_message: data.message
      }
    })
  }
}

// 处理兑换码清空
const handleCouponCleared = () => {
  console.log('侧边栏兑换码已清空')
  // 可以在这里添加清空后的业务逻辑
}

// 处理倒计时结束
const handleCountdownFinish = () => {
  console.log('会员时长倒计时结束')
  emit('countdown-finish')
  // 可以在这里触发用户信息刷新等操作
}
</script>

<style scoped>
.sidebar-content {
  padding: 80px 14px 20px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  background-color: #F5F7FF;
  height: 100%;
  overflow-y: auto;
}

/* 用户资料样式 */
.user-profile {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
  width: 100%;
}

.avatar-container {
  position: relative;
  width: 88px;
  height: 88px;
  cursor: pointer;
}

.user-avatar {
  border: 2px solid #FFFFFF;
  border-radius: 51px;
}

.edit-icon-container {
  position: absolute;
  bottom: 8%;
  right: 8%;
  width: 20px;
  height: 20px;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edit-icon {
  width: 12px;
  height: 12px;
}

.user-info {
  flex: 1;
  margin-left: 12px;
}

.username {
  font-size: 18px;
  font-weight: 600;
  color: #222222;
  margin-bottom: 4px;
}

.user-id, .user-location {
  font-size: 12px;
  color: #222222;
  margin-bottom: 4px;
}

/* AI Relief 身份卡片容器 */
.identity-card-container {
  width: 100%;
  margin-bottom: 14px;
  /* 确保容器能够正确包含卡片 */
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

/* 设置选项 */
.settings-options {
  background-color: #FFFFFF;
  border-radius: 10px;
  overflow: hidden;
}

.option {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 0.333px solid rgba(84, 84, 86, 0.34);
  cursor: pointer;
}

.option:last-child {
  border-bottom: none;
}

.option-icon {
  margin-right: 4px;
}

.option-text {
  flex: 1;
  font-size: 14px;
  color: #39359B;
}

.option-arrow {
  color: #39359B;
}
</style>
