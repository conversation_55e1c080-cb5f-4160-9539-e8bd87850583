<template>
    <div class="redeem-code-input">
      <van-field
        v-model="couponCode"
        placeholder="请输入优惠码"
        class="redeem-input"
        :disabled="validatingCoupon"
        :readonly="couponValid"
      >
        <template #button>
          <van-button
            v-if="!couponValid"
            size="small"
            type="primary"
            :loading="validatingCoupon"
            :disabled="!couponCode"
            class="verify-button"
            @click="validateCouponCode"
          >
            点击验证
          </van-button>
          <van-button
            v-else
            size="small"
            type="success"
            disabled
            class="verified-button"
          >
            已验证
          </van-button>
        </template>
      </van-field>

      <!-- 验证成功后的信息展示 -->
      <div v-if="couponValid" class="coupon-info mt-md">
        <!-- <div v-if="couponDiscount > 0" class="coupon-discount mb-sm">
          优惠金额: ¥{{ couponDiscount }}
        </div> -->
        <div v-if="couponExpiry" class="coupon-expiry mb-sm">
          优惠兑换码生效中 {{ countdownDisplay }}后过期
        </div>
      </div>

      <!-- 验证失败的错误信息 -->
      <div v-if="couponMessage && !couponValid && couponCode" class="coupon-error mt-sm rounded-sm">
        {{ couponMessage }}
      </div>
    </div>
  </template>

  <script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import { showToast } from 'vant'
  import { validateCoupon } from '@/apis/payment'

  // 定义组件的事件
  const emit = defineEmits<{
    (e: 'coupon-validated', data: { valid: boolean; discount: number; expiry: string; message: string }): void
    (e: 'coupon-cleared'): void
  }>()

  // 兑换码相关状态
  const couponCode = ref('')
  const couponValid = ref(false)
  const couponDiscount = ref(0)
  const couponExpiry = ref('')
  const couponMessage = ref('')
  const validatingCoupon = ref(false)

  // 倒计时相关
  const currentTime = ref(Date.now())
  let countdownTimer: number | null = null

  // 计算倒计时显示
  const countdownDisplay = computed(() => {
    if (!couponExpiry.value) return ''
    
    const expiryTime = new Date(couponExpiry.value).getTime()
    const timeDiff = expiryTime - currentTime.value
    
    if (timeDiff <= 0) {
      return '已过期'
    }
    
    // 计算剩余时间
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)
    
    // 根据剩余时间长度决定显示格式
    if (days > 0) {
      return `${days}天${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    } else if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  })

  // 开始倒计时
  const startCountdown = () => {
    if (countdownTimer) return
    
    countdownTimer = window.setInterval(() => {
      currentTime.value = Date.now()
      
      // 检查是否已过期
      if (couponExpiry.value) {
        const expiryTime = new Date(couponExpiry.value).getTime()
        if (currentTime.value >= expiryTime) {
          stopCountdown()
          // 可以触发过期事件
          couponValid.value = false
          couponMessage.value = '兑换码已过期'
        }
      }
    }, 1000)
  }

  // 停止倒计时
  const stopCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }

  // 验证兑换码
  const validateCouponCode = async (): Promise<void> => {
    if (!couponCode.value) {
      showToast('请输入兑换码')
      return
    }

    validatingCoupon.value = true
    try {
      const result = await validateCoupon(couponCode.value)
      couponValid.value = result.valid
      couponDiscount.value = result.discount || 0
      couponExpiry.value = result.expiry || ''
      couponMessage.value = result.message || ''

      // 如果验证成功且有过期时间，开始倒计时
      if (result.valid && result.expiry) {
        startCountdown()
      }

      // 发送验证结果事件
      emit('coupon-validated', {
        valid: result.valid,
        discount: couponDiscount.value,
        expiry: couponExpiry.value,
        message: couponMessage.value
      })

      if (result.valid) {
        showToast('限时优惠验证成功')
      } else {
        showToast(result.message || '兑换码无效')
      }
    } catch (error) {
      console.error('验证兑换码失败:', error)
      showToast('验证兑换码失败，请重试')

      // 发送验证失败事件
      emit('coupon-validated', {
        valid: false,
        discount: 0,
        expiry: '',
        message: '验证失败，请重试'
      })
    } finally {
      validatingCoupon.value = false
    }
  }

  // 清空兑换码
  const clearCoupon = (): void => {
    couponCode.value = ''
    couponValid.value = false
    couponDiscount.value = 0
    couponExpiry.value = ''
    couponMessage.value = ''
    stopCountdown()
    emit('coupon-cleared')
  }

  // 获取当前兑换码信息
  const getCouponInfo = () => {
    return {
      code: couponCode.value,
      valid: couponValid.value,
      discount: couponDiscount.value,
      expiry: couponExpiry.value,
      message: couponMessage.value
    }
  }

  // 设置兑换码信息（用于从外部同步数据）
  const setCouponInfo = (info: { code: string; valid: boolean; discount: number; expiry: string; message: string }) => {
    couponCode.value = info.code
    couponValid.value = info.valid
    couponDiscount.value = info.discount
    couponExpiry.value = info.expiry
    couponMessage.value = info.message
    
    // 如果设置为有效状态且有过期时间，开始倒计时
    if (info.valid && info.expiry) {
      startCountdown()
    } else {
      stopCountdown()
    }
  }

  // 组件挂载时如果已有有效兑换码则开始倒计时
  onMounted(() => {
    if (couponValid.value && couponExpiry.value) {
      startCountdown()
    }
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopCountdown()
  })

  // 暴露方法给父组件
  defineExpose({
    clearCoupon,
    getCouponInfo,
    setCouponInfo,
    validateCouponCode
  })
  </script>

  <style scoped>
  .redeem-code-input {
    width: 100%;
    margin-bottom: var(--ai-spacing-md);
  }

  /* 输入框容器样式 */
  .redeem-input {
    --van-field-border: none;
    --van-field-input-background: #FFFFFF;
    background: #FFFFFF;
    border-radius: var(--ai-radius-sm);
    padding: 10px var(--ai-spacing-lg);
    width: 100%;
    max-width: 382px;
    height: 51px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  /* 确保 van-field 内部结构垂直居中 */
  .redeem-input :deep(.van-cell) {
    padding: 0;
    background: transparent;
  }

  .redeem-input :deep(.van-field__body) {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .redeem-input :deep(.van-field__button) {
    display: flex;
    align-items: center;
    margin-left: var(--ai-spacing-sm);
  }

  /* 输入框文本样式 */
  .redeem-input :deep(.van-field__control) {
    font-family: var(--ai-font-family);
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
    color: #555555;
    background: transparent;
    border: none;
    outline: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
    text-align: left;
  }

  /* placeholder 样式 */
  .redeem-input :deep(.van-field__control::placeholder) {
    color: #90949E;
  }

  /* 验证按钮样式 */
  .verify-button {
    --van-button-primary-background: transparent;
    --van-button-primary-border-color: transparent;
    --van-button-small-height: 25px;
    --van-button-small-padding: 0 var(--ai-spacing-sm);
    --van-button-small-font-size: 14px;
    --van-button-small-min-width: auto;
    
    font-family: var(--ai-font-family);
    font-weight: 400;
    line-height: 25px;
    color: var(--ai-primary);
    background: transparent;
    border: none;
    cursor: pointer;
  }

  .verify-button:hover {
    opacity: 0.8;
  }

  .verify-button:disabled {
    color: #AAAAAA;
    opacity: 1;
    background: transparent;
    border: none;
    cursor: default;
  }

  /* 已验证按钮样式 */
  .verified-button {
    --van-button-success-background: transparent;
    --van-button-success-border-color: transparent;
    --van-button-small-height: 25px;
    --van-button-small-padding: 0 var(--ai-spacing-sm);
    --van-button-small-font-size: 14px;
    --van-button-small-min-width: auto;
    
    font-family: var(--ai-font-family);
    font-weight: 400;
    line-height: 25px;
    color: #898A8D;
    background: transparent;
    border: none;
  }

  /* 优惠信息样式 */
  .coupon-info {
    font-size: 12px;
  }

  .coupon-discount {
    color: #FF6B35;
    font-weight: 500;
  }

  .coupon-expiry {
    color: #898A8D;
    margin-left: var(--ai-spacing-sm);
  }

  /* 错误信息样式 */
  .coupon-error {
    font-size: 12px;
    color: #FF3B30;
    padding: var(--ai-spacing-sm) var(--ai-spacing-md);
    background-color: #FFF5F5;
  }

  /* 响应式适配 */
  @media (max-width: 375px) {
    .redeem-input {
      padding: var(--ai-spacing-sm) var(--ai-spacing-md);
      height: 47px;
    }

    .redeem-input :deep(.van-field__control) {
      font-size: 13px;
    }

    .verify-button, .verified-button {
      --van-button-small-font-size: 13px;
      height: 23px;
      font-size: 13px;
      line-height: 23px;
    }

    .coupon-discount,
    .coupon-expiry {
      font-size: 11px;
    }

    .coupon-error {
      font-size: 11px;
      padding: 6px var(--ai-spacing-sm);
    }
  }

  @media (max-width: 320px) {
    .redeem-input {
      padding: 6px 10px;
      height: 43px;
    }

    .redeem-input :deep(.van-field__control) {
      font-size: 12px;
    }

    .verify-button, .verified-button {
      --van-button-small-font-size: 12px;
      height: 21px;
      font-size: 12px;
      line-height: 21px;
    }
  }
  </style>