<template>
  <div class="identity-card" :class="{ 'identity-card--expired': isExpired }">
    <!-- 主卡片区域 -->
    <div class="identity-card__main">
      <!-- 背景装饰 -->
      <div class="identity-card__decoration" />

      <!-- 内容区域 -->
      <div class="identity-card__content">
        <!-- AI Relief 标题 -->
        <div class="identity-card__header">
          <h2 class="identity-card__title">
            AI Relief
            <span class="identity-card__sparkle" />
          </h2>
        </div>

        <!-- 陪伴信息 -->
        <p class="identity-card__info">
          已陪伴您{{ remainingTimeDisplay?.companionDays || 0 }}天
        </p>

        <!-- 延长陪伴按钮 -->
        <div class="identity-card__actions">
          <van-button
            class="identity-card__button"
            @click="$emit('extend-subscription')"
          >
            延长陪伴
          </van-button>
        </div>
      </div>
    </div>

    <!-- 底部信息栏 -->
    <div class="identity-card__footer">
      <span class="identity-card__label">剩余陪伴</span>
      <span class="identity-card__value">
        <!-- 实时倒计时显示 -->
        <van-count-down
          v-if="remainingTimeDisplay?.type === 'time' && remainingTimeDisplay.value > 0"
          :time="remainingTimeDisplay.value * 1000"
          :format="countdownFormat"
          class="identity-card__countdown"
          @finish="handleCountdownFinish"
        />
        <!-- 天数显示或已过期 -->
        <span v-else>{{ remainingTimeDisplay?.text || '0天' }}</span>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface RemainingTimeDisplay {
  type: 'days' | 'time'
  value: number
  text: string
  companionDays?: number // 已陪伴天数
}

interface Props {
  remainingTimeDisplay?: RemainingTimeDisplay
  showArrow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  remainingTimeDisplay: () => ({ type: 'days', value: 0, text: '0天', companionDays: 0 }),
  showArrow: true
})

// 计算是否已过期（剩余时长为0）
const isExpired = computed(() => {
  return props.remainingTimeDisplay?.value === 0
})

// 计算倒计时格式
const countdownFormat = computed(() => {
  if (!props.remainingTimeDisplay?.value) return 'HH:mm:ss'

  const totalSeconds = props.remainingTimeDisplay.value
  const totalHours = totalSeconds / (60 * 60)

  if (totalHours > 24) {
    // 大于24小时，显示天数+时分秒格式
    return 'DD天 HH:mm:ss'
  } else {
    // 小于等于24小时，显示时分秒格式
    return 'HH:mm:ss'
  }
})



const emit = defineEmits<{
  (e: 'extend-subscription'): void
  (e: 'countdown-finish'): void
}>()

// 处理倒计时结束
const handleCountdownFinish = () => {
  emit('countdown-finish')
}
</script>

<style scoped>
/* CSS变量 */
.identity-card {
  /* 颜色变量 */
  --primary-color: #5F59FF;
  --primary-light: #F0F0FF;
  --primary-dark: #4a44cc;
  --text-white: #F8F8FF;
  --bg-white: #FFFFFF;
  
  /* 灰色状态颜色变量 - 可调整 */
  --expired-primary-color: #9E9EB0;
  --expired-light-color: #F0F0F9;
  --expired-text-color: #9E9EB0;
  --expired-background-color: #fff;
  --expired-decoration-opacity: 0.5;
  
  /* 尺寸变量 */
  --border-radius: 12px;
  --border-radius-small: 8px;
  --border-radius-button: 6px;
  --footer-height: 46px;
  --spacing-xs: 4px;
  --spacing-sm: 12px;
  --spacing-md: 18px;
  --spacing-lg: 20px;
  
  /* 字体变量 */
  --font-family: 'PingFang SC', sans-serif;
  --font-size-title: clamp(18px, 5.5vw, 22px);
  --font-weight-light: 300;
  --font-weight-medium: 350;
  --font-weight-semibold: 600;
}

/* 主容器 */
.identity-card {
  position: relative;
  width: 100%;
  aspect-ratio: 326 / 212;
  border-radius: var(--border-radius-small);
  overflow: hidden;
  box-sizing: border-box;
}

/* 主卡片区域 */
.identity-card__main {
  height: calc(100% - var(--footer-height));
  background: var(--primary-light);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  position: relative;
  overflow: hidden;
}

.identity-card--expired .identity-card__main {
  background: var(--expired-light-color);
}

/* 背景装饰 */
.identity-card__decoration {
  position: absolute;
  right: -32%;
  bottom: -20%;
  width: 100%;
  height: 100%;
  background: url('@/assets/images/heart-1.svg') no-repeat center / contain;
  z-index: 0;
}

.identity-card--expired .identity-card__decoration {
  opacity: var(--expired-decoration-opacity);
  filter: grayscale(100%);
}

/* 内容布局 */
.identity-card__content {
  position: relative;
  z-index: 1;
  height: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 标题区域 */
.identity-card__header {
  margin-top: 2px;
}

.identity-card__title {
  position: relative;
  font-family: var(--font-family);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-title);
  line-height: 1;
  color: var(--primary-color);
  margin: 0;
  width: fit-content;
}

.identity-card--expired .identity-card__title {
  color: var(--expired-primary-color);
}

/* Sparkle装饰 */
.identity-card__sparkle {
  position: absolute;
  top: -8px;
  right: -18px;
  width: 16px;
  height: 14px;
  background: url('@/assets/icon/sparkle.svg') no-repeat center / contain;
}

.identity-card--expired .identity-card__sparkle {
  filter: grayscale(100%);
  opacity: var(--expired-decoration-opacity);
}

/* 陪伴信息 */
.identity-card__info {
  font-family: var(--font-family);
  font-weight: var(--font-weight-light);
  color: var(--primary-color);
  margin: 2px 0 0 0;
}

.identity-card--expired .identity-card__info {
  color: var(--expired-text-color);
}

/* 按钮区域 */
.identity-card__actions {
  margin-top: 2px;
}

.identity-card__button {
  height: 32px;
  background: var(--primary-color);
  border: none;
  border-radius: var(--border-radius-button);
  color: var(--text-white);
  font-family: var(--font-family);
  display: flex;
  align-items: center;
  justify-content: center;
}

.identity-card__button:active {
  background-color: var(--primary-dark);
}

/* 底部信息栏 */
.identity-card__footer {
  height: var(--footer-height);
  background: var(--bg-white);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.identity-card--expired .identity-card__footer {
  background: var(--expired-background-color);
}

.identity-card__label {
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  white-space: nowrap;
}

.identity-card--expired .identity-card__label {
  color: var(--expired-primary-color);
}

.identity-card__value {
  font-family: var(--font-family);
  color: var(--primary-color);
  white-space: nowrap;
}

.identity-card--expired .identity-card__value {
  color: var(--expired-primary-color);
}

/* 倒计时样式 */
.identity-card__countdown {
  font-family: var(--font-family);
  color: var(--primary-color) !important;
  white-space: nowrap;
}

.identity-card--expired .identity-card__countdown {
  color: var(--expired-primary-color) !important;
}

.identity-card__countdown :deep(.van-count-down) {
  color: inherit !important;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .identity-card {
    --spacing-sm: 10px;
    --spacing-md: 16px;
    --spacing-lg: 18px;
  }
}

@media (max-width: 360px) {
  .identity-card {
    --spacing-sm: 8px;
    --spacing-md: 14px;
    --spacing-lg: 16px;
  }
  
  .identity-card__decoration {
    right: -18%;
    width: 65%;
    bottom: -11%;
  }
}

@media (max-width: 320px) {
  .identity-card__decoration {
    right: -24%;
    width: 60%;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  .identity-card {
    --primary-color: #4A3AFF;
    --primary-light: #E8E5FF;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .identity-card__button {
    transition: none;
  }
}
</style>
