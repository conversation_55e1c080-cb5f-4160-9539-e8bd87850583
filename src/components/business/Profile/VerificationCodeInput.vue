<template>
  <div class="verification-code-input-wrapper">
    <input
      v-for="index in length"
      :key="index"
      :ref="el => setInputRef(index - 1, el as HTMLInputElement)"
      v-model="digits[index - 1]"
      type="tel"
      maxlength="1"
      class="code-digit"
      :class="{ 'filled': digits[index - 1] }"
      @input="event => handleInput(event as InputEvent, index - 1)"
      @keydown="event => handleKeyDown(event as KeyboardEvent, index - 1)"
      @focus="event => handleFocus(event as FocusEvent)"
      @paste="event => handlePaste(event as ClipboardEvent, index - 1)"
      :aria-label="`Digit ${index}`"
      pattern="\d*"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  length: {
    type: Number,
    default: 4,
  },
})

const emit = defineEmits(['update:modelValue', 'filled'])

const digits = ref<string[]>(new Array(props.length).fill(''))
const inputRefs = ref<HTMLInputElement[]>([])

const setInputRef = (index: number, el: HTMLInputElement | null) => {
  if (el) {
    inputRefs.value[index] = el;
  }
}

watch(() => props.modelValue, (newValue) => {
  if (newValue !== digits.value.join('')) {
    for (let i = 0; i < props.length; i++) {
      digits.value[i] = newValue[i] || ''
    }
  }
})

watch(digits, (newDigits) => {
  const code = newDigits.join('')
  emit('update:modelValue', code)
  if (code.length === props.length && /^\d+$/.test(code)) {
    emit('filled', code)
  }
}, { deep: true })

const handleInput = (event: InputEvent, index: number) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;

  if (!/^\d?$/.test(value)) {
    digits.value[index] = '';
    target.value = '';
    return;
  }

  digits.value[index] = value;

  if (value && index < props.length - 1) {
    nextTick(() => {
      inputRefs.value[index + 1]?.focus()
    })
  }
}

const handleKeyDown = (event: KeyboardEvent, index: number) => {
  if (event.key === 'Backspace') {
    if (!digits.value[index] && index > 0) {
      event.preventDefault();
      digits.value[index - 1] = '';
      inputRefs.value[index - 1]?.focus();
    }
  } else if (event.key === 'ArrowLeft' && index > 0) {
    event.preventDefault();
    inputRefs.value[index - 1]?.focus();
  } else if (event.key === 'ArrowRight' && index < props.length - 1) {
    event.preventDefault();
    inputRefs.value[index + 1]?.focus();
  } else if (event.key.length === 1 && !/\d/.test(event.key) && event.key !== 'Unidentified') {
    event.preventDefault();
  }
}

const handleFocus = (event: FocusEvent) => {
  (event.target as HTMLInputElement).select()
}

const handlePaste = (event: ClipboardEvent, startIndex: number) => {
  event.preventDefault()
  const pastedData = event.clipboardData?.getData('text').replace(/\s+/g, '').match(/\d+/g)?.join('').slice(0, props.length - startIndex)

  if (pastedData) {
    let currentPastedIndex = 0;
    for (let i = startIndex; i < props.length && currentPastedIndex < pastedData.length; i++) {
      digits.value[i] = pastedData[currentPastedIndex];
      currentPastedIndex++;
    }
    
    const nextFocusIndex = Math.min(startIndex + pastedData.length, props.length -1);
    if (inputRefs.value[nextFocusIndex]) {
        inputRefs.value[nextFocusIndex].focus();
    } else if (inputRefs.value[props.length -1]) {
        inputRefs.value[props.length -1].focus();
    }
  }
}

onMounted(() => {
  if (props.modelValue) {
    for (let i = 0; i < props.length; i++) {
      digits.value[i] = props.modelValue[i] || '';
    }
  }
});

</script>

<style scoped>
.verification-code-input-wrapper {
  display: flex;
  justify-content: space-between;
  /* The gap will be determined by the overall width and item width.
     Let's aim for a total width that allows for some spacing.
     If each box is ~50-60px, and we have 4 boxes, plus gaps.
     Example: (4 * 56px) + (3 * 12px) = 224 + 36 = 260px
  */
  gap: 12px; /* Adjust gap as needed to match visual spacing */
  width: 100%;
  max-width: 244px; /* Max width for the container of 4 boxes */
}

.code-digit {
  width: 52px;  /* Adjusted for a slightly more rectangular look if needed, or keep 50px */
  height: 56px; /* Making them square */
  text-align: center;
  font-size: 24px;
  font-weight: 500; /* Slightly bolder digits */
  border: none;
  border-radius: 8px; /* More pronounced rounded corners as per image */
  background-color: #FFFFFF; /* White background */
  color: #2b314c; /* Dark blue/grey text color from Figma context */
  caret-color: #2b314c; /* Caret color matching text */
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: textfield;
  box-sizing: border-box; /* Ensure padding/border are included in width/height */
}

.code-digit::-webkit-outer-spin-button,
.code-digit::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.code-digit:focus {
  border-color: #5F59FF; /* Primary accent color for focus border */
  /* Optional: add a subtle shadow for more emphasis on focus, matching common UI patterns */
  box-shadow: 0 0 0 2px rgba(95, 89, 255, 0.2);
  outline: none;
}

/* If you want a specific style for filled inputs, you can use .filled */
/* .code-digit.filled { */
  /* Example: border-color: #5F59FF; */
/* } */
</style>