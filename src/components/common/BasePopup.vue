<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  show: boolean
  title?: string
  position?: 'center' | 'top' | 'bottom' | 'left' | 'right'
  closeOnClickOverlay?: boolean
  round?: boolean
  closeable?: boolean
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  position: 'center',
  closeOnClickOverlay: true,
  round: true,
  closeable: true
})

const emit = defineEmits<Emits>()

const localShow = ref(props.show)

watch(() => props.show, (val) => {
  localShow.value = val
})

watch(localShow, (val) => {
  emit('update:show', val)
  if (!val) {
    emit('close')
  }
})
</script>

<template>
  <van-popup
    v-model:show="localShow"
    :position="position"
    :close-on-click-overlay="closeOnClickOverlay"
    :round="round"
    :closeable="closeable"
    class="base-popup"
  >
    <div v-if="title" class="base-popup__title">{{ title }}</div>
    <div class="base-popup__content">
      <slot></slot>
    </div>
    <div class="base-popup__footer">
      <slot name="footer"></slot>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.base-popup {
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;

  &__title {
    padding: 16px 16px 0;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
  }

  &__content {
    padding: 16px;
  }

  &__footer {
    padding: 0 16px 16px;
  }
}
</style>