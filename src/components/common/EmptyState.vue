<script setup lang="ts">
import { Empty } from 'vant'

interface Props {
  description?: string
  image?: string
  imageSize?: number | string
}

withDefaults(defineProps<Props>(), {
  description: '暂无数据',
  image: 'empty'
})
</script>

<template>
  <div class="empty-state">
    <van-empty
      :description="description"
      :image="image"
      :image-size="imageSize"
    >
      <template #default>
        <slot></slot>
      </template>
    </van-empty>
  </div>
</template>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 32px 0;
}
</style>