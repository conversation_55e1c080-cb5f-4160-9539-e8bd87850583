<script setup lang="ts">
import { Loading } from 'vant'

interface Props {
  text?: string
  vertical?: boolean
  color?: string
  size?: string | number
  textSize?: string | number
  textColor?: string
}

withDefaults(defineProps<Props>(), {
  text: '加载中...',
  vertical: true,
  color: '#1989fa',
  size: '24px'
})
</script>

<template>
  <div class="loading-indicator">
    <van-loading
      :color="color"
      :size="size"
      :text="text"
      :vertical="vertical"
      :text-size="textSize"
      :text-color="textColor"
    />
  </div>
</template>

<style lang="scss" scoped>
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 80px;
}
</style>