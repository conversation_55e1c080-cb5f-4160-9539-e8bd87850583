/**
 * 环境配置
 * 统一管理各种环境变量和配置
 */

export interface AppConfig {
  // 基础配置
  apiBaseUrl: string
  wsBaseUrl: string
  enableMock: boolean
  
  // WebSocket配置
  websocket: {
    reconnectInterval: number
    maxReconnectAttempts: number
    heartbeatInterval: number
  }
  
  // 语音识别配置
  voice: {
    maxDuration: number
    minDuration: number
    sampleRate: number
    channels: number
  }
  
  // 外部服务配置
  external: {
    aiServiceUrl: string
    aiApiKey: string
  }
}

// 获取环境配置
function getConfig(): AppConfig {
  const isDev = import.meta.env.DEV
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || ''
  
  // WebSocket URL构建
  let wsBaseUrl = ''
  if (apiBaseUrl) {
    // 从API URL构建WebSocket URL
    const url = new URL(apiBaseUrl)
    const protocol = url.protocol === 'https:' ? 'wss:' : 'ws:'
    wsBaseUrl = `${protocol}//${url.host}`
  } else {
    // 使用当前域名
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    wsBaseUrl = `${protocol}//${window.location.host}`
  }
  
  // 检测是否为微信环境
  const isWechatEnv = typeof window !== 'undefined' &&
                      /micromessenger/i.test(window.navigator.userAgent)

  // 智能模拟模式：在浏览器环境下启用模拟，在微信环境下使用真实API
  const shouldEnableMock = import.meta.env.VITE_ENABLE_MOCK === 'true' ||
                          (import.meta.env.VITE_ENABLE_MOCK === 'auto' && !isWechatEnv) ||
                          (import.meta.env.VITE_ENABLE_MOCK !== 'false' && !apiBaseUrl)

  return {
    // 基础配置
    apiBaseUrl,
    wsBaseUrl,
    enableMock: shouldEnableMock,
    
    // WebSocket配置
    websocket: {
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000
    },
    
    // 语音识别配置
    voice: {
      maxDuration: 60, // 最大录音时长（秒）
      minDuration: 1,  // 最小录音时长（秒）
      sampleRate: 16000, // 采样率
      channels: 1      // 声道数
    },
    
    // 外部服务配置
    external: {
      aiServiceUrl: import.meta.env.VITE_AI_SERVICE_URL || 'https://www.airelief.cn:8997/agent',
      aiApiKey: import.meta.env.VITE_AI_API_KEY || 'test_api_key'
    }
  }
}

// 导出配置实例
export const appConfig = getConfig()

// 打印配置信息（仅开发环境）
if (import.meta.env.DEV) {
  console.log('🔧 App Configuration:', {
    apiBaseUrl: appConfig.apiBaseUrl,
    wsBaseUrl: appConfig.wsBaseUrl,
    enableMock: appConfig.enableMock,
    websocket: appConfig.websocket,
    voice: appConfig.voice,
    external: {
      aiServiceUrl: appConfig.external.aiServiceUrl,
      aiApiKey: appConfig.external.aiApiKey.substring(0, 8) + '...' // 隐藏完整API密钥
    }
  })
}
