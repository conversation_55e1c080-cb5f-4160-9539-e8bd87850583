// API相关常量
export const API_ENDPOINTS = {
  // 授权模块
  WECHAT_LOGIN: '/api/v1/airelief/auth/wechat-login',
  WECHAT_AUTH_URL: '/api/v1/airelief/auth/wechat-auth-url',
  WECHAT_CALLBACK: '/api/v1/airelief/auth/wechat-callback',
  WECHAT_JSAPI_CONFIG: '/api/v1/airelief/auth/wechat-jsapi-config',
  LOGOUT: '/api/v1/airelief/auth/logout',
  AGREEMENT_STATUS: '/api/v1/airelief/auth/agreement-status',
  
  // 用户模块
  USER_PROFILE: '/api/v1/airelief/user/profile',
  UPDATE_PROFILE: '/api/v1/airelief/user/profile',
  UPLOAD_AVATAR: '/api/v1/airelief/user/avatar',
  SEND_SMS_CODE: '/api/v1/airelief/user/send-sms-code',
  BIND_PHONE: '/api/v1/airelief/user/bind-phone',
  UNBIND_PHONE: '/api/v1/airelief/user/unbind-phone',
  BIND_WECHAT: '/api/v1/airelief/user/bind-wechat',
  UNBIND_WECHAT: '/api/v1/airelief/user/unbind-wechat',
  DEACTIVATE_ACCOUNT: '/api/v1/airelief/user/deactivate',
  ACTIVATE_TRIAL: '/api/v1/airelief/user/activate-trial',

  // 引导模块
  ONBOARDING_MESSAGES: '/api/v1/airelief/onboarding/messages',
  CLAIM_BENEFIT: '/api/v1/airelief/onboarding/claim-benefit',

  // 聊天模块
  CHAT_HISTORY: '/api/v1/airelief/chat/history',
  SEND_MESSAGE: '/api/v1/airelief/chat/send-message',
  UPLOAD_AUDIO: '/api/v1/airelief/chat/upload-audio',
  
  // 支付模块
  PAYMENT_PACKAGES: '/api/v1/airelief/payment/packages',
  PAYMENT_PACKAGES_ALL: '/api/v1/airelief/payment/packages/all', // 后台管理
  CREATE_ORDER: '/api/v1/airelief/payment/create-order',
  PAYMENT_NOTIFY: '/api/v1/airelief/payment/notify',
  ORDER_STATUS: '/api/v1/airelief/payment/orders', // 查询订单状态: GET /orders/{order_id}/status
  USER_ORDERS: '/api/v1/airelief/payment/orders', // 用户订单列表: GET /orders?user_id=xxx
  
  // 后台管理订单接口
  ADMIN_ORDERS_ALL: '/api/v1/airelief/payment/orders/all',
  ADMIN_ORDER_DETAIL: '/api/v1/airelief/payment/orders', // GET /orders/{order_id}
  ADMIN_UPDATE_ORDER_STATUS: '/api/v1/airelief/payment/orders', // PUT /orders/{order_id}/status
  ADMIN_ORDER_STATISTICS: '/api/v1/airelief/payment/orders/statistics',
  
  // 优惠券相关
  VALIDATE_COUPON: '/api/v1/airelief/coupon/validate',
  USE_COUPON: '/api/v1/airelief/coupon/redeem',
  VALIDATE_COUPON_FOR_PAYMENT: '/api/v1/airelief/coupon/validate-for-payment',

  // 协议相关
  GET_PUBLIC_AGREEMENT: '/api/v1/airelief/agreement/public',

  // 测试模块
  HEALTH_CHECK: '/api/v1/base/health',
  TEST_CONNECTION: '/api/v1/base/test'
} as const

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  PAYLOAD_TOO_LARGE: 413,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500
} as const

// 业务状态码
export const BUSINESS_CODE = {
  SUCCESS: 0,
  FAILED: 1,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500
} as const
