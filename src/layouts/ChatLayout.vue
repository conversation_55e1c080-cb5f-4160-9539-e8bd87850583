<script setup lang="ts">
import { ref } from 'vue'
import ChatHeader from '@/components/business/Chat/ChatHeader.vue'
import ChatInput from '@/components/business/Chat/ChatInput.vue'

interface Props {
  title?: string
  isTyping?: boolean
  isOnline?: boolean
  inputDisabled?: boolean
}

interface Emits {
  (e: 'menu-click'): void
  (e: 'send-text', text: string): void
  (e: 'send-audio', file: File, duration: number): void
  (e: 'recording-start'): void
  (e: 'recording-end'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '愈言空间',
  isTyping: false,
  isOnline: true,
  inputDisabled: false
})

const emit = defineEmits<Emits>()

const handleMenuClick = () => {
  emit('menu-click')
}

const handleSendText = (text: string) => {
  emit('send-text', text)
}

const handleSendAudio = (file: File, duration: number) => {
  emit('send-audio', file, duration)
}

const handleRecordingStart = () => {
  emit('recording-start')
}

const handleRecordingEnd = () => {
  emit('recording-end')
}
</script>

<template>
  <div class="chat-layout">
    <ChatHeader
      :title="title"
      :is-typing="isTyping"
      :is-online="isOnline"
      @menu-click="handleMenuClick"
    />

    <div class="chat-layout__content">
      <slot></slot>
    </div>

    <ChatInput
      :disabled="inputDisabled"
      @send-text="handleSendText"
      @send-audio="handleSendAudio"
      @recording-start="handleRecordingStart"
      @recording-end="handleRecordingEnd"
    />
  </div>
</template>

<style scoped>
.chat-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-layout__content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}
</style>