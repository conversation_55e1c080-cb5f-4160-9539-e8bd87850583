<!--
  DefaultLayout 组件 - 应用程序的默认布局组件
  
  功能说明：
  - 提供统一的页面布局结构
  - 包含可选的导航栏
  - 支持普通模式和全屏模式
  - 处理返回按钮和右侧按钮的点击事件
  
  使用示例：
  
  1. 基础用法：
  <default-layout title="页面标题">
    <div>页面内容</div>
  </default-layout>
  
  2. 带返回按钮：
  <default-layout title="页面标题" :show-back="true">
    <div>页面内容</div>
  </default-layout>
  
  3. 带右侧按钮：
  <default-layout title="页面标题" right-text="保存" @right-click="handleSave">
    <div>页面内容</div>
  </default-layout>
  
  4. 全屏模式（适用于需要居中显示内容的页面）：
  <default-layout title="页面标题" :show-back="true" :fullscreen="true">
    <img src="..." alt="图片" />
  </default-layout>
  
  5. 不显示导航栏：
  <default-layout :show-nav-bar="false">
    <div>无导航栏的页面内容</div>
  </default-layout>
-->
<script setup lang="ts">
interface Props {
  /** 是否显示导航栏，默认 true */
  showNavBar?: boolean
  /** 导航栏标题 */
  title?: string
  /** 是否显示返回按钮，默认 false */
  showBack?: boolean
  /** 右侧按钮文字，为空时不显示右侧按钮 */
  rightText?: string
  /** 是否启用全屏模式，适用于需要垂直水平居中显示内容的场景，默认 false */
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showNavBar: true,
  title: '',
  showBack: false,
  rightText: '',
  fullscreen: false
})

/** 事件定义 */
const emit = defineEmits<{
  /** 右侧按钮点击事件 */
  'right-click': []
}>()

/** 处理右侧按钮点击 */
const handleRightClick = () => {
  emit('right-click')
}
</script>

<template>
  <div class="default-layout gradient-background">
    <!-- 导航栏 -->
    <van-nav-bar
      v-if="showNavBar"
      :title="title"
      :left-arrow="showBack"
      @click-left="$router.back()"
    >
      <!-- 右侧按钮插槽 -->
      <template v-if="rightText" #right>
        <span class="nav-right-text" @click="handleRightClick">
          {{ rightText }}
        </span>
      </template>
    </van-nav-bar>

    <!-- 内容区域 -->
    <div 
      class="default-layout__content" 
      :class="{ 'default-layout__content--fullscreen': fullscreen }"
    >
      <!-- 页面内容插槽 -->
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
/* 整体布局容器 */
.default-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 普通内容区域 */
.default-layout__content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 全屏模式的内容区域 
   适用于需要垂直水平居中显示内容的场景，如图片展示、空状态页面等 */
.default-layout__content--fullscreen {
  position: fixed;
  top: 46px; /* 为导航栏留出空间，vant nav-bar 默认高度为 46px */
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0; /* 移除默认内边距 */
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  z-index: 1; /* 降低 z-index，确保在导航栏之下 */
}

/* 导航栏右侧文字按钮样式 */
.nav-right-text {
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
}

.nav-right-text:hover {
  opacity: 0.7;
}

/* 确保导航栏在全屏模式下仍可见和可点击 */
:deep(.van-nav-bar) {
  position: relative;
  z-index: 100; /* 确保导航栏在所有内容之上 */
}
</style>