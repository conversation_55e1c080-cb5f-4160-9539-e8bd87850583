import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import { useUserStore } from './stores/user'

// 函数式组件样式手动导入（由于 auto-import-resolver 对函数式组件支持不完善）
import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/notify/style'
import 'vant/es/image-preview/style'

// 导入全局样式
import './assets/styles/global.css'

const app = createApp(App)

// 使用 Pinia
const pinia = createPinia()
app.use(pinia)

// 使用 Vue Router
app.use(router)

// 初始化用户状态
const userStore = useUserStore()
userStore.initializeUser()

app.mount('#app')
