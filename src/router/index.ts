import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ROUTE_NAMES } from '@/constants'
import routes from './routes'

const router = createRouter({
  history: createWebHistory('/AIrelief/'),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  const { requiresAuth, requiresGuest, requiresNewUser } = to.meta

  console.log('路由守卫检查:', {
    to: to.name,
    from: from.name,
    requiresAuth,
    requiresGuest,
    requiresNewUser,
    isLoggedIn: userStore.isLoggedIn,
    isNewUser: userStore.isNewUser,
    hasToken: !!userStore.token
  })

  // 如果有token但没有用户信息，尝试获取用户信息
  if (userStore.token && !userStore.userInfo) {
    try {
      await userStore.fetchUserInfo()
    } catch (error) {
      console.error('获取用户信息失败，清除无效token:', error)
      // 如果获取用户信息失败，说明token无效，清除token
      userStore.clearToken()
    }
  }

  // 1. 需要登录的路由 - 未登录用户重定向到启动页
  if (requiresAuth && !userStore.isLoggedIn) {
    console.log('未登录用户访问需要认证的路由，重定向到启动页')
    next({ name: ROUTE_NAMES.LAUNCH })
    return
  }

  // 2. 仅限游客的路由（已登录用户访问登录页等）
  if (requiresGuest && userStore.isLoggedIn) {
    console.log('已登录用户访问游客页面，重定向到相应页面')
    // 如果是新用户，跳转到引导页
    if (userStore.isNewUser) {
      next({ name: ROUTE_NAMES.ONBOARDING_CHAT })
    } else {
      next({ name: ROUTE_NAMES.CHAT })
    }
    return
  }

  // 3. 已登录用户访问根路径时的智能重定向
  if (to.path === '/' && userStore.isLoggedIn) {
    console.log('已登录用户访问根路径，智能重定向')
    if (userStore.isNewUser) {
      next({ name: ROUTE_NAMES.ONBOARDING_CHAT })
    } else {
      next({ name: ROUTE_NAMES.CHAT })
    }
    return
  }

  // 4. 仅限新用户的路由 - 老用户重定向到聊天页
  if (requiresNewUser && userStore.isLoggedIn && !userStore.isNewUser) {
    console.log('老用户访问新用户专属页面，重定向到聊天页')
    next({ name: ROUTE_NAMES.CHAT })
    return
  }

  // 5. 防止启动页面无限重定向
  if (to.name === ROUTE_NAMES.LAUNCH && userStore.isLoggedIn) {
    console.log('已登录用户访问启动页，重定向到相应页面')
    if (userStore.isNewUser) {
      next({ name: ROUTE_NAMES.ONBOARDING_CHAT })
    } else {
      next({ name: ROUTE_NAMES.CHAT })
    }
    return
  }

  // 6. 允许通过
  console.log('路由检查通过，允许访问')
  next()
})

export default router