import type { RouteRecordRaw } from 'vue-router'
import { ROUTE_NAMES } from '@/constants'

const authRoutes: RouteRecordRaw[] = [
  {
    path: '/launch',
    name: ROUTE_NAMES.LAUNCH,
    component: () => import('@/views/Auth/LaunchScreen.vue'),
    meta: {
      requiresGuest: true,
      title: '愈言空间'
    }
  },
  {
    path: '/login',
    name: 'Login',
    redirect: { name: ROUTE_NAMES.LAUNCH }
  },
  {
    path: '/auth/gowechat',
    name: 'GoWechat',
    component: () => import('@/views/Auth/GoWechat.vue'),
    meta: {
      requiresGuest: true,
      title: '去微信使用'
    }
  },
  {
    path: '/auth/callback',
    name: 'AuthCallback',
    component: () => import('@/views/Auth/WechatCallback.vue'),
    meta: {
      requiresGuest: true,
      title: '授权登录'
    }
  },
  {
    path: '/auth/error',
    name: ROUTE_NAMES.AUTH_ERROR,
    component: () => import('@/views/Auth/AuthError.vue'),
    meta: {
      requiresGuest: true,
      title: '授权失败'
    }
  }
]

export default authRoutes