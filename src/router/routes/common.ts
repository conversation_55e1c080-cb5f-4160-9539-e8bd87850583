import type { RouteRecordRaw } from 'vue-router'
import { ROUTE_NAMES } from '@/constants'

const commonRoutes: RouteRecordRaw[] = [
  {
    path: '/service-agreement',
    name: ROUTE_NAMES.SERVICE_AGREEMENT,
    component: () => import('@/views/Common/ServiceAgreementPage.vue'),
    meta: {
      title: '服务协议'
    }
  },
  {
    path: '/privacy-policy',
    name: ROUTE_NAMES.PRIVACY_POLICY,
    component: () => import('@/views/Common/PrivacyPolicyPage.vue'),
    meta: {
      title: '隐私政策'
    }
  }
]

export default commonRoutes
