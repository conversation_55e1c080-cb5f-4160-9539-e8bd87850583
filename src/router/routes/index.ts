import type { RouteRecordRaw } from 'vue-router'
import authRoutes from './auth'
import commonRoutes from './common'
import onboardingRoutes from './onboarding'
import chatRoutes from './chat'
import profileRoutes from './profile'
import paymentRoutes from './payment'
import { ROUTE_NAMES } from '@/constants'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: { name: ROUTE_NAMES.LAUNCH }
  },
  ...authRoutes,
  ...onboardingRoutes,
  ...commonRoutes,
  ...chatRoutes,
  ...profileRoutes,
  ...paymentRoutes,

  // 测试页面索引 - 保留主测试页面
  {
    path: '/test',
    name: ROUTE_NAMES.TEST_INDEX,
    component: () => import('@/views/Test/TestIndex.vue'),
    meta: {
      title: '测试中心'
    }
  },

  // 语音录音测试页面
  {
    path: '/test/voice',
    name: ROUTE_NAMES.VOICE_TEST,
    component: () => import('@/views/Test/VoiceTestPage.vue'),
    meta: {
      title: '语音录音测试'
    }
  },

  // 流式语音识别测试页面
  {
    path: '/test/streaming-voice',
    name: ROUTE_NAMES.STREAMING_VOICE_TEST,
    component: () => import('@/views/Test/StreamingVoiceTestPage.vue'),
    meta: {
      title: '流式语音识别测试'
    }
  },
  
  {
    path: '/:pathMatch(.*)*',
    name: ROUTE_NAMES.NOT_FOUND,
    component: () => import('@/views/Common/NotFoundPage.vue')
  }
]

export default routes
