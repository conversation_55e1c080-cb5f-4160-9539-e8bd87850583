import type { RouteRecordRaw } from 'vue-router'
import { ROUTE_NAMES } from '@/constants'

const onboardingRoutes: RouteRecordRaw[] = [
  {
    path: '/onboarding',
    name: ROUTE_NAMES.ONBOARDING_CHAT,
    component: () => import('@/views/Onboarding/OnboardingChatPage.vue'),
    meta: {
      requiresAuth: true,
      requiresNewUser: true, // 只允许新用户访问
      title: '引导聊天'
    }
  }
]

export default onboardingRoutes
