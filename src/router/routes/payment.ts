import type { RouteRecordRaw } from 'vue-router'

const paymentRoutes: RouteRecordRaw[] = [
  {
    path: '/payment',
    name: 'Payment',
    component: () => import('@/views/Payment/PaymentPage.vue'),
    meta: {
      requiresAuth: true,
      title: '充值'
    }
  },
  {
    path: '/payment/coupon',
    name: 'CouponRedeem',
    component: () => import('@/views/Payment/CouponRedeemPage.vue'),
    meta: {
      requiresAuth: true,
      title: '兑换码'
    }
  },
  {
    path: '/payment/result/:orderId',
    name: 'PaymentResult',
    component: () => import('@/views/Payment/PaymentResultPage.vue'),
    meta: {
      requiresAuth: true,
      title: '支付结果'
    }
  }
]

export default paymentRoutes