import type { RouteRecordRaw } from 'vue-router'

const profileRoutes: RouteRecordRaw[] = [
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile/ProfilePage.vue'),
    meta: {
      requiresAuth: true,
      title: '个人中心'
    }
  },
  {
    path: '/profile/edit',
    name: 'ProfileEdit',
    component: () => import('@/views/Profile/ProfileEditPage.vue'),
    meta: {
      requiresAuth: true,
      title: '编辑资料'
    }
  },
  {
    path: '/profile/orders',
    name: 'OrderHistory',
    component: () => import('@/views/Profile/OrderHistoryPage.vue'),
    meta: {
      requiresAuth: true,
      title: '订单记录'
    }
  },
  {
    path: '/profile/settings',
    name: 'Settings',
    component: () => import('@/views/Profile/SettingsPage.vue'),
    meta: {
      requiresAuth: true,
      title: '设置'
    }
  },
  {
    path: '/profile/account-bind',
    name: 'AccountBind',
    component: () => import('@/views/Profile/AccountBind.vue'),
    meta: {
      requiresAuth: true,
      title: '账号与绑定'
    }
  },
  {
    path: '/profile/bind-phone',
    name: 'BindPhone',
    component: () => import('@/views/Profile/BindPhone.vue'),
    meta: {
      requiresAuth: true,
      title: '绑定手机号'
    }
  },
  {
    path: '/profile/contact',
    name: 'ContactUs',
    component: () => import('@/views/Profile/ContactUsPage.vue'),
    meta: {
      requiresAuth: true,
      title: '联系我们'
    }
  }
]

export default profileRoutes