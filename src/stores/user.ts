import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { wechat<PERSON>ogin, logout, getUserInfo, getAgreementStatus, updateAgreementStatus } from '@/apis/auth'
import type { UserInfo, AgreementStatus } from '@/types/api'
import { STORAGE_KEYS, APP_CONFIG } from '@/constants'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem(STORAGE_KEYS.TOKEN) || '')
  const userInfo = ref<UserInfo | null>(null)
  const loading = ref(false)
  const agreementStatus = ref<AgreementStatus>({
    serviceAgreement: false,
    privacyPolicy: false,
    version: APP_CONFIG.AGREEMENT_VERSION
  })

  // 定时刷新相关
  let refreshTimer: number | null = null

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isNewUser = computed(() => userInfo.value?.isNewUser || false)
  const remainingDuration = computed(() => userInfo.value?.duration || 0)
  const expiryDate = computed(() => userInfo.value?.expiryDate || '')
  const hasAgreedToTerms = computed(() =>
    agreementStatus.value.serviceAgreement && agreementStatus.value.privacyPolicy
  )

  // 格式化剩余时长
  const formattedDuration = computed(() => {
    if (!remainingDuration.value) return '0天0小时0分钟'

    const seconds = remainingDuration.value
    const days = Math.floor(seconds / (24 * 60 * 60))
    const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60))
    const minutes = Math.floor((seconds % (60 * 60)) / 60)

    return `${days}天${hours}小时${minutes}分钟`
  })

  // 方法
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem(STORAGE_KEYS.TOKEN, newToken)
  }

  const clearToken = () => {
    token.value = ''
    localStorage.removeItem(STORAGE_KEYS.TOKEN)
  }

  const setAgreementStatus = (status: AgreementStatus) => {
    agreementStatus.value = status
    localStorage.setItem(STORAGE_KEYS.AGREEMENT_STATUS, JSON.stringify(status))
  }

  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
  }

  const clearUserInfo = () => {
    userInfo.value = null
  }

  // 登录
  const loginAction = async (code: string) => {
    loading.value = true
    try {
      const result = await wechatLogin(code)
      setToken(result.token)
      setUserInfo(result.userInfo)
      return result
    } finally {
      loading.value = false
    }
  }

  // 测试用户登录（绕过微信认证）
  const testUserLogin = async (testUserId?: string) => {
    loading.value = true
    try {
      // 生成测试用户数据（限制长度为16字符）
      const timestamp = Date.now().toString().slice(-8) // 取时间戳后8位
      const mockUserId = testUserId || `test${timestamp}` // 最多12字符
      const mockToken = `test_token_${Date.now()}`

      // 创建测试用户信息
      const testUserInfo: UserInfo = {
        id: Date.now(),
        user_id: mockUserId,
        wechat_openid: `test_openid_${Date.now()}`,
        wechat_unionid: `test_unionid_${Date.now()}`,
        nickname: `测试用户_${mockUserId.slice(-6)}`,
        avatar: '',
        gender: 1,
        wechat_province: '测试省份',
        wechat_city: '测试城市',
        wechat_country: '中国',
        wechat_privilege: [],
        is_wechat_subscribed: false,
        duration: 7 * 24 * 60 * 60, // 7天会员时长
        totalDuration: 7 * 24 * 60 * 60,
        consumedDuration: 0,
        companionDays: 0,
        isNewUser: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // 设置token和用户信息
      setToken(mockToken)
      setUserInfo(testUserInfo)

      console.log('✅ 测试用户登录成功:', testUserInfo)

      return {
        token: mockToken,
        userInfo: testUserInfo
      }
    } catch (error) {
      console.error('❌ 测试用户登录失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 协议相关方法
  const fetchAgreementStatus = async () => {
    try {
      const status = await getAgreementStatus()
      setAgreementStatus(status)
      return status
    } catch (error) {
      console.error('获取协议状态失败:', error)
      return null
    }
  }

  const updateAgreementAction = async (status: AgreementStatus) => {
    try {
      await updateAgreementStatus(status)
      setAgreementStatus(status)
      return true
    } catch (error) {
      console.error('更新协议状态失败:', error)
      return false
    }
  }

  // 清除微信授权数据
  const clearWechatAuthData = () => {
    // 清除微信相关的本地存储数据
    localStorage.removeItem('wechat_auth_state')
    localStorage.removeItem(STORAGE_KEYS.AGREEMENT_STATUS)
    localStorage.removeItem('membership_reminder_date')
    localStorage.removeItem('pending_order')
    
    // 重置协议状态
    agreementStatus.value = {
      serviceAgreement: false,
      privacyPolicy: false,
      version: APP_CONFIG.AGREEMENT_VERSION
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      if (token.value) {
        await logout()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      stopAutoRefresh() // 停止定时刷新
      clearToken()
      userInfo.value = null
      clearWechatAuthData() // 清除微信授权数据
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!token.value) return null

    loading.value = true
    try {
      const info = await getUserInfo()
      setUserInfo(info)

      // 如果是首次获取用户信息成功，启动定时刷新
      if (info && !refreshTimer) {
        startAutoRefresh()
      }

      return info
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，清除token
      clearToken()
      return null
    } finally {
      loading.value = false
    }
  }

  // 初始化用户状态（应用启动时调用）
  const initializeUser = async () => {
    if (token.value && !userInfo.value) {
      await fetchUserInfo()
    }
  }

  // 开始定时刷新用户信息（每30秒刷新一次）
  const startAutoRefresh = () => {
    if (refreshTimer) return

    refreshTimer = window.setInterval(async () => {
      if (token.value && userInfo.value) {
        try {
          await fetchUserInfo()
        } catch (error) {
          console.error('定时刷新用户信息失败:', error)
        }
      }
    }, 30000) // 30秒刷新一次
  }

  // 停止定时刷新
  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  return {
    // 状态
    token,
    userInfo,
    loading,
    agreementStatus,

    // 计算属性
    isLoggedIn,
    isNewUser,
    remainingDuration,
    expiryDate,
    formattedDuration,
    hasAgreedToTerms,

    // 方法
    setToken,
    clearToken,
    setUserInfo,
    setAgreementStatus,
    loginAction,
    testUserLogin,
    logoutAction,
    fetchUserInfo,
    fetchAgreementStatus,
    updateAgreementAction,
    initializeUser,
    clearUserInfo,
    clearWechatAuthData,
    startAutoRefresh,
    stopAutoRefresh
  }
})