import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { WebSocketChatClient } from '@/utils/websocketClient'
import { showToast } from 'vant'
import type { ChatMessage } from '@/types/api'
import { chatApi } from '@/apis/chat'

export const useWebSocketStore = defineStore('websocket', () => {
  // WebSocket客户端实例
  const wsClient = ref<WebSocketChatClient | null>(null)
  
  // 连接状态
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const connectionError = ref('')
  
  // 聊天消息
  const messages = ref<ChatMessage[]>([])

  // 输入状态
  const isTyping = ref(false)

  // 当前会话ID
  const currentSessionId = ref<string>('')

  // 消息缓存键
  const CACHE_KEY_PREFIX = 'chat_messages_'
  const CURRENT_SESSION_KEY = 'current_session_id'
  
  // 重连计数器
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5



  // 计算属性
  const canReconnect = computed(() => reconnectAttempts.value < maxReconnectAttempts)

  // 消息缓存相关方法
  const saveMessagesToCache = (sessionId: string, messageList: ChatMessage[]) => {
    try {
      const cacheKey = CACHE_KEY_PREFIX + sessionId
      localStorage.setItem(cacheKey, JSON.stringify(messageList))
      localStorage.setItem(CURRENT_SESSION_KEY, sessionId)
      console.log(`消息已缓存到本地: ${messageList.length}条消息`)
    } catch (error) {
      console.error('保存消息到缓存失败:', error)
    }
  }

  const loadMessagesFromCache = (sessionId: string): ChatMessage[] => {
    try {
      const cacheKey = CACHE_KEY_PREFIX + sessionId
      const cached = localStorage.getItem(cacheKey)
      if (cached) {
        const messageList = JSON.parse(cached) as ChatMessage[]
        console.log(`从缓存加载消息: ${messageList.length}条消息`)
        return messageList
      }
    } catch (error) {
      console.error('从缓存加载消息失败:', error)
    }
    return []
  }

  const clearMessageCache = (sessionId?: string) => {
    try {
      if (sessionId) {
        const cacheKey = CACHE_KEY_PREFIX + sessionId
        localStorage.removeItem(cacheKey)
      } else {
        // 清除所有消息缓存
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith(CACHE_KEY_PREFIX)) {
            localStorage.removeItem(key)
          }
        })
        localStorage.removeItem(CURRENT_SESSION_KEY)
      }
      console.log('消息缓存已清除')
    } catch (error) {
      console.error('清除消息缓存失败:', error)
    }
  }

  const getCurrentSessionId = (): string => {
    return localStorage.getItem(CURRENT_SESSION_KEY) || ''
  }

  // 加载历史消息
  const loadHistoryMessages = async (userId: string, limit: number = 10): Promise<ChatMessage[]> => {
    try {
      console.log(`加载用户 ${userId} 的历史消息，限制 ${limit} 条`)
      const response = await chatApi.getMessageHistory(userId, limit)

      console.log('API响应:', response)

      // 响应拦截器已经解包了数据，直接检查 messages 字段
      if (response && response.messages && Array.isArray(response.messages)) {
        const historyMessages: ChatMessage[] = response.messages.map((msg: any) => ({
          id: msg.id || msg.message_id,
          role: msg.role,
          content: msg.content,
          type: msg.message_type || msg.type,
          timestamp: new Date(msg.created_at || msg.timestamp).getTime(),
          audioDuration: msg.audio_duration,
          transcription: msg.transcription || msg.transcription_text,
          sessionId: msg.session_id,
          sessionEnded: msg.session_ended || false
        }))

        console.log(`成功加载 ${historyMessages.length} 条历史消息`)
        console.log('历史消息详情:', historyMessages.slice(0, 2)) // 只显示前2条避免日志过长
        return historyMessages
      } else {
        console.log('响应中没有找到消息数据，响应格式:', typeof response, Object.keys(response || {}))
      }
    } catch (error) {
      console.error('加载历史消息失败:', error)
    }
    return []
  }

  // 添加会话分割线
  const addSessionDivider = (sessionId: string, endTime?: number): ChatMessage => {
    return {
      id: `divider_${sessionId}_${Date.now()}`,
      role: 'system',
      content: '', // 不需要文字内容
      type: 'divider',
      timestamp: endTime || Date.now(),
      sessionId: sessionId,
      sessionEnded: true
    }
  }

  // 合并历史消息和当前消息
  const mergeHistoryWithCurrent = (historyMessages: ChatMessage[], currentMessages: ChatMessage[]): ChatMessage[] => {
    const merged: ChatMessage[] = []

    // 按会话分组历史消息
    const sessionGroups = new Map<string, ChatMessage[]>()
    historyMessages.forEach(msg => {
      const sessionId = msg.sessionId || 'unknown'
      if (!sessionGroups.has(sessionId)) {
        sessionGroups.set(sessionId, [])
      }
      sessionGroups.get(sessionId)!.push(msg)
    })

    // 添加历史会话（按时间排序）
    const sortedSessions = Array.from(sessionGroups.entries()).sort((a, b) => {
      const aTime = Math.min(...a[1].map(m => m.timestamp))
      const bTime = Math.min(...b[1].map(m => m.timestamp))
      return aTime - bTime
    })

    sortedSessions.forEach(([sessionId, sessionMessages]) => {
      // 添加会话消息
      merged.push(...sessionMessages.sort((a, b) => a.timestamp - b.timestamp))

      // 如果会话已结束，添加分割线
      const lastMessage = sessionMessages[sessionMessages.length - 1]
      if (lastMessage?.sessionEnded) {
        merged.push(addSessionDivider(sessionId, lastMessage.timestamp + 1000))
      }
    })

    // 添加当前会话消息
    if (currentMessages.length > 0) {
      // 如果有历史消息且当前会话不同，添加分割线
      if (merged.length > 0) {
        const currentSessionIdValue = currentMessages[0]?.sessionId || currentSessionId.value
        const lastHistorySessionId = historyMessages[historyMessages.length - 1]?.sessionId
        if (currentSessionIdValue !== lastHistorySessionId) {
          merged.push(addSessionDivider('new_session', currentMessages[0].timestamp - 1000))
        }
      }
      merged.push(...currentMessages)
    }

    return merged
  }
  
  // 初始化WebSocket连接
  const initWebSocket = async (userId: string) => {
    if (!userId) {
      console.error('用户ID不存在，无法建立WebSocket连接')
      return false
    }

    try {
      isConnecting.value = true
      connectionError.value = ''

      // 如果已有连接且用户ID相同，直接返回
      if (wsClient.value && wsClient.value.isConnected() && wsClient.value.userId === userId) {
        console.log('WebSocket已连接，无需重复连接')
        isConnecting.value = false
        return true
      }

      // 断开现有连接
      if (wsClient.value) {
        wsClient.value.disconnect()
      }

      wsClient.value = new WebSocketChatClient({
        url: '', // URL在客户端内部构建
        userId: userId
      })

      // 设置事件回调
      wsClient.value.onMessage((message) => {
        const newMessage: ChatMessage = {
          id: message.message_id,
          role: message.role,
          content: message.content,
          type: message.message_type,
          timestamp: new Date(message.timestamp).getTime(),
          audioDuration: message.audio_duration,
          transcription: message.transcription,
          sessionId: message.session_id || currentSessionId.value
        }

        messages.value.push(newMessage)

        // 实时保存到缓存
        if (currentSessionId.value) {
          saveMessagesToCache(currentSessionId.value, messages.value.filter(m => m.sessionId === currentSessionId.value))
        }
      })

      wsClient.value.onTyping((typing) => {
        isTyping.value = typing
      })

      wsClient.value.onConnected(async () => {
        isConnected.value = true
        isConnecting.value = false
        reconnectAttempts.value = 0 // 重置重连计数
        console.log('WebSocket连接成功')

        // 获取当前会话ID（从WebSocket连接中获取）
        const sessionId = wsClient.value?.sessionId || getCurrentSessionId()
        if (sessionId) {
          currentSessionId.value = sessionId
          console.log(`当前会话ID: ${sessionId}`)

          // 如果当前没有消息，才加载历史消息
          if (messages.value.length === 0) {
            console.log('当前无消息，加载历史消息')

            // 先加载缓存的当前会话消息
            const cachedMessages = loadMessagesFromCache(sessionId)

            // 加载历史消息
            const historyMessages = await loadHistoryMessages(userId, 10)

            // 合并历史消息和缓存消息
            const mergedMessages = mergeHistoryWithCurrent(historyMessages, cachedMessages)
            messages.value = mergedMessages

            console.log(`消息加载完成: 历史${historyMessages.length}条, 缓存${cachedMessages.length}条, 总计${mergedMessages.length}条`)
          } else {
            console.log(`当前已有 ${messages.value.length} 条消息，跳过历史消息加载`)
          }
        }

        showToast('连接成功')
      })

      wsClient.value.onDisconnected(() => {
        isConnected.value = false
        console.log('WebSocket连接断开，AI对话已结束')

        // 保存当前会话消息到缓存
        if (currentSessionId.value) {
          const currentSessionMessages = messages.value.filter(m => m.sessionId === currentSessionId.value)
          // 标记会话已结束
          currentSessionMessages.forEach(msg => {
            if (msg.sessionId === currentSessionId.value) {
              msg.sessionEnded = true
            }
          })
          saveMessagesToCache(currentSessionId.value, currentSessionMessages)

          // 添加分割线
          const divider = addSessionDivider(currentSessionId.value)
          messages.value.push(divider)
        }

        showToast('连接断开，对话已结束')
      })

      wsClient.value.onError((error) => {
        connectionError.value = error
        isConnecting.value = false
        console.error('WebSocket错误:', error)
        showToast(`连接错误: ${error}`)
      })

      wsClient.value.onVoiceMessage((data) => {
        console.log('收到语音消息确认:', data)

        // 创建语音消息对象
        const voiceMessage: ChatMessage = {
          id: data.message_id || `voice-${Date.now()}`,
          role: 'user',
          content: data.transcription || data.content || '',
          type: 'audio',
          timestamp: new Date(data.timestamp || Date.now()).getTime(),
          audioDuration: data.audioDuration || 0,
          transcription: data.transcription,
          audioUrl: data.audioUrl, // 重要：包含音频文件URL
          sessionId: currentSessionId.value
        }

        console.log('添加语音消息到store:', voiceMessage)
        messages.value.push(voiceMessage)

        // 实时保存到缓存
        if (currentSessionId.value) {
          saveMessagesToCache(currentSessionId.value, messages.value.filter(m => m.sessionId === currentSessionId.value))
        }
      })

      // 建立连接
      await wsClient.value.connect()
      return true

    } catch (error) {
      isConnecting.value = false
      connectionError.value = error instanceof Error ? error.message : '连接失败'
      console.error('WebSocket连接失败:', error)
      showToast('连接失败，请重试')
      return false
    }
  }

  // 重连WebSocket
  const reconnectWebSocket = async (userId: string) => {
    if (!canReconnect.value) {
      console.log('已达到最大重连次数，停止重连')
      showToast('连接失败，请刷新页面重试')
      return false
    }
    
    return await initWebSocket(userId)
  }

  // 手动重连（重置重连计数）
  const manualReconnect = async (userId: string) => {
    reconnectAttempts.value = 0
    return await initWebSocket(userId)
  }

  // 发送文本消息
  const sendTextMessage = (content: string) => {
    if (!wsClient.value?.isConnected()) {
      showToast('连接已断开，请重新连接')
      return false
    }
    
    wsClient.value.sendTextMessage(content)
    return true
  }

  // 发送语音消息
  const sendVoiceMessage = (audioData: string, duration: number, options?: any) => {
    if (!wsClient.value?.isConnected()) {
      showToast('连接已断开，请重新连接')
      return false
    }
    
    wsClient.value.sendVoiceMessage(audioData, duration, options)
    return true
  }

  // 开始流式识别
  const startStreamingRecognition = () => {
    if (!wsClient.value?.isConnected()) {
      showToast('连接已断开，请重新连接')
      return false
    }
    
    return wsClient.value.startStreamingRecognition()
  }

  // 发送流式音频数据
  const sendStreamingAudio = (audioData: string) => {
    if (!wsClient.value?.isConnected()) {
      return false
    }
    
    return wsClient.value.sendStreamingAudio(audioData)
  }

  // 结束流式识别
  const endStreamingRecognition = () => {
    if (!wsClient.value?.isConnected()) {
      return false
    }
    
    return wsClient.value.endStreamingRecognition()
  }

  // 断开连接
  const disconnect = () => {
    if (wsClient.value) {
      console.log('手动断开WebSocket连接')
      wsClient.value.disconnect()
      wsClient.value = null
    }
    isConnected.value = false
    isConnecting.value = false
    reconnectAttempts.value = 0
  }

  // 清空消息
  const clearMessages = () => {
    messages.value = []
    // 清除当前会话的缓存
    if (currentSessionId.value) {
      clearMessageCache(currentSessionId.value)
    }
  }

  // 清空所有消息和缓存
  const clearAllMessages = () => {
    messages.value = []
    clearMessageCache() // 清除所有缓存
    currentSessionId.value = ''
  }

  // 添加消息（供外部调用）
  const addMessage = (message: ChatMessage) => {
    // 设置当前会话ID
    if (currentSessionId.value) {
      message.sessionId = currentSessionId.value
    }

    messages.value.push(message)

    // 实时保存到缓存
    if (currentSessionId.value) {
      const currentSessionMessages = messages.value.filter(m => m.sessionId === currentSessionId.value)
      saveMessagesToCache(currentSessionId.value, currentSessionMessages)
    }
  }

  // 监听消息变化，自动保存到缓存
  watch(
    () => messages.value.length,
    () => {
      if (currentSessionId.value && messages.value.length > 0) {
        const currentSessionMessages = messages.value.filter(m =>
          m.sessionId === currentSessionId.value && m.type !== 'divider'
        )
        if (currentSessionMessages.length > 0) {
          saveMessagesToCache(currentSessionId.value, currentSessionMessages)
        }
      }
    },
    { deep: true }
  )

  return {
    // 状态
    wsClient,
    isConnected,
    isConnecting,
    connectionError,
    messages,
    isTyping,
    reconnectAttempts,
    canReconnect,
    currentSessionId,

    // 方法
    initWebSocket,
    reconnectWebSocket,
    manualReconnect,
    sendTextMessage,
    sendVoiceMessage,
    startStreamingRecognition,
    sendStreamingAudio,
    endStreamingRecognition,
    disconnect,
    clearMessages,
    clearAllMessages,
    addMessage,
    loadHistoryMessages,
    saveMessagesToCache,
    loadMessagesFromCache,
    getCurrentSessionId,
    mergeHistoryWithCurrent,
    addSessionDivider
  }
})
