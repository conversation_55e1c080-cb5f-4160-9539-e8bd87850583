/**
 * 音频录制工具
 * 支持录音、播放和格式转换
 */

import { appConfig } from '@/config/env'

export interface AudioRecorderConfig {
  sampleRate?: number
  channels?: number
  bitDepth?: number
  maxDuration?: number // 最大录音时长（秒）
  minDuration?: number // 最小录音时长（秒）
}

export interface RecordingResult {
  audioData: string // base64编码的音频数据
  duration: number   // 录音时长（秒）
  size: number      // 文件大小（字节）
  format: string    // 音频格式
}

export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null
  private audioStream: MediaStream | null = null
  private audioChunks: Blob[] = []
  private startTime: number = 0
  private config: Required<AudioRecorderConfig>
  private isRecording = false
  private isPaused = false

  // 用于实时PCM数据提取
  private audioContext: AudioContext | null = null
  private analyser: AnalyserNode | null = null
  private processor: ScriptProcessorNode | null = null
  private pcmBuffer: Float32Array[] = []

  // 事件回调
  private onStartCallback?: () => void
  private onStopCallback?: (result: RecordingResult) => void
  private onPauseCallback?: () => void
  private onResumeCallback?: () => void
  private onErrorCallback?: (error: string) => void
  private onDataAvailableCallback?: (duration: number, audioData?: string) => void
  private onRealTimeDataCallback?: (audioData: string) => void

  constructor(config: AudioRecorderConfig = {}) {
    this.config = {
      sampleRate: appConfig.voice.sampleRate,
      channels: appConfig.voice.channels,
      bitDepth: 16,
      maxDuration: appConfig.voice.maxDuration,
      minDuration: appConfig.voice.minDuration,
      ...config
    }
  }

  /**
   * 检查浏览器支持
   */
  static isSupported(): boolean {
    return !!(navigator.mediaDevices &&
              navigator.mediaDevices.getUserMedia &&
              window.MediaRecorder)
  }

  /**
   * 检查麦克风权限状态
   */
  static async checkPermissionStatus(): Promise<{
    status: 'granted' | 'denied' | 'prompt' | 'unknown'
    message: string
  }> {
    try {
      if (!navigator.permissions) {
        return {
          status: 'unknown',
          message: '浏览器不支持权限查询'
        }
      }

      const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName })

      switch (permission.state) {
        case 'granted':
          return {
            status: 'granted',
            message: '麦克风权限已授权'
          }
        case 'denied':
          return {
            status: 'denied',
            message: '麦克风权限被拒绝，请在浏览器设置中重新授权'
          }
        case 'prompt':
          return {
            status: 'prompt',
            message: '需要请求麦克风权限'
          }
        default:
          return {
            status: 'unknown',
            message: '未知的权限状态'
          }
      }
    } catch (error) {
      console.error('检查麦克风权限状态失败:', error)
      return {
        status: 'unknown',
        message: '无法检查权限状态'
      }
    }
  }

  /**
   * 请求麦克风权限
   */
  async requestPermission(): Promise<boolean> {
    try {
      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.error('浏览器不支持麦克风访问')
        return false
      }

      // 检查是否在安全上下文中（HTTPS或localhost）
      if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        console.error('麦克风访问需要HTTPS协议或localhost环境')
        return false
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      // 立即停止流，只是为了测试权限
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch (error: any) {
      console.error('麦克风权限请求失败:', error)

      // 详细的错误处理
      if (error.name === 'NotAllowedError') {
        console.error('用户拒绝了麦克风权限')
      } else if (error.name === 'NotFoundError') {
        console.error('未找到麦克风设备')
      } else if (error.name === 'NotSupportedError') {
        console.error('浏览器不支持麦克风访问')
      } else if (error.name === 'SecurityError') {
        console.error('安全错误：可能需要HTTPS协议')
      } else if (error.message && error.message.includes('shutdown')) {
        console.error('麦克风访问被系统阻止或设备正在关闭')
      }

      return false
    }
  }

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    if (this.isRecording) {
      throw new Error('正在录音中')
    }

    if (!AudioRecorder.isSupported()) {
      throw new Error('浏览器不支持录音功能')
    }

    try {
      // 获取音频流
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      // 创建MediaRecorder
      const options: MediaRecorderOptions = {
        mimeType: this.getSupportedMimeType(),
        audioBitsPerSecond: this.config.sampleRate * this.config.bitDepth * this.config.channels
      }

      this.mediaRecorder = new MediaRecorder(this.audioStream, options)
      this.setupMediaRecorderEvents()

      // 设置AudioContext用于实时PCM数据提取
      this.setupAudioContext()

      // 清空之前的录音数据
      this.audioChunks = []
      this.pcmBuffer = []
      this.startTime = Date.now()
      this.isRecording = true
      this.isPaused = false

      // 开始录音
      this.mediaRecorder.start(100) // 每100ms触发一次dataavailable事件

      // 设置最大录音时长限制
      setTimeout(() => {
        if (this.isRecording && !this.isPaused) {
          this.stopRecording()
        }
      }, this.config.maxDuration * 1000)

      this.onStartCallback?.()

    } catch (error: any) {
      console.error('开始录音失败:', error)
      this.cleanup()

      // 根据错误类型提供更具体的错误信息
      let errorMessage = '录音启动失败'

      if (error.name === 'NotAllowedError') {
        errorMessage = '麦克风权限被拒绝，请在浏览器设置中允许麦克风访问'
      } else if (error.name === 'NotFoundError') {
        errorMessage = '未找到麦克风设备，请检查设备连接'
      } else if (error.name === 'NotSupportedError') {
        errorMessage = '当前浏览器不支持录音功能'
      } else if (error.name === 'SecurityError') {
        errorMessage = '安全限制：录音功能需要在HTTPS环境下使用'
      } else if (error.message && error.message.includes('shutdown')) {
        errorMessage = '设备正在关闭或麦克风被其他应用占用'
      } else if (error.message && error.message.includes('permission')) {
        errorMessage = '麦克风权限不足，请重新授权'
      }

      throw new Error(errorMessage)
    }
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<RecordingResult> {
    if (!this.isRecording || !this.mediaRecorder) {
      throw new Error('未在录音中')
    }

    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('MediaRecorder不存在'))
        return
      }

      // 设置停止事件处理
      const handleStop = async () => {
        try {
          const duration = (Date.now() - this.startTime) / 1000
          
          // 检查最小录音时长
          if (duration < this.config.minDuration) {
            throw new Error(`录音时长不能少于${this.config.minDuration}秒`)
          }

          // 合并音频数据
          const audioBlob = new Blob(this.audioChunks, { 
            type: this.mediaRecorder!.mimeType 
          })

          // 转换为base64
          const audioData = await this.blobToBase64(audioBlob)

          const result: RecordingResult = {
            audioData,
            duration,
            size: audioBlob.size,
            format: this.getAudioFormat()
          }

          this.cleanup()
          this.onStopCallback?.(result)
          resolve(result)

        } catch (error) {
          this.cleanup()
          this.onErrorCallback?.(error instanceof Error ? error.message : '录音处理失败')
          reject(error)
        }
      }

      this.mediaRecorder.addEventListener('stop', handleStop, { once: true })
      this.mediaRecorder.stop()
    })
  }

  /**
   * 暂停录音
   */
  pauseRecording(): void {
    if (!this.isRecording || this.isPaused || !this.mediaRecorder) {
      return
    }

    this.mediaRecorder.pause()
    this.isPaused = true
    this.onPauseCallback?.()
  }

  /**
   * 恢复录音
   */
  resumeRecording(): void {
    if (!this.isRecording || !this.isPaused || !this.mediaRecorder) {
      return
    }

    this.mediaRecorder.resume()
    this.isPaused = false
    this.onResumeCallback?.()
  }

  /**
   * 取消录音
   */
  cancelRecording(): void {
    if (this.isRecording && this.mediaRecorder) {
      this.mediaRecorder.stop()
    }
    this.cleanup()
  }

  /**
   * 设置AudioContext用于实时PCM数据提取
   */
  private setupAudioContext(): void {
    try {
      // 创建AudioContext
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate
      })

      // 创建音频源
      const source = this.audioContext.createMediaStreamSource(this.audioStream!)

      // 创建ScriptProcessorNode用于处理音频数据
      // 使用1024的缓冲区大小，减少数据量
      this.processor = this.audioContext.createScriptProcessor(1024, 1, 1)

      // 处理音频数据
      this.processor.onaudioprocess = (event) => {
        if (!this.isRecording || this.isPaused) return

        const inputBuffer = event.inputBuffer
        const inputData = inputBuffer.getChannelData(0) // 获取第一个声道的数据

        // 将Float32Array转换为16位PCM
        const pcmData = this.float32ToPCM16(inputData)

        // 如果有实时数据回调，发送PCM数据
        if (this.onRealTimeDataCallback) {
          try {
            // 将PCM数据转换为base64
            const base64Data = this.arrayBufferToBase64(pcmData.buffer)
            this.onRealTimeDataCallback(base64Data)
          } catch (error) {
            console.warn('实时PCM数据转换失败:', error)
          }
        }

        // 保存PCM数据到缓冲区
        this.pcmBuffer.push(new Float32Array(inputData))
      }

      // 连接音频节点
      source.connect(this.processor)
      this.processor.connect(this.audioContext.destination)

      console.log('✅ AudioContext设置成功，开始实时PCM数据提取')

    } catch (error) {
      console.error('❌ AudioContext设置失败:', error)
      // 如果AudioContext设置失败，降级到原来的方式
    }
  }

  /**
   * 将Float32Array转换为16位PCM
   */
  private float32ToPCM16(float32Array: Float32Array): Int16Array {
    const pcm16 = new Int16Array(float32Array.length)
    for (let i = 0; i < float32Array.length; i++) {
      // 将-1到1的浮点数转换为-32768到32767的整数
      const sample = Math.max(-1, Math.min(1, float32Array[i]))
      pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF
    }
    return pcm16
  }

  /**
   * 将ArrayBuffer转换为base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * 设置MediaRecorder事件
   */
  private setupMediaRecorderEvents(): void {
    if (!this.mediaRecorder) return

    this.mediaRecorder.ondataavailable = async (event) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data)

        // 触发数据可用回调
        const duration = (Date.now() - this.startTime) / 1000

        // 注意：在使用AudioContext的情况下，不再通过MediaRecorder发送实时数据
        // 实时数据通过AudioContext的ScriptProcessorNode发送

        this.onDataAvailableCallback?.(duration)
      }
    }

    this.mediaRecorder.onerror = (event) => {
      console.error('MediaRecorder错误:', event)
      this.cleanup()
      this.onErrorCallback?.('录音过程中发生错误')
    }
  }

  /**
   * 获取支持的MIME类型
   */
  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ]

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type
      }
    }

    return 'audio/webm' // 默认类型
  }

  /**
   * 获取音频格式
   */
  private getAudioFormat(): string {
    if (!this.mediaRecorder) return 'webm'
    
    const mimeType = this.mediaRecorder.mimeType
    if (mimeType.includes('webm')) return 'webm'
    if (mimeType.includes('mp4')) return 'mp4'
    if (mimeType.includes('wav')) return 'wav'
    return 'webm'
  }

  /**
   * Blob转Base64
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // 移除data:audio/webm;base64,前缀
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.isRecording = false
    this.isPaused = false

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop())
      this.audioStream = null
    }

    if (this.mediaRecorder) {
      this.mediaRecorder = null
    }

    // 清理AudioContext资源
    if (this.processor) {
      this.processor.disconnect()
      this.processor = null
    }

    if (this.analyser) {
      this.analyser.disconnect()
      this.analyser = null
    }

    if (this.audioContext) {
      this.audioContext.close().catch(console.error)
      this.audioContext = null
    }

    this.audioChunks = []
    this.pcmBuffer = []
  }

  /**
   * 获取录音状态
   */
  getStatus(): {
    isRecording: boolean
    isPaused: boolean
    duration: number
  } {
    return {
      isRecording: this.isRecording,
      isPaused: this.isPaused,
      duration: this.isRecording ? (Date.now() - this.startTime) / 1000 : 0
    }
  }

  /**
   * 设置事件回调
   */
  onStart(callback: () => void): void {
    this.onStartCallback = callback
  }

  onStop(callback: (result: RecordingResult) => void): void {
    this.onStopCallback = callback
  }

  onPause(callback: () => void): void {
    this.onPauseCallback = callback
  }

  onResume(callback: () => void): void {
    this.onResumeCallback = callback
  }

  onError(callback: (error: string) => void): void {
    this.onErrorCallback = callback
  }

  onDataAvailable(callback: (duration: number) => void): void {
    this.onDataAvailableCallback = callback
  }

  onRealTimeData(callback: (audioData: string) => void): void {
    this.onRealTimeDataCallback = callback
  }

  /**
   * 播放音频（用于预览）
   */
  static playAudio(audioData: string, format: string = 'webm'): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const audio = new Audio(`data:audio/${format};base64,${audioData}`)
        audio.onended = () => resolve()
        audio.onerror = () => reject(new Error('音频播放失败'))
        audio.play()
      } catch (error) {
        reject(error)
      }
    })
  }
}
