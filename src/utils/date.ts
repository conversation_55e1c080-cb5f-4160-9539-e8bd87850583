/**
 * 日期时间格式化工具函数
 */

/**
 * 格式化日期时间为友好的显示格式
 * @param date - 日期字符串或Date对象
 * @param format - 格式类型: 'datetime' | 'date' | 'time' | 'relative'
 * @returns 格式化后的字符串
 */
export function formatDate(date: string | Date, format: 'datetime' | 'date' | 'time' | 'relative' = 'datetime'): string {
  if (!date) return ''
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) return ''
  
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  switch (format) {
    case 'datetime':
      return dateObj.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      
    case 'date':
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
      
    case 'time':
      return dateObj.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      
    case 'relative':
      if (diffDays === 0) {
        return '今天'
      } else if (diffDays === 1) {
        return '昨天'
      } else if (diffDays === -1) {
        return '明天'
      } else if (diffDays > 0) {
        return `${diffDays}天前`
      } else {
        return `${Math.abs(diffDays)}天后`
      }
      
    default:
      return dateObj.toLocaleString('zh-CN')
  }
}

/**
 * 格式化支付时间
 * @param date - 支付时间
 * @returns 格式化后的支付时间字符串
 */
export function formatPaymentTime(date: string | Date): string {
  return formatDate(date, 'datetime')
}

/**
 * 格式化到期时间
 * @param date - 到期时间
 * @returns 格式化后的到期时间字符串
 */
export function formatExpiryDate(date: string | Date): string {
  return formatDate(date, 'datetime')
}

/**
 * 格式化相对时间（多久前/多久后）
 * @param date - 日期时间
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: string | Date): string {
  return formatDate(date, 'relative')
}

/**
 * 计算日期间隔天数
 * @param startDate - 开始日期
 * @param endDate - 结束日期（默认为当前时间）
 * @returns 天数差值
 */
export function calculateDaysDiff(startDate: string | Date, endDate: string | Date = new Date()): number {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate
  
  const diffMs = end.getTime() - start.getTime()
  return Math.floor(diffMs / (1000 * 60 * 60 * 24))
}

/**
 * 判断日期是否已过期
 * @param date - 目标日期
 * @returns 是否已过期
 */
export function isExpired(date: string | Date): boolean {
  const targetDate = typeof date === 'string' ? new Date(date) : date
  return targetDate.getTime() < new Date().getTime()
}

/**
 * 格式化时长（秒）为友好的显示格式
 * @param seconds - 秒数
 * @param format - 格式类型: 'full' | 'short' | 'days'
 * @returns 格式化后的时长字符串
 */
export function formatDuration(seconds: number, format: 'full' | 'short' | 'days' = 'full'): string {
  if (!seconds || seconds <= 0) return '0秒'
  
  const days = Math.floor(seconds / (24 * 60 * 60))
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60))
  const minutes = Math.floor((seconds % (60 * 60)) / 60)
  const remainingSeconds = seconds % 60
  
  switch (format) {
    case 'full':
      const parts = []
      if (days > 0) parts.push(`${days}天`)
      if (hours > 0) parts.push(`${hours}小时`)
      if (minutes > 0) parts.push(`${minutes}分钟`)
      if (remainingSeconds > 0 && days === 0 && hours === 0) parts.push(`${remainingSeconds}秒`)
      return parts.length > 0 ? parts.join('') : '0秒'
      
    case 'short':
      if (days > 0) return `${days}天`
      if (hours > 0) return `${hours}小时`
      if (minutes > 0) return `${minutes}分钟`
      return `${remainingSeconds}秒`
      
    case 'days':
      return `${days}天`
      
    default:
      return `${seconds}秒`
  }
} 