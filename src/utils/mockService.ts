// ========================================
// 模拟API服务 - 仅用于开发环境
// ========================================
//
// 重要提示：此文件仅用于开发环境的API模拟
// 生产环境部署时：
// 1. 此文件不会被打包到生产版本中
// 2. 所有模拟API调用会被真实API替代
// 3. 确保真实API接口与此文件中定义的接口规范一致
//
import type { UserInfo, AgreementStatus, LoginResponse, PaymentRecord, PaymentPackage, OrderInfo } from '@/types/api'
import defaultAvatarImg from '@/assets/images/user-avtar.png'

// 模拟数据存储
class MockDataStore {
  private users = {
    newUser: {
      id: 'mock_user_1',
      user_id: 'A1B2C3D4E5',
      nickname: '农夫小鸭',
      avatar: defaultAvatarImg,
      gender: 0, // 0=未知
      birthday: '',
      isNewUser: true,
      duration: 0,
      totalDuration: 0,
      expiryDate: ''
    } as UserInfo,

    existingUser: {
      id: 'mock_user_2',
      user_id: 'F6G7H8I9J0',
      nickname: '测试用户',
      avatar: defaultAvatarImg,
      gender: 1, // 1=女性
      birthday: '1990-01-01',
      isNewUser: false,
      duration: 24 * 60 * 60, // 剩余1天
      totalDuration: 100 * 60 * 60, // 100小时
      expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 1天后到期
    } as UserInfo
  }

  private currentUser: UserInfo = this.users.existingUser

  // 模拟支付记录数据
  private paymentRecords: PaymentRecord[] = [
    {
      id: '13561351',
      orderId: '13561351',
      packageName: '陪伴月卡',
      duration: '1个月',
      amount: 12,
      status: 'paid',
      createTime: '2025/5/4',
      payTime: '2025/5/4 09:00:00'
    },
    {
      id: '13561350',
      orderId: '13561350',
      packageName: '陪伴季卡',
      duration: '3个月',
      amount: 12,
      status: 'paid',
      createTime: '2025/5/4',
      payTime: '2025/5/4 08:00:00'
    },
    {
      id: '13561349',
      orderId: '13561349',
      packageName: '陪伴年卡',
      duration: '1年',
      amount: 100,
      status: 'failed',
      createTime: '2025/5/4',
      payTime: ''
    },
    {
      id: '13561348',
      orderId: '13561348',
      packageName: '陪伴月卡',
      duration: '1个月',
      amount: 12,
      status: 'pending',
      createTime: '2025/5/4',
      payTime: ''
    }
  ]

  // 支付相关数据
  private paymentPackages: PaymentPackage[] = [
    {
      id: 1,
      name: '月卡',
      duration: '30天',
      duration_seconds: 30 * 24 * 3600,
      price: 12,
      original_price: 15,
      tag: '推荐',
      is_active: true,
      sort_order: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 2,
      name: '季度卡',
      duration: '90天',
      duration_seconds: 90 * 24 * 3600,
      price: 37,
      original_price: 45,
      tag: '热门',
      is_active: true,
      sort_order: 2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 3,
      name: '年卡',
      duration: '365天',
      duration_seconds: 365 * 24 * 3600,
      price: 100,
      original_price: 120,
      tag: '超值',
      is_active: true,
      sort_order: 3,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]

  // 获取当前用户
  getCurrentUser(): UserInfo {
    return this.currentUser
  }

  // 设置当前用户
  setCurrentUser(user: UserInfo): void {
    this.currentUser = user
  }

  // 更新用户信息
  updateUser(updates: Partial<UserInfo>): UserInfo {
    this.currentUser = {
      ...this.currentUser,
      ...updates
    }
    return this.currentUser
  }

  // 模拟登录
  login(code: string): LoginResponse {
    // 根据code决定返回新用户还是老用户
    const isNewUser = code.includes('new') || Math.random() > 0.5
    this.currentUser = isNewUser ? this.users.newUser : this.users.existingUser

    return {
      token: `mock_token_${Date.now()}`,
      userInfo: this.currentUser
    }
  }

  // 获取协议状态
  getAgreementStatus(): AgreementStatus {
    return {
      serviceAgreement: true,
      privacyPolicy: true,
      version: '1.0.0'
    }
  }

  // 获取充值套餐
  getPaymentPackages(): PaymentPackage[] {
    return this.paymentPackages
  }

  // 同步支付套餐
  syncPaymentPackages(packages: PaymentPackage[]): PaymentPackage[] {
    if (packages && packages.length > 0) {
      this.paymentPackages = packages
      console.log('🔧 Mock: 支付套餐已同步', this.paymentPackages)
    }
    return this.paymentPackages
  }

  // 验证兑换码
  validateCoupon(code: string) {
    if (code === 'WELCOME2025') {
      // 设置10分钟后过期，便于测试倒计时效果
      const expiryTime = new Date(Date.now() + 10 * 60 * 1000)
      return {
        valid: true,
        discount: 50,
        expiry: expiryTime.toISOString(),
        message: '新用户专享5折优惠'
      }
    }

    return {
      valid: false,
      message: '兑换码无效或已过期'
    }
  }

  // 兑换优惠券
  redeemCoupon(code: string) {
    if (code === 'WELCOME2025') {
      return {
        success: true,
        message: '兑换成功！已获得3天免费体验'
      }
    }

    return {
      success: false,
      message: '兑换失败，请检查兑换码'
    }
  }

  // 获取支付记录
  getPaymentRecords(page: number = 1, pageSize: number = 10) {
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const records = this.paymentRecords.slice(startIndex, endIndex)

    return {
      records,
      total: this.paymentRecords.length
    }
  }

  // 发送手机验证码
  sendSmsCode(phone: string) {
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      throw new Error('手机号格式不正确')
    }

    // 模拟发送成功
    console.log(`🔧 Mock: 发送验证码到 ${phone}`)
    return {
      message: '验证码发送成功'
    }
  }

  // 绑定手机号
  bindPhoneNumber(phone: string, code: string) {
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      throw new Error('手机号格式不正确')
    }

    // 验证验证码格式
    const codeRegex = /^\d{4}$/
    if (!codeRegex.test(code)) {
      throw new Error('验证码格式不正确')
    }

    // 模拟验证码验证（开发环境接受任何4位数字）
    if (code !== '1234' && code !== '0000') {
      // 在开发环境中，我们可以接受一些通用的测试验证码
      console.log(`🔧 Mock: 验证码 ${code} 验证通过（开发环境）`)
    }

    // 更新当前用户的手机号
    this.currentUser = {
      ...this.currentUser,
      phone: phone
    }

    console.log(`🔧 Mock: 手机号 ${phone} 绑定成功`)
    return {
      message: '手机号绑定成功'
    }
  }

  // 解绑手机号
  unbindPhoneNumber() {
    // 移除当前用户的手机号
    this.currentUser = {
      ...this.currentUser,
      phone: undefined
    }

    console.log('🔧 Mock: 手机号解绑成功')
    return {
      message: '手机号解绑成功'
    }
  }

  // 注销账号
  deactivateAccount() {
    console.log('🔧 Mock: 账号注销成功')
    return {
      message: '账号注销成功'
    }
  }

  // 创建订单并处理支付
  createOrder(packageId: string, couponCode?: string): OrderInfo {
    // 查找套餐
    const selectedPackage = this.paymentPackages.find(pkg => pkg.id === parseInt(packageId))
    if (!selectedPackage) {
      throw new Error('套餐不存在')
    }

    // 计算最终价格（考虑优惠券）
    let finalAmount = selectedPackage.price
    if (couponCode) {
      // 简单模拟优惠券逻辑
      const discount = couponCode.startsWith('TEST') ? 5 : 0
      finalAmount = Math.max(0, finalAmount - discount)
    }

    // 生成订单ID
    const orderId = `order_${Date.now()}_${Math.floor(Math.random() * 1000)}`

    // 更新用户信息 - 模拟支付成功后的状态变化
    const durationInSeconds = this.parseDuration(selectedPackage.duration)
    
    // 更新当前用户的时长
    if (this.currentUser) {
      const currentDuration = this.currentUser.duration || 0
      const currentTotalDuration = this.currentUser.totalDuration || 0
      
      // 计算新的到期时间
      const now = new Date()
      const expiryDate = new Date(now.getTime() + durationInSeconds * 1000)
      
      // 更新用户信息
      this.currentUser = {
        ...this.currentUser,
        duration: currentDuration + durationInSeconds,
        totalDuration: currentTotalDuration + durationInSeconds,
        expiryDate: expiryDate.toISOString()
      }
      
      console.log('🔧 Mock: 用户信息已更新（支付后）', this.currentUser)
    }

    // 模拟订单信息
    const orderInfo = {
      orderId,
      packageId: selectedPackage.id.toString(),
      packageName: selectedPackage.name,
      amount: finalAmount,
      payUrl: `https://mock-payment.example.com/pay?orderId=${orderId}&amount=${finalAmount}`
    }

    // 创建支付记录
    const paymentRecord = {
      id: `RECORD_${Date.now()}`,
      orderId,
      packageName: selectedPackage.name,
      duration: selectedPackage.duration,
      amount: finalAmount,
      status: 'paid' as const, // 模拟环境直接成功
      createTime: new Date().toISOString(),
      payTime: new Date().toISOString()
    }

    // 保存支付记录
    this.paymentRecords.unshift(paymentRecord)
    
    console.log('🔧 Mock: 订单已创建', orderInfo)
    console.log('🔧 Mock: 支付记录已创建', paymentRecord)
    
    return orderInfo
  }

  // 解析时长字符串为秒数
  private parseDuration(duration: string): number {
    const match = duration.match(/(\d+)([天日周月年])/)
    if (!match) return 0
    
    const value = parseInt(match[1])
    const unit = match[2]
    
    switch (unit) {
      case '天':
      case '日':
        return value * 24 * 60 * 60
      case '周':
        return value * 7 * 24 * 60 * 60
      case '月':
        return value * 30 * 24 * 60 * 60
      case '年':
        return value * 365 * 24 * 60 * 60
      default:
        return 0
    }
  }

  // 获取订单状态
  getOrderStatus(orderId: string) {
    const record = this.paymentRecords.find(r => r.orderId === orderId)
    
    if (!record) {
      throw new Error('订单不存在')
    }

    return {
      status: record.status,
      packageName: record.packageName,
      duration: record.duration,
      amount: record.amount,
      payTime: record.payTime
    }
  }

  // 同步用户信息 - 确保一致性
  syncUserInfo(userInfo: UserInfo): UserInfo {
    // 更新当前用户
    this.currentUser = {
      ...userInfo,
      // 确保关键字段存在
      id: userInfo.id || this.currentUser.id,
      user_id: userInfo.user_id || this.currentUser.user_id,
      isNewUser: typeof userInfo.isNewUser === 'boolean' ? userInfo.isNewUser : this.currentUser.isNewUser,
      duration: userInfo.duration !== undefined ? userInfo.duration : this.currentUser.duration,
      totalDuration: userInfo.totalDuration !== undefined ? userInfo.totalDuration : this.currentUser.totalDuration,
      expiryDate: userInfo.expiryDate || this.currentUser.expiryDate
    }

    console.log('🔧 Mock: 用户信息已同步', this.currentUser)
    return this.currentUser
  }

  // 重置用户信息到默认状态
  resetUserInfo(userType: 'new' | 'existing' = 'existing'): UserInfo {
    this.currentUser = userType === 'new' ? this.users.newUser : this.users.existingUser
    console.log(`🔧 Mock: 用户信息已重置为${userType === 'new' ? '新用户' : '老用户'}`)
    return this.currentUser
  }

  // 清空支付记录
  clearPaymentRecords(): void {
    this.paymentRecords = []
    console.log('🔧 Mock: 支付记录已清空')
  }

  // 重置支付记录为默认值
  resetPaymentRecords(): void {
    this.paymentRecords = [
      {
        id: '13561351',
        orderId: '13561351',
        packageName: '陪伴月卡',
        duration: '1个月',
        amount: 12,
        status: 'paid',
        createTime: '2025/5/4',
        payTime: '2025/5/4 09:00:00'
      },
      {
        id: '13561350',
        orderId: '13561350',
        packageName: '陪伴季卡',
        duration: '3个月',
        amount: 12,
        status: 'paid',
        createTime: '2025/5/4',
        payTime: '2025/5/4 08:00:00'
      },
      {
        id: '13561349',
        orderId: '13561349',
        packageName: '陪伴年卡',
        duration: '1年',
        amount: 100,
        status: 'failed',
        createTime: '2025/5/4',
        payTime: ''
      },
      {
        id: '13561348',
        orderId: '13561348',
        packageName: '陪伴月卡',
        duration: '1个月',
        amount: 12,
        status: 'pending',
        createTime: '2025/5/4',
        payTime: ''
      }
    ]
    console.log('🔧 Mock: 支付记录已重置为默认值')
  }

  // 简化的新用户福利领取
  claimNewUserTrial() {
    const user = this.getCurrentUser()

    // 验证是否为新用户
    if (!user.isNewUser) {
      throw new Error('仅限新用户领取')
    }

    // 计算3天试用期
    const now = new Date()
    const expiryTime = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000) // 3天后
    const trialDuration = 3 * 24 * 60 * 60 // 3天的秒数

    // 更新用户信息
    this.currentUser = {
      ...this.currentUser,
      duration: (this.currentUser.duration || 0) + trialDuration,
      totalDuration: (this.currentUser.totalDuration || 0) + trialDuration,
      expiryDate: expiryTime.toISOString(),
      isNewUser: false // 领取后不再是新用户
    }

    console.log('🔧 Mock: 新用户试用已激活', {
      trialDuration: '3天',
      expiryTime: expiryTime.toISOString(),
      user: this.currentUser
    })

    return {
      success: true,
      message: '恭喜您！成功获得3天免费试用',
      userInfo: this.currentUser
    }
  }

  // 获取公开协议内容
  getPublicAgreement(agreementType: string) {
    const agreements = {
      'user_agreement': {
        type: 'user_agreement',
        title: '用户服务协议',
        content: `# 用户服务协议

## 第一条 协议的接受
欢迎使用AI Relief服务！本协议是您与AI Relief之间关于使用AI Relief服务的法律协议。

## 第二条 服务内容
AI Relief为用户提供AI聊天陪伴服务，包括但不限于：
1. 智能对话功能
2. 情感陪伴服务
3. 个性化聊天体验

## 第三条 用户权利和义务
1. 用户有权享受AI Relief提供的各项服务
2. 用户应当遵守相关法律法规
3. 用户不得利用服务进行违法活动

## 第四条 隐私保护
我们承诺保护用户隐私，详见《隐私政策》。

## 第五条 协议修改
我们保留随时修改本协议的权利，修改后的协议将在平台上公布。

本协议自发布之日起生效。`,
        version: '1.0',
        effective_date: null,
        updated_at: new Date().toISOString()
      },
      'privacy_policy': {
        type: 'privacy_policy',
        title: '隐私政策',
        content: `# 隐私政策

## 信息收集
我们可能收集以下信息：
1. 用户提供的个人信息
2. 设备信息和使用数据
3. 聊天记录（仅用于改善服务）

## 信息使用
收集的信息将用于：
1. 提供和改善服务
2. 个性化用户体验
3. 安全防护

## 信息保护
我们采用行业标准的安全措施保护用户信息。

## 信息共享
除法律要求外，我们不会与第三方分享用户个人信息。

## 联系我们
如有隐私相关问题，请联系我们。`,
        version: '1.0',
        effective_date: null,
        updated_at: new Date().toISOString()
      }
    }

    const agreement = agreements[agreementType as keyof typeof agreements]
    if (!agreement) {
      throw new Error(`协议类型 ${agreementType} 不存在`)
    }

    return agreement
  }
}

// 创建单例实例
export const mockDataStore = new MockDataStore()

// 模拟API响应
export const mockApiResponse = async (url: string, method: string, data?: any): Promise<any> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 700))

  console.log(`🔧 Mock API: ${method} ${url}`, data)

  // 解析JSON数据
  let parsedData = data
  if (typeof data === 'string') {
    try {
      parsedData = JSON.parse(data)
    } catch (e) {
      // 如果不是JSON，保持原样
    }
  }

  switch (true) {
    case url.includes('/api/auth/wechat-login') && method === 'POST':
      return mockDataStore.login(parsedData?.code || 'mock_code')

    case url.includes('/api/user/profile') && method === 'GET':
      return mockDataStore.getCurrentUser()

    case url.includes('/api/user/profile') && method === 'PUT':
      return mockDataStore.updateUser(parsedData)

    case url.includes('/api/auth/agreement-status') && method === 'GET':
      return mockDataStore.getAgreementStatus()

    case url.includes('/api/auth/agreement-status') && method === 'POST':
      return parsedData

    case url.includes('/api/auth/logout') && method === 'POST':
      return { success: true }

    case url.includes('/api/user/avatar') && method === 'POST':
      // ========================================
      // 头像上传API模拟 - 生产环境替换点
      // ========================================
      //
      // 模拟头像上传成功，返回一个模拟的头像URL
      //
      // 生产环境API要求：
      // 请求格式: POST /api/user/avatar
      // Content-Type: multipart/form-data
      // Body: { avatar: File }
      // 响应格式: { avatarUrl: string }
      //
      // 安全要求：
      // - 验证JWT token
      // - 验证文件类型 (image/jpeg, image/png, image/gif, image/webp)
      // - 验证文件大小 (最大5MB)
      // - 病毒扫描
      // - 频率限制
      //
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
      const randomColor = colors[Math.floor(Math.random() * colors.length)]

      // 创建一个简单的SVG头像作为示例，使用URL编码避免btoa的中文问题
      const svgContent = `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <circle cx="50" cy="50" r="50" fill="${randomColor}"/>
        <text x="50" y="60" font-family="Arial" font-size="36" fill="white" text-anchor="middle">A</text>
      </svg>`

      const svgAvatar = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`

      return { avatarUrl: svgAvatar }

    case url.includes('/api/payment/packages') && method === 'GET':
      return mockDataStore.getPaymentPackages()

    case url.includes('/api/payment/validate-coupon') && method === 'POST':
      return mockDataStore.validateCoupon(parsedData?.code || '')

    case url.includes('/api/payment/redeem-coupon') && method === 'POST':
      return mockDataStore.redeemCoupon(parsedData?.code || '')

    case url.includes('/api/payment/records') && method === 'GET':
      // 解析查询参数
      const urlObj = new URL(url, 'http://localhost')
      const page = parseInt(urlObj.searchParams.get('page') || '1')
      const pageSize = parseInt(urlObj.searchParams.get('pageSize') || '10')
      return mockDataStore.getPaymentRecords(page, pageSize)

    case url.includes('/api/user/send-sms-code') && method === 'POST':
      return mockDataStore.sendSmsCode(parsedData?.phone || '')

    case url.includes('/api/user/bind-phone') && method === 'POST':
      return mockDataStore.bindPhoneNumber(parsedData?.phone || '', parsedData?.code || '')

    case url.includes('/api/user/unbind-phone') && method === 'POST':
      return mockDataStore.unbindPhoneNumber()

    case url.includes('/api/user/deactivate') && method === 'POST':
      return mockDataStore.deactivateAccount()

    case url.includes('/api/payment/create-order') && method === 'POST':
      return mockDataStore.createOrder(parsedData?.packageId || '', parsedData?.couponCode || '')

    case url.includes('/api/payment/order-status') && method === 'GET':
      // 从 URL 路径中提取 orderId
      const match = url.match(/\/api\/payment\/order-status\/(.+)/)
      const orderId = match ? match[1] : ''
      return mockDataStore.getOrderStatus(orderId)

    // 新用户试用激活
    case url.includes('/api/user/activate-trial') && method === 'POST':
      return mockDataStore.claimNewUserTrial()

    // 协议相关API
    case url.includes('/api/v1/airelief/agreement/public/') && method === 'GET':
      // 从URL中提取协议类型
      const agreementMatch = url.match(/\/api\/v1\/airelief\/agreement\/public\/(.+)/)
      const agreementType = agreementMatch ? agreementMatch[1] : ''
      return mockDataStore.getPublicAgreement(agreementType)

    default:
      throw new Error(`Mock API not implemented: ${method} ${url}`)
  }
}
