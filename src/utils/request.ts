import axios from 'axios'
import { useUserStore } from '@/stores/user'
import { mockApiResponse } from './mockService'
import { HTTP_STATUS, BUSINESS_CODE } from '@/constants/apiConstants'

// ========================================
// 模拟API配置 - 生产环境部署时需要注意
// ========================================
// 是否启用模拟模式的判断逻辑：
// 1. 如果明确设置 VITE_ENABLE_MOCK=true，启用Mock
// 2. 如果明确设置 VITE_ENABLE_MOCK=false，禁用Mock
// 3. 如果没有设置 VITE_ENABLE_MOCK 且没有 API_BASE_URL，启用Mock
const ENABLE_MOCK = import.meta.env.VITE_ENABLE_MOCK === 'true' ||
  (import.meta.env.VITE_ENABLE_MOCK !== 'false' && !import.meta.env.VITE_API_BASE_URL)

// 调试信息 - 在所有环境中显示
console.log('🔧 Mock Service Configuration:', {
  DEV: import.meta.env.DEV,
  PROD: import.meta.env.PROD,
  VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
  VITE_ENABLE_MOCK: import.meta.env.VITE_ENABLE_MOCK,
  ENABLE_MOCK
})

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  async (config) => {
    console.log('🔧 Request interceptor triggered:', config.url, 'Mock enabled:', ENABLE_MOCK)

    // ========================================
    // 模拟API处理逻辑 - 支持开发和演示环境
    // ========================================
    // 如果启用模拟模式，直接返回模拟数据
    // 可通过 VITE_ENABLE_MOCK=true 在生产环境中启用
    if (ENABLE_MOCK) {
      try {
        console.log('🔧 Using mock API for:', config.url)
        const mockData = await mockApiResponse(
          config.url || '',
          config.method?.toUpperCase() || 'GET',
          config.data
        )

        // 创建一个模拟的响应对象
        const mockResponse = {
          data: mockData,
          status: HTTP_STATUS.OK,
          statusText: 'OK',
          headers: {},
          config,
          request: {}
        }

        // 抛出一个特殊的错误，在响应拦截器中处理
        const mockError = new Error('MOCK_RESPONSE')
        ;(mockError as any).mockResponse = mockResponse
        throw mockError
      } catch (error: any) {
        if (error.message === 'MOCK_RESPONSE') {
          throw error
        }
        // 如果模拟失败，继续正常请求
        console.warn('Mock failed, falling back to real API:', error)
      }
    }

    const userStore = useUserStore()

    // 如果有token，添加到请求头
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }

    return config
  },
  (error) => {
    // 处理模拟响应
    if (error.message === 'MOCK_RESPONSE' && error.mockResponse) {
      console.log('🔧 Request interceptor - returning mock response:', error.mockResponse.data)
      // 直接返回 Mock 数据，跳过响应拦截器的处理
      return Promise.resolve(error.mockResponse.data)
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    console.log('🔧 Response interceptor - response:', response)
    const res = response.data
    console.log('🔧 Response interceptor - res:', res)

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return res
    }

    // 如果接口返回的是包装格式，解包
    if (res && typeof res === 'object' && res.code !== undefined) {
      // 使用常量定义的成功状态码
      if (res.code === BUSINESS_CODE.SUCCESS || res.code === HTTP_STATUS.OK) {
        console.log('🔧 Response interceptor - returning res.data:', res.data)
        return res.data
      } else {
        // 处理业务错误
        const error = new Error(res.msg || res.message || '请求失败')
        error.name = 'BusinessError'
        return Promise.reject(error)
      }
    }

    // 直接返回数据
    console.log('🔧 Response interceptor - returning res directly:', res)
    return res
  },
  (error) => {
    // 处理模拟响应错误
    if (error.message === 'MOCK_RESPONSE' && error.mockResponse) {
      // 返回模拟响应的数据部分，而不是整个响应对象
      return Promise.resolve(error.mockResponse.data)
    }

    const userStore = useUserStore()

    // 处理401错误，清除token并跳转到登录页
    if (error.response && error.response.status === HTTP_STATUS.UNAUTHORIZED) {
      userStore.logoutAction()
      window.location.href = '/launch'
    }

    // 格式化错误信息
    let message = '网络错误，请稍后重试'
    if (error.response && error.response.data) {
      message = error.response.data.message || error.response.data.msg || message
    }

    error.message = message
    return Promise.reject(error)
  }
)

export default request