/**
 * 通用录音适配器
 * 根据环境自动选择微信录音或浏览器录音
 */

import { AudioRecorder, type RecordingResult } from './audioRecorder'
import { WechatRecorder, type WechatRecordingResult } from './wechatRecorder'
import { appConfig } from '@/config/env'

export interface UniversalRecordingResult {
  audioData?: string      // base64编码的音频数据（浏览器录音）
  localId?: string        // 微信本地媒体ID（微信录音）
  serverId?: string       // 微信服务器媒体ID（微信录音上传后）
  duration: number        // 录音时长（秒）
  size?: number          // 文件大小（字节）
  format: string         // 音频格式
  provider: 'browser' | 'wechat' // 录音提供者
  transcription?: string  // 语音识别结果
}

export interface UniversalRecorderConfig {
  maxDuration?: number
  minDuration?: number
  sampleRate?: number
  channels?: number
  autoUploadWechat?: boolean // 微信环境下是否自动上传到服务器
  forceBrowser?: boolean // 强制使用浏览器录音
}

export class UniversalRecorder {
  private config: Required<UniversalRecorderConfig>
  private recorder: AudioRecorder | WechatRecorder | null = null
  private provider: 'browser' | 'wechat' = 'browser'
  private isInitialized = false

  // 事件回调
  private onStartCallback?: () => void
  private onStopCallback?: (result: UniversalRecordingResult) => void
  private onErrorCallback?: (error: string) => void
  private onDataAvailableCallback?: (duration: number, audioData?: string) => void
  private onRealTimeDataCallback?: (audioData: string) => void

  constructor(config: UniversalRecorderConfig = {}) {
    this.config = {
      maxDuration: appConfig.voice.maxDuration,
      minDuration: appConfig.voice.minDuration,
      sampleRate: appConfig.voice.sampleRate,
      channels: appConfig.voice.channels,
      autoUploadWechat: true,
      forceBrowser: false,
      ...config
    }
  }

  /**
   * 检测并初始化最佳的录音方案
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    // 如果强制使用浏览器录音，直接使用浏览器录音
    if (this.config.forceBrowser) {
      console.log('🌐 强制使用浏览器录音模式')
      this.initBrowserRecorder()
    }
    // 优先使用微信录音（在微信环境中）
    else if (WechatRecorder.isWechatEnvironment()) {
      try {
        const wechatRecorder = new WechatRecorder({
          maxDuration: this.config.maxDuration,
          minDuration: this.config.minDuration,
          autoUpload: this.config.autoUploadWechat
        })

        const initialized = await wechatRecorder.initWechatSDK()
        if (initialized) {
          this.recorder = wechatRecorder
          this.provider = 'wechat'
          this.setupWechatEvents()
          console.log('✅ 微信录音初始化成功，使用微信录音')
        } else {
          console.log('❌ 微信录音初始化失败')
          throw new Error('微信录音初始化失败')
        }
      } catch (error) {
        console.warn('⚠️ 微信录音不可用，降级到浏览器录音:', error)
        this.initBrowserRecorder()
      }
    } else {
      // 非微信环境使用浏览器录音
      this.initBrowserRecorder()
    }

    this.isInitialized = true
  }

  /**
   * 初始化浏览器录音
   */
  private initBrowserRecorder(): void {
    this.recorder = new AudioRecorder({
      maxDuration: this.config.maxDuration,
      minDuration: this.config.minDuration,
      sampleRate: this.config.sampleRate,
      channels: this.config.channels
    })
    this.provider = 'browser'
    this.setupBrowserEvents()
    console.log('🌐 使用浏览器录音')
  }

  /**
   * 设置微信录音事件
   */
  private setupWechatEvents(): void {
    if (!(this.recorder instanceof WechatRecorder)) return

    this.recorder.onStart(() => {
      this.onStartCallback?.()
    })

    this.recorder.onStop(async (result: WechatRecordingResult) => {
      console.log('🔄 UniversalRecorder 收到微信录音停止事件:', result)

      try {
        let serverId: string | undefined

        // 如果配置了自动上传，则上传到微信服务器
        if (this.config.autoUploadWechat && this.recorder instanceof WechatRecorder) {
          try {
            console.log('📤 开始上传录音到微信服务器...')
            serverId = await this.recorder.uploadVoice(result.localId)
            console.log('✅ 录音上传成功，serverId:', serverId)
          } catch (error) {
            console.warn('❌ 微信录音上传失败:', error)
          }
        }

        const universalResult: UniversalRecordingResult = {
          localId: result.localId,
          serverId,
          duration: result.duration,
          size: result.size,
          format: result.format,
          provider: 'wechat'
        }

        console.log('🎯 调用 onStopCallback，结果:', universalResult)
        this.onStopCallback?.(universalResult)
      } catch (error) {
        console.error('❌ 微信录音处理失败:', error)
        this.onErrorCallback?.(`微信录音处理失败: ${error}`)
      }
    })

    // 微信录音没有onError回调，错误会在其他回调中处理

    this.recorder.onDataAvailable((duration: number) => {
      this.onDataAvailableCallback?.(duration)
    })

    // 注意：微信录音不支持实时音频数据流
    // 微信录音只能在录音结束后获取完整的录音文件
  }

  /**
   * 设置浏览器录音事件
   */
  private setupBrowserEvents(): void {
    if (!(this.recorder instanceof AudioRecorder)) return

    this.recorder.onStart(() => {
      this.onStartCallback?.()
    })

    this.recorder.onStop((result: RecordingResult) => {
      const universalResult: UniversalRecordingResult = {
        audioData: result.audioData,
        duration: result.duration,
        size: result.size,
        format: result.format,
        provider: 'browser'
      }

      this.onStopCallback?.(universalResult)
    })

    this.recorder.onError((error: string) => {
      this.onErrorCallback?.(error)
    })

    this.recorder.onDataAvailable((duration: number) => {
      this.onDataAvailableCallback?.(duration)
    })

    // 设置实时音频数据回调
    this.recorder.onRealTimeData((audioData: string) => {
      this.onRealTimeDataCallback?.(audioData)
    })
  }

  /**
   * 检查录音支持
   */
  static async checkSupport(): Promise<{
    browser: boolean
    wechat: boolean
    recommended: 'browser' | 'wechat' | 'none'
  }> {
    const browserSupport = AudioRecorder.isSupported()
    const wechatSupport = WechatRecorder.isWechatEnvironment() && WechatRecorder.isWechatJSSDKAvailable()

    let recommended: 'browser' | 'wechat' | 'none' = 'none'
    if (wechatSupport) {
      recommended = 'wechat'
    } else if (browserSupport) {
      recommended = 'browser'
    }

    return {
      browser: browserSupport,
      wechat: wechatSupport,
      recommended
    }
  }

  /**
   * 请求录音权限
   */
  async requestPermission(): Promise<boolean> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    if (!this.recorder) {
      return false
    }

    if (this.provider === 'wechat') {
      // 微信录音不需要额外的权限请求
      return true
    } else {
      // 浏览器录音需要请求麦克风权限
      return await (this.recorder as AudioRecorder).requestPermission()
    }
  }

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    if (!this.recorder) {
      throw new Error('录音器未初始化')
    }

    await this.recorder.startRecording()
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<UniversalRecordingResult> {
    if (!this.recorder) {
      throw new Error('录音器未初始化')
    }

    return new Promise((resolve, reject) => {
      const originalCallback = this.onStopCallback

      this.onStopCallback = (result: UniversalRecordingResult) => {
        // 恢复原始回调
        this.onStopCallback = originalCallback

        // 先调用原始回调（如果存在）
        if (originalCallback) {
          try {
            console.log('🔄 调用原始回调函数...')
            originalCallback(result)
          } catch (error) {
            console.error('❌ 原始回调执行失败:', error)
          }
        }

        // 然后resolve Promise
        resolve(result)
      }

      // 停止录音
      this.recorder!.stopRecording().catch(reject)
    })
  }

  /**
   * 取消录音
   */
  cancelRecording(): void {
    if (this.recorder) {
      try {
        this.recorder.cancelRecording()
      } catch (error) {
        console.log('取消录音时出现错误（可忽略）:', error)
      }
    }
  }

  /**
   * 播放录音（仅微信环境）
   */
  playVoice(localId: string): void {
    if (this.provider === 'wechat' && this.recorder instanceof WechatRecorder) {
      this.recorder.playVoice(localId)
    } else {
      console.warn('播放功能仅在微信环境中可用')
    }
  }

  /**
   * 停止播放（仅微信环境）
   */
  stopVoice(localId: string): void {
    if (this.provider === 'wechat' && this.recorder instanceof WechatRecorder) {
      this.recorder.stopVoice(localId)
    }
  }

  /**
   * 语音识别（仅微信环境）
   */
  async translateVoice(localId: string): Promise<string> {
    if (this.provider === 'wechat' && this.recorder instanceof WechatRecorder) {
      return await this.recorder.translateVoice(localId)
    } else {
      throw new Error('语音识别功能仅在微信环境中可用')
    }
  }

  /**
   * 获取录音状态
   */
  getStatus(): {
    isRecording: boolean
    duration: number
    provider: 'browser' | 'wechat'
    initialized: boolean
  } {
    const recorderStatus = this.recorder?.getStatus() || {
      isRecording: false,
      duration: 0
    }

    return {
      isRecording: recorderStatus.isRecording,
      duration: recorderStatus.duration,
      provider: this.provider,
      initialized: this.isInitialized
    }
  }

  /**
   * 设置事件回调
   */
  onStart(callback: () => void): void {
    this.onStartCallback = callback
  }

  onStop(callback: (result: UniversalRecordingResult) => void): void {
    this.onStopCallback = callback
  }

  onError(callback: (error: string) => void): void {
    this.onErrorCallback = callback
  }

  onDataAvailable(callback: (duration: number, audioData?: string) => void): void {
    this.onDataAvailableCallback = callback
  }

  onRealTimeData(callback: (audioData: string) => void): void {
    this.onRealTimeDataCallback = callback
  }
}
