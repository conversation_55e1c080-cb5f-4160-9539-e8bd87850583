/**
 * 微信相关工具函数
 */

import type { WeChatPayParams, WeChatEnvironment } from '@/types/api'

// WeChat JS-SDK类型声明
declare global {
  interface Window {
    wx?: {
      config: (config: any) => void;
      ready: (callback: () => void) => void;
      error: (callback: (res: any) => void) => void;
      checkJsApi: (config: any) => void;
      chooseWXPay: (config: {
        timestamp: string; // 注意：chooseWXPay使用timestamp，不是timeStamp
        nonceStr: string;
        package: string;
        signType: string;
        paySign: string;
        success?: (res: any) => void;
        fail?: (res: any) => void;
        cancel?: (res: any) => void;
        complete?: (res: any) => void;
      }) => void;
      requestPayment?: (config: WeChatPayParams & {
        success?: (res: any) => void;
        fail?: (res: any) => void;
        cancel?: (res: any) => void;
        complete?: (res: any) => void;
      }) => void;
      // 录音相关API
      startRecord: (config?: {
        success?: (res: any) => void;
        fail?: (res: any) => void;
        complete?: (res: any) => void;
      }) => void;
      stopRecord: (config?: {
        success?: (res: any) => void;
        fail?: (res: any) => void;
        complete?: (res: any) => void;
      }) => void;
      onVoiceRecordEnd: (config: {
        complete?: (res: any) => void;
      }) => void;
      playVoice: (config: {
        localId: string;
        success?: (res: any) => void;
        fail?: (res: any) => void;
      }) => void;
      pauseVoice: (config: {
        localId: string;
        success?: (res: any) => void;
        fail?: (res: any) => void;
      }) => void;
      stopVoice: (config: {
        localId: string;
        success?: (res: any) => void;
        fail?: (res: any) => void;
      }) => void;
      onVoicePlayEnd: (config: {
        complete?: (res: any) => void;
      }) => void;
      uploadVoice: (config: {
        localId: string;
        isShowProgressTips?: number;
        success?: (res: any) => void;
        fail?: (res: any) => void;
      }) => void;
      downloadVoice: (config: {
        serverId: string;
        isShowProgressTips?: number;
        success?: (res: any) => void;
        fail?: (res: any) => void;
      }) => void;
      translateVoice: (config: {
        localId: string;
        isShowProgressTips?: number;
        success?: (res: any) => void;
        fail?: (res: any) => void;
      }) => void;
      // 检查API可用性
      checkJsApi: (config: {
        jsApiList: string[];
        success?: (res: any) => void;
        fail?: (res: any) => void;
      }) => void;
    };
    WeixinJSBridge?: {
      invoke: (method: string, params: any, callback: (res: any) => void) => void;
      on: (event: string, callback: () => void) => void;
    };
  }
}

/**
 * 检测是否在微信浏览器中
 */
export function isWechatBrowser(): boolean {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

/**
 * 检测是否在微信小程序中
 */
export function isWechatMiniProgram(): boolean {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('miniprogram')
}

/**
 * 获取微信环境信息
 */
export function getWeChatEnvironment(): WeChatEnvironment {
  const ua = navigator.userAgent.toLowerCase()
  const isWeChatBrowser = ua.includes('micromessenger')
  const isWeChatMiniProgram = ua.includes('miniprogram')
  
  return {
    isWeChatBrowser,
    isWeChatMiniProgram,
    version: extractWeChatVersion(ua)
  }
}

/**
 * 提取微信版本号
 */
function extractWeChatVersion(ua: string): string | undefined {
  const match = ua.match(/micromessenger\/(\d+\.\d+\.\d+)/)
  return match ? match[1] : undefined
}

/**
 * 调用微信支付
 */
export function callWechatPay(params: WeChatPayParams): Promise<any> {
  return new Promise((resolve, reject) => {
    if (!isWechatBrowser()) {
      reject(new Error('请在微信客户端中打开'))
      return
    }

    // 检查微信JS-SDK是否可用
    if (typeof window.wx !== 'undefined' && window.wx.requestPayment) {
      // 使用新版微信支付API
      window.wx.requestPayment({
        ...params,
        success: (res: any) => {
          console.log('微信支付成功:', res)
          resolve(res)
        },
        fail: (res: any) => {
          console.error('微信支付失败:', res)
          reject(new Error(res.err_msg || '支付失败'))
        },
        cancel: (res: any) => {
          console.log('用户取消支付:', res)
          reject(new Error('用户取消支付'))
        }
      })
    } else if (typeof window.wx !== 'undefined' && window.wx.chooseWXPay) {
      // 使用旧版微信支付API - 注意参数名的差异
      const chooseWXPayParams = {
        timestamp: params.timeStamp, // 注意：chooseWXPay使用timestamp，不是timeStamp
        nonceStr: params.nonceStr,
        package: params.package,
        signType: params.signType,
        paySign: params.paySign,
        success: (res: any) => {
          console.log('微信支付成功:', res)
          resolve(res)
        },
        fail: (res: any) => {
          console.error('微信支付失败:', res)
          reject(new Error(res.err_msg || '支付失败'))
        },
        cancel: (res: any) => {
          console.log('用户取消支付:', res)
          reject(new Error('用户取消支付'))
        }
      }

      console.log('🔧 调用chooseWXPay参数:', chooseWXPayParams)
      window.wx.chooseWXPay(chooseWXPayParams)
    } else if (typeof window.WeixinJSBridge !== 'undefined') {
      // 使用WeixinJSBridge调用支付
      window.WeixinJSBridge.invoke('getBrandWCPayRequest', {
        appId: params.appId,
        timeStamp: params.timeStamp,
        nonceStr: params.nonceStr,
        package: params.package,
        signType: params.signType,
        paySign: params.paySign
      }, (res: any) => {
        if (res.err_msg === 'get_brand_wcpay_request:ok') {
          console.log('微信支付成功:', res)
          resolve(res)
        } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
          console.log('用户取消支付:', res)
          reject(new Error('用户取消支付'))
        } else {
          console.error('微信支付失败:', res)
          reject(new Error(res.err_msg || '支付失败'))
        }
      })
    } else {
      reject(new Error('微信支付环境初始化失败，请稍后重试'))
    }
  })
}

/**
 * 等待微信JS-SDK准备就绪
 */
export function waitForWeChatReady(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!isWechatBrowser()) {
      reject(new Error('不在微信环境中'))
      return
    }

    if (typeof window.wx !== 'undefined') {
      window.wx.ready(() => {
        resolve()
      })
      
      window.wx.error((res: any) => {
        reject(new Error(`微信JS-SDK初始化失败: ${res.errMsg}`))
      })
    } else if (typeof window.WeixinJSBridge !== 'undefined') {
      // WeixinJSBridge已经准备好
      resolve()
    } else {
      // 等待微信环境加载
      let retryCount = 0
      const maxRetries = 50 // 最多等待5秒
      
      const checkWeChatEnv = () => {
        if (typeof window.wx !== 'undefined' || typeof window.WeixinJSBridge !== 'undefined') {
          resolve()
        } else if (retryCount < maxRetries) {
          retryCount++
          setTimeout(checkWeChatEnv, 100)
        } else {
          reject(new Error('微信环境加载超时'))
        }
      }
      
      checkWeChatEnv()
    }
  })
}

/**
 * 格式化微信支付错误信息
 */
export function formatWeChatPayError(errMsg: string): string {
  const errorMessages: Record<string, string> = {
    'get_brand_wcpay_request:cancel': '用户取消支付',
    'get_brand_wcpay_request:failed': '支付失败',
    'get_brand_wcpay_request:timeout': '支付超时',
    'chooseWXPay:cancel': '用户取消支付',
    'chooseWXPay:fail': '支付失败',
    'requestPayment:cancel': '用户取消支付',
    'requestPayment:fail': '支付失败'
  }
  
  return errorMessages[errMsg] || errMsg || '支付过程中出现错误'
}

/**
 * 初始化微信JS-SDK
 * @param url 当前页面URL
 */
export async function initWeChatJSSDK(url?: string): Promise<boolean> {
  return new Promise(async (resolve, reject) => {
    if (!isWechatBrowser()) {
      reject(new Error('请在微信客户端中打开'))
      return
    }

    try {
      // 获取当前页面URL
      const pageUrl = url || window.location.href.split('#')[0]
      
      // 从后端获取JS-SDK配置
      const { getWechatJSApiConfig } = await import('@/apis/auth')
      const config = await getWechatJSApiConfig(pageUrl)
      
             // 配置微信JS-SDK
       if (typeof window.wx !== 'undefined') {
         window.wx.config({
           debug: false, // 生产环境设为false
           appId: config.appId,
           timestamp: config.timestamp,
           nonceStr: config.nonceStr,
           signature: config.signature,
           jsApiList: config.jsApiList
         })

         // 监听配置成功
         window.wx.ready(() => {
           console.log('微信JS-SDK配置成功')
           resolve(true)
         })

         // 监听配置失败
         window.wx.error((res: any) => {
           console.error('微信JS-SDK配置失败:', res)
           reject(new Error(`微信JS-SDK配置失败: ${res.errMsg}`))
         })
       } else {
         reject(new Error('微信JS-SDK未加载'))
       }
    } catch (error: any) {
      console.error('初始化微信JS-SDK失败:', error)
      reject(new Error(`初始化微信JS-SDK失败: ${error.message}`))
    }
  })
} 