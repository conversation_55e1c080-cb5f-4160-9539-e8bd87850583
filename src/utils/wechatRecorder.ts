/**
 * 微信录音工具
 * 基于微信JS-SDK的录音功能
 */

import { appConfig } from '@/config/env'
import { isWechatBrowser, waitForWeChatReady } from './wechat'

export interface WechatRecordingResult {
  localId: string     // 微信本地媒体ID
  duration: number    // 录音时长（秒）
  size?: number       // 文件大小（字节）
  format: string      // 音频格式
}

export interface WechatRecorderConfig {
  maxDuration?: number // 最大录音时长（秒）
  minDuration?: number // 最小录音时长（秒）
  autoUpload?: boolean // 是否自动上传到微信服务器
}

export class WechatRecorder {
  private config: Required<WechatRecorderConfig>
  private isRecording = false
  private startTime = 0
  private currentLocalId = ''
  private recordTimer: number | null = null

  // 事件回调
  private onStartCallback?: () => void
  private onStopCallback?: (result: WechatRecordingResult) => void
  private onDataAvailableCallback?: (duration: number) => void

  constructor(config: WechatRecorderConfig = {}) {
    this.config = {
      maxDuration: appConfig.voice.maxDuration,
      minDuration: appConfig.voice.minDuration,
      autoUpload: false,
      ...config
    }
  }

  /**
   * 检查是否在微信环境中
   */
  static isWechatEnvironment(): boolean {
    return isWechatBrowser()
  }

  /**
   * 检查微信JS-SDK是否可用
   */
  static isWechatJSSDKAvailable(): boolean {
    return typeof window !== 'undefined' && !!window.wx
  }

  /**
   * 初始化微信JS-SDK
   */
  async initWechatSDK(): Promise<boolean> {
    try {
      if (!WechatRecorder.isWechatEnvironment()) {
        console.warn('不在微信环境中')
        return false
      }

      // 等待微信JS-SDK准备就绪
      await waitForWeChatReady()

      if (!window.wx) {
        console.error('微信JS-SDK未加载')
        return false
      }

      // 检查录音API是否可用
      const isRecordApiAvailable = await this.checkRecordApiAvailable()
      if (!isRecordApiAvailable) {
        console.error('微信录音API不可用，请检查JS-SDK配置')
        return false
      }

      // 设置录音事件
      this.setupWechatRecordEvents()
      console.log('微信录音SDK初始化成功')
      return true

    } catch (error) {
      console.error('微信录音SDK初始化失败:', error)
      return false
    }
  }

  /**
   * 检查录音API是否可用
   */
  private async checkRecordApiAvailable(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!window.wx) {
        resolve(false)
        return
      }

      // 检查录音相关API是否可用
      window.wx.checkJsApi({
        jsApiList: ['startRecord', 'stopRecord', 'onVoiceRecordEnd'],
        success: (res: any) => {
          console.log('录音API检查结果:', res)
          console.log('检查结果详情:', res.checkResult)

          // 检查核心录音API是否可用
          const startRecordAvailable = res.checkResult.startRecord === true
          const stopRecordAvailable = res.checkResult.stopRecord === true

          // onVoiceRecordEnd 可能不在检查结果中，但这不影响基本录音功能
          const coreApiAvailable = startRecordAvailable && stopRecordAvailable

          if (!coreApiAvailable) {
            console.warn('核心录音API不可用:', res.checkResult)
            resolve(false)
          } else {
            console.log('录音API检查通过')
            resolve(true)
          }
        },
        fail: (res: any) => {
          console.error('录音API检查失败:', res)
          resolve(false)
        }
      })
    })
  }

  /**
   * 设置微信录音事件
   */
  private setupWechatRecordEvents(): void {
    if (!window.wx) return

    // 录音结束事件
    window.wx.onVoiceRecordEnd({
      complete: (res: any) => {
        this.handleRecordEnd(res.localId)
      }
    })
  }

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    if (this.isRecording) {
      throw new Error('正在录音中')
    }

    if (!window.wx) {
      throw new Error('微信JS-SDK不可用')
    }

    return new Promise((resolve, reject) => {
      try {
        this.isRecording = true
        this.startTime = Date.now()
        this.currentLocalId = ''

        // 开始录音，添加成功和失败回调
        window.wx!.startRecord({
          success: () => {
            console.log('微信录音启动成功')

            // 设置最大录音时长限制
            this.recordTimer = window.setTimeout(() => {
              if (this.isRecording) {
                this.stopRecording()
              }
            }, this.config.maxDuration * 1000)

            // 开始计时回调
            const timer = setInterval(() => {
              if (!this.isRecording) {
                clearInterval(timer)
                return
              }
              const duration = (Date.now() - this.startTime) / 1000
              this.onDataAvailableCallback?.(duration)
            }, 100)

            this.onStartCallback?.()
            resolve()
          },
          fail: (res: any) => {
            this.cleanup()
            console.error('微信录音启动失败:', res)

            let errorMessage = '录音启动失败'
            if (res.errMsg) {
              if (res.errMsg.includes('permission')) {
                errorMessage = '录音权限被拒绝，请在微信中允许录音权限'
              } else if (res.errMsg.includes('offline verifying')) {
                errorMessage = '录音权限验证中，请稍后重试或重新进入页面'
              } else {
                errorMessage = `录音启动失败: ${res.errMsg}`
              }
            }

            reject(new Error(errorMessage))
          }
        })

      } catch (error) {
        this.cleanup()
        reject(new Error('微信录音启动异常'))
      }
    })
  }

  /**
   * 停止录音
   */
  async stopRecording(): Promise<WechatRecordingResult> {
    if (!this.isRecording) {
      throw new Error('未在录音中')
    }

    return new Promise((resolve, reject) => {
      try {
        // 停止录音
        window.wx!.stopRecord({
          success: (res: any) => {
            this.handleRecordEnd(res.localId)
            
            const duration = (Date.now() - this.startTime) / 1000
            
            // 检查最小录音时长
            if (duration < this.config.minDuration) {
              reject(new Error(`录音时长不能少于${this.config.minDuration}秒`))
              return
            }

            const result: WechatRecordingResult = {
              localId: res.localId,
              duration,
              format: 'amr', // 微信录音格式为AMR
            }

            this.cleanup()
            this.onStopCallback?.(result)
            resolve(result)
          },
          fail: (res: any) => {
            this.cleanup()
            console.error('微信录音停止失败:', res)

            let errorMessage = '录音停止失败'
            if (res && res.errMsg) {
              if (res.errMsg.includes('permission')) {
                errorMessage = '录音权限异常，停止失败'
              } else if (res.errMsg.includes('offline verifying')) {
                errorMessage = '录音权限验证中，停止失败'
              } else {
                errorMessage = `录音停止失败: ${res.errMsg}`
              }
            }

            reject(new Error(errorMessage))
          }
        })
      } catch (error) {
        this.cleanup()
        reject(new Error('停止录音异常'))
      }
    })
  }

  /**
   * 处理录音结束
   */
  private handleRecordEnd(localId: string): void {
    this.currentLocalId = localId
    console.log('录音结束，localId:', localId)
  }

  /**
   * 上传录音到微信服务器
   */
  async uploadVoice(localId: string): Promise<string> {
    if (!window.wx) {
      throw new Error('微信JS-SDK不可用')
    }

    return new Promise((resolve, reject) => {
      window.wx!.uploadVoice({
        localId: localId,
        isShowProgressTips: 1, // 显示进度提示
        success: (res: any) => {
          console.log('录音上传成功，serverId:', res.serverId)
          resolve(res.serverId)
        },
        fail: () => {
          console.error('录音上传失败')
          reject(new Error('录音上传失败'))
        }
      })
    })
  }

  /**
   * 播放录音
   */
  playVoice(localId: string): void {
    if (!window.wx) return

    window.wx.playVoice({
      localId: localId
    })
  }

  /**
   * 暂停播放录音
   */
  pauseVoice(localId: string): void {
    if (!window.wx) return

    window.wx.pauseVoice({
      localId: localId
    })
  }

  /**
   * 停止播放录音
   */
  stopVoice(localId: string): void {
    if (!window.wx) return

    window.wx.stopVoice({
      localId: localId
    })
  }

  /**
   * 下载录音
   */
  async downloadVoice(serverId: string): Promise<string> {
    if (!window.wx) {
      throw new Error('微信JS-SDK不可用')
    }

    return new Promise((resolve, reject) => {
      window.wx!.downloadVoice({
        serverId: serverId,
        isShowProgressTips: 1,
        success: (res: any) => {
          console.log('录音下载成功，localId:', res.localId)
          resolve(res.localId)
        },
        fail: () => {
          console.error('录音下载失败')
          reject(new Error('录音下载失败'))
        }
      })
    })
  }

  /**
   * 语音识别
   */
  async translateVoice(localId: string): Promise<string> {
    if (!window.wx) {
      throw new Error('微信JS-SDK不可用')
    }

    return new Promise((resolve, reject) => {
      window.wx!.translateVoice({
        localId: localId,
        isShowProgressTips: 1,
        success: (res: any) => {
          console.log('语音识别成功:', res.translateResult)
          resolve(res.translateResult)
        },
        fail: (res: any) => {
          console.error('语音识别失败:', res)
          reject(new Error('语音识别失败'))
        }
      })
    })
  }



  /**
   * 取消录音
   */
  cancelRecording(): void {
    // 只有在真正录音时才调用微信API
    if (this.isRecording && window.wx) {
      try {
        // 微信没有直接的取消录音API，只能停止录音
        window.wx.stopRecord({
          success: () => {
            console.log('录音已取消')
          },
          fail: (error: any) => {
            // 忽略取消录音时的错误，因为可能录音并未开始或已经结束
            console.log('取消录音时出现错误（可忽略）:', error)
          }
        })
      } catch (error) {
        console.log('调用wx.stopRecord时出现异常（可忽略）:', error)
      }
    }
    this.cleanup()
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    this.isRecording = false
    this.currentLocalId = ''
    
    if (this.recordTimer) {
      clearTimeout(this.recordTimer)
      this.recordTimer = null
    }
  }

  /**
   * 获取录音状态
   */
  getStatus(): {
    isRecording: boolean
    duration: number
    localId: string
  } {
    return {
      isRecording: this.isRecording,
      duration: this.isRecording ? (Date.now() - this.startTime) / 1000 : 0,
      localId: this.currentLocalId
    }
  }

  /**
   * 设置事件回调
   */
  onStart(callback: () => void): void {
    this.onStartCallback = callback
  }

  onStop(callback: (result: WechatRecordingResult) => void): void {
    this.onStopCallback = callback
  }



  onDataAvailable(callback: (duration: number) => void): void {
    this.onDataAvailableCallback = callback
  }
}
