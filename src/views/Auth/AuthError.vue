<template>
  <div class="auth-error-container">
    <!-- 错误图标 -->
    <div class="error-icon-wrapper">
      <van-icon name="warning-o" class="error-icon" />
    </div>

    <!-- 错误信息 -->
    <div class="error-content">
      <h2 class="error-title">授权失败</h2>
      <p class="error-message">{{ errorMessage }}</p>
      
      <!-- 常见问题提示 -->
      <div class="tips-section">
        <p class="tips-title">可能的原因：</p>
        <ul class="tips-list">
          <li>授权链接已过期</li>
          <li>网络连接不稳定</li>
          <li>微信授权被取消</li>
        </ul>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <van-button
        type="primary"
        block
        round
        class="retry-btn"
        @click="handleRetry"
      >
        重新授权
      </van-button>
      
      <van-button
        type="default"
        block
        round
        class="back-btn"
        @click="handleGoBack"
      >
        返回首页
      </van-button>
    </div>

    <!-- 客服联系方式 -->
    <div class="contact-info">
      <p>如果问题依然存在，请联系客服</p>
      <!-- <van-button type="default" size="small" @click="handleContactService">
        联系客服
      </van-button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { ROUTE_NAMES } from '@/constants'

const router = useRouter()
const route = useRoute()

// 错误信息
const errorMessage = ref('授权失败，请重新尝试')

onMounted(() => {
  // 从URL参数获取错误信息
  const message = route.query.message as string
  if (message) {
    errorMessage.value = decodeURIComponent(message)
  }
})

// 重新授权
const handleRetry = () => {
  showToast('正在跳转到授权页面...')
  // 清除可能存在的缓存状态
  localStorage.removeItem('wechat_auth_state')
  localStorage.removeItem('token')
  
  // 跳转到登录页面
  router.replace({ name: ROUTE_NAMES.LAUNCH })
}

// 返回首页
const handleGoBack = () => {
  router.replace({ name: ROUTE_NAMES.LAUNCH })
}

// 联系客服
const handleContactService = () => {
  showToast('客服功能开发中...')
  // TODO: 实现客服联系功能
}
</script>

<style scoped>
.auth-error-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 32px;
  position: relative;
}

/* 错误图标 */
.error-icon-wrapper {
  margin-bottom: 32px;
}

.error-icon {
  font-size: 80px;
  color: #fff;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* 错误内容 */
.error-content {
  text-align: center;
  margin-bottom: 40px;
  background: rgba(255, 255, 255, 0.95);
  padding: 32px 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  max-width: 320px;
  width: 100%;
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.error-message {
  font-size: 16px;
  line-height: 1.5;
  color: #666;
  margin: 0 0 24px 0;
}

/* 提示信息 */
.tips-section {
  text-align: left;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.tips-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px 0;
}

.tips-list {
  margin: 0;
  padding-left: 16px;
  font-size: 13px;
  color: #666;
  line-height: 1.6;
}

.tips-list li {
  margin-bottom: 4px;
}

/* 操作按钮 */
.action-buttons {
  width: 100%;
  max-width: 280px;
  margin-bottom: 32px;
}

.retry-btn {
  background: #5F59FF;
  border-color: #5F59FF;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  box-shadow: 0 4px 16px rgba(95, 89, 255, 0.3);
}

.retry-btn:active {
  background: #4a44cc;
  border-color: #4a44cc;
}

.back-btn {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 0.9);
  color: #666;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.7);
}

/* 客服信息 */
.contact-info {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.contact-info p {
  margin: 0 0 12px 0;
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .auth-error-container {
    max-width: 414px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }

  /* 桌面端保持固定尺寸 */
  .error-icon {
    font-size: 80px !important;
  }

  .error-title {
    font-size: 24px !important;
  }

  .error-message {
    font-size: 16px !important;
  }

  .retry-btn,
  .back-btn {
    height: 48px !important;
    font-size: 16px !important;
  }
}

/* 响应式调整 */
@media (max-width: 375px) {
  .auth-error-container {
    padding: 32px 24px;
  }

  .error-content {
    padding: 24px 20px;
  }

  .error-icon {
    font-size: 64px;
  }

  .error-title {
    font-size: 20px;
  }
}
</style> 