<template>
  <div class="go-wechat-container gradient-background">
    <!-- Logo 和标语 -->
    <div class="branding">
      <img :src="logoSvg" alt="AIRelief Logo" class="app-logo" />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="guide-text">
        <p class="guide-title">为了给您更好的体验</p>
        <p class="guide-subtitle">请点击按钮前往微信使用</p>
      </div>

      <!-- 微信打开按钮 -->
      <van-button
        type="primary"
        block
        round
        class="wechat-open-btn"
        @click="handleOpenWechat"
      >
        <van-icon name="wechat" class="wechat-icon" />
        <span>去微信打开</span>
      </van-button>
    </div>

    <!-- 底部联系我们 -->
    <div class="footer-contact">
      <div class="contact-link" @click="handleContact">
        <van-icon name="wechat" class="contact-icon" />
        <span>联系我们</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant'
import logoSvg from '@/assets/logo.svg'

// 方法
const handleOpenWechat = () => {
  // 尝试打开微信
  try {
    // 如果是在微信浏览器中
    if (isWechatBrowser()) {
      showToast('请在微信中打开此链接')
      return
    }

    // 尝试通过URL Scheme打开微信
    const wechatScheme = 'weixin://'
    const link = document.createElement('a')
    link.href = wechatScheme
    link.click()

    // 备用方案：提示用户手动打开微信
    setTimeout(() => {
      showToast('请手动打开微信应用')
    }, 1000)
  } catch (error) {
    console.error('打开微信失败:', error)
    showToast('请手动打开微信应用')
  }
}

const handleContact = () => {
  showToast('请通过微信联系我们')
}

// 检测是否在微信浏览器中
const isWechatBrowser = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}
</script>

<style scoped>
.go-wechat-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 0 54px;
}

/* 桌面端样式：保持原始px单位，不被PostCSS转换 */
@media (min-width: 768px) {
  .go-wechat-container {
    max-width: 414px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }

  /* 桌面端背景 */
  body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  /* 桌面端文字保持固定尺寸 */
  .app-title {
    font-size: 48px !important;
  }

  .app-subtitle {
    font-size: 20px !important;
  }

  .guide-title {
    font-size: 20px !important;
  }

  .guide-subtitle {
    font-size: 16px !important;
  }

  .wechat-open-btn {
    height: 50px !important;
    font-size: 18px !important;
  }

  .contact-link {
    font-size: 16px !important;
  }
}

/* Logo 和标语 */
.branding {
  text-align: center;
  margin-top: 120px;
  z-index: 10;
}

.app-title {
  font-size: 48px;
  font-weight: 400;
  color: #5F59FF;
  margin: 0 0 8px 0;
  line-height: 1.2;
  font-family: 'Arial', sans-serif;
  letter-spacing: -0.5px;
}

.app-subtitle {
  font-size: 20px;
  font-weight: 300;
  color: #5F59FF;
  margin: 0;
  opacity: 0.8;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  z-index: 10;
}

.guide-text {
  text-align: center;
  margin-bottom: 40px;
}

.guide-title {
  font-size: 16px;
  font-weight: 400;
  color: #5F59FF;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.guide-subtitle {
  font-size: 16px;
  font-weight: 400;
  color: #5F59FF;
  margin: 0;
  line-height: 1.4;
}

.wechat-open-btn {
  background: #5F59FF;
  border-color: #5F59FF;
  border-radius: 32px;
  height: 50px;
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  max-width: 300px;
}

.wechat-open-btn:active {
  background: #4a44cc;
  border-color: #4a44cc;
}

.wechat-icon {
  font-size: 22px;
}

/* 底部联系我们 */
.footer-contact {
  margin-bottom: 40px;
  z-index: 10;
}

.contact-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #4CAF50;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.2s ease;
}

.contact-link:active {
  opacity: 0.7;
  background-color: rgba(76, 175, 80, 0.1);
}

.contact-icon {
  font-size: 18px;
}
</style>
