<template>
  <div class="welcome-container gradient-background">
    <!-- Logo 和标语 -->
    <div class="branding">
      <img :src="logoSvg" alt="AIRelief Logo" class="app-logo" />
    </div>

    <!-- 登录区域 -->
    <div class="login-section">
      <!-- <van-button
        type="default"
        size="small"
        class="test-agreement-btn"
        @click="showAgreementDialog = true"
      >
        测试协议对话框
      </van-button> -->

    <!-- 协议对话框 -->
    <van-dialog
      v-model:show="showAgreementDialog"
      title="欢迎使用愈言空间"
      show-cancel-button
      cancel-button-text="取消"
      confirm-button-text="确定"
      confirm-button-color="#5F59FF"
      :close-on-click-overlay="false"
      :close-on-popstate="false"
      @confirm="handleAgreementConfirm"
      @cancel="handleAgreementCancel"
      class="agreement-dialog"
    >
      <div class="agreement-dialog-content">
        <p class="agreement-text-main">
          为了更好保护您的权益，请您在使用前阅读
          <span @click="goToServiceAgreement" class="agreement-link">服务协议</span>
          和
          <span @click="goToPrivacyPolicy" class="agreement-link">隐私权限</span>
        </p>
      </div>
    </van-dialog>
      <!-- 微信登录按钮 -->
      <van-button
        type="primary"
        block
        round
        class="wechat-login-btn"
        :disabled="!agreementChecked"
        :loading="loading"
        @click="handleWechatLogin"
      >
        <van-icon name="wechat" class="wechat-icon" />
        <span>微信登录</span>
      </van-button>

      <!-- 服务协议 -->
      <div class="agreement-wrapper">
        <van-checkbox v-model="agreementChecked">
          <span class="agreement-text">
            我已阅读并同意
            <a @click.stop="goToServiceAgreement">服务协议</a>
            和
            <a @click.stop="goToPrivacyPolicy">隐私权限</a>
          </span>
        </van-checkbox>
      </div>
    </div>

    <!-- 底部装饰 -->
    <div class="bottom-decoration"></div>

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'

import { useUserStore } from '@/stores/user'
import { APP_CONFIG, ROUTE_NAMES } from '@/constants'
import logoSvg from '@/assets/logo.svg'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const agreementChecked = ref(false)
const loading = ref(false)
const showAgreementDialog = ref(false)

// 生命周期
onMounted(() => {
  // 检查是否需要显示协议弹窗
  checkAgreementStatus()
})

// 方法
const checkAgreementStatus = () => {
  // 如果是首次访问或协议版本更新，显示协议弹窗
  const savedAgreement = localStorage.getItem('agreementStatus')
  if (!savedAgreement) {
    // 首次访问，显示协议弹窗
    showAgreementDialog.value = true
    agreementChecked.value = false
  } else {
    try {
      const agreement = JSON.parse(savedAgreement)
      if (agreement.version !== APP_CONFIG.AGREEMENT_VERSION || !agreement.agreed) {
        // 协议版本更新或未同意，显示协议弹窗
        showAgreementDialog.value = true
        agreementChecked.value = false
      } else {
        // 已同意且版本匹配，自动勾选协议
        agreementChecked.value = true
        showAgreementDialog.value = false
      }
    } catch {
      // 解析失败，显示协议弹窗
      showAgreementDialog.value = true
      agreementChecked.value = false
    }
  }
}

const goToServiceAgreement = () => {
  // 关闭对话框后跳转
  showAgreementDialog.value = false
  router.push({ name: ROUTE_NAMES.SERVICE_AGREEMENT })
}

const goToPrivacyPolicy = () => {
  // 关闭对话框后跳转
  showAgreementDialog.value = false
  router.push({ name: ROUTE_NAMES.PRIVACY_POLICY })
}

const handleWechatLogin = async () => {
  if (!agreementChecked.value) {
    showToast('请先同意服务协议和隐私政策')
    return
  }

  loading.value = true
  try {
    // 检查是否为生产环境（有配置API地址）
    const isProduction = import.meta.env.VITE_API_BASE_URL

    if (isProduction) {
      // 生产环境：使用后端API获取微信授权URL
      showToast('正在获取授权链接...')

      const state = `state_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // 保存状态到本地存储，用于回调验证
      localStorage.setItem('wechat_auth_state', state)

      // 调用后端API获取微信授权URL（使用后端默认的snsapi_userinfo授权范围）
      const { getWechatAuthUrl } = await import('@/apis/auth')
      const authResult = await getWechatAuthUrl(undefined, state)

      showToast('正在跳转微信授权...')

      // 跳转到微信授权页面
      window.location.href = authResult.auth_url
    } else {
      // 开发环境：使用模拟登录逻辑
      showToast('正在登录...')

      // 生成模拟的微信授权码
      const mockCode = `mock_wechat_code_${Date.now()}_${Math.random() > 0.5 ? 'new' : 'existing'}`

      // 调用登录API（会被模拟服务拦截）
      const result = await userStore.loginAction(mockCode)

      console.log('登录结果:', result)

      if (result) {
        // showSuccessToast('登录成功')

        // 根据用户类型跳转
        if (result.userInfo && result.userInfo.isNewUser) {
          router.replace({ name: ROUTE_NAMES.ONBOARDING_CHAT })
        } else {
          router.replace({ name: ROUTE_NAMES.CHAT })
        }
      } else {
        throw new Error('登录失败：未返回有效结果')
      }
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    showToast(error.message || '登录失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleAgreementConfirm = () => {
  showAgreementDialog.value = false
  agreementChecked.value = true

  // 保存协议同意状态到本地存储
  const agreementStatus = {
    agreed: true,
    version: APP_CONFIG.AGREEMENT_VERSION,
    timestamp: Date.now()
  }
  localStorage.setItem('agreementStatus', JSON.stringify(agreementStatus))

  showToast('已同意服务协议')
}

const handleAgreementCancel = () => {
  showAgreementDialog.value = false
  agreementChecked.value = false
  showToast('请同意服务协议后继续使用')
}
</script>

<style scoped>
.welcome-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 桌面端样式：保持原始px单位，不被PostCSS转换 */
@media (min-width: 768px) {
  .welcome-container {
    max-width: 414px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }

  /* 桌面端背景 */
  body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  /* 桌面端Logo保持固定尺寸 */
  .app-logo {
    width: 189px !important;
  }

  /* 桌面端按钮保持固定尺寸 */
  .wechat-login-btn {
    height: 46px !important;
    font-size: 16px !important;
  }

  /* 桌面端文字保持固定尺寸 */
  .agreement-text {
    font-size: 14px !important;
  }

  /* 桌面端间距保持固定 */
  .branding {
    margin-top: 220px !important;
  }

  .login-section {
    bottom: 180px !important;
    padding: 0 54px !important;
  }

  .agreement-wrapper {
    margin-top: 20px !important;
  }
}

/* Logo 和标语 */
.branding {
  text-align: center;
  margin-top: 220px;
  z-index: 10;
}

.app-logo {
  width: 189px;
  height: auto;
  margin: 0 auto 20px auto;
  display: block;
}

/* 登录区域 */
.login-section {
  position: absolute;
  bottom: 180px;
  left: 0;
  right: 0;
  padding: 0 54px;
  z-index: 10;
}

.wechat-login-btn {
  background: #5F59FF;
  border-color: #5F59FF;
  border-radius: 32px;
  height: 46px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.wechat-login-btn:active {
  background: #4a44cc;
  border-color: #4a44cc;
}

.wechat-login-btn:disabled {
  background: #cccccc;
  border-color: #cccccc;
  color: white;
}

.wechat-icon {
  font-size: 20px;
}

/* 服务协议 */
.agreement-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.agreement-text {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
  color: #999999;
  user-select: none;
}

.agreement-text a {
  color: #666666;
  text-decoration: none;
}

.agreement-text a:active {
  opacity: 0.7;
}

/* 底部装饰 */
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 29px;
  background: linear-gradient(to bottom, transparent, #f5f5f5);
}

/* 协议对话框样式 */
.agreement-dialog {
  :deep(.van-dialog) {
    border-radius: 16px;
    max-width: 320px;
    margin: 0 auto;
    overflow: hidden;
  }

  :deep(.van-dialog__header) {
    padding: 24px 24px 16px;
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 1.4;
  }

  :deep(.van-dialog__content) {
    padding: 0 24px 24px;
  }

  :deep(.van-dialog__footer) {
    padding: 0 24px 24px;
    display: flex;
    gap: 12px;
  }

  :deep(.van-dialog__footer .van-button) {
    flex: 1;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
  }

  :deep(.van-dialog__footer .van-button--default) {
    background: #F5F5F5;
    border-color: #F5F5F5;
    color: #666666;
  }

  :deep(.van-dialog__footer .van-button--default:active) {
    background: #E8E8E8;
    border-color: #E8E8E8;
  }

  :deep(.van-dialog__footer .van-button--primary) {
    background: #5F59FF;
    border-color: #5F59FF;
    color: white;
  }

  :deep(.van-dialog__footer .van-button--primary:active) {
    background: #4a44cc;
    border-color: #4a44cc;
  }
}

.agreement-dialog-content {
  text-align: left;
  padding: 8px 24px;
  padding-bottom: 24px;
}

.agreement-text-main {
  margin: 0;
  font-size: 15px;
  line-height: 1.6;
  color: #666666;
}

.agreement-link {
  color: #5F59FF;
  text-decoration: none;
  cursor: pointer;
  margin: 0 2px;
}

.agreement-link:active {
  opacity: 0.7;
}
</style>
