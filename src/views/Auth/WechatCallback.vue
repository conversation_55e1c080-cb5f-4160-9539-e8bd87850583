<template>
  <div class="wechat-callback">
    <div class="loading-container">
      <!-- 登录成功时显示成功图标 -->
      <div v-if="isSuccess" class="success-container">
        <van-icon name="checked" size="40px" color="#4CAF50" />
        <div class="success-text">{{ loadingText }}</div>
      </div>
      <!-- 登录失败时显示失败图标 -->
      <div v-else-if="isError" class="error-container">
        <van-icon name="close" size="40px" color="#ee0a24" />
        <div class="error-text">{{ loadingText }}</div>
      </div>
      <!-- 登录中时显示loading -->
      <van-loading
        v-else
        size="24px"
        vertical
        :type="loadingType"
        :color="loadingColor"
      >
        {{ loadingText }}
      </van-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ROUTE_NAMES } from '@/constants'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式变量控制loading状态
const loadingText = ref('微信登录中...')
const loadingType = ref<'circular' | 'spinner'>('circular')
const loadingColor = ref('#5F59FF')

// 状态控制变量
const isSuccess = ref(false)
const isError = ref(false)

onMounted(async () => {
  try {
    // 获取URL参数
    const code = route.query.code as string
    const token = route.query.token as string
    const state = route.query.state as string

    console.log('回调参数:', { code: !!code, token: !!token, state })

    // 验证状态参数
    const savedState = localStorage.getItem('wechat_auth_state')
    if (!state || state !== savedState) {
      throw new Error('授权状态验证失败')
    }

    // 清除保存的状态
    localStorage.removeItem('wechat_auth_state')

    let result

    if (token) {
      // 方案1: 直接使用后端传递的token
      console.log('使用后端传递的token')
      userStore.setToken(token)

      // 获取用户信息
      result = await userStore.fetchUserInfo()
      if (!result) {
        throw new Error('获取用户信息失败')
      }

      // 构造登录结果格式
      result = {
        token,
        userInfo: result
      }
    } else if (code) {
      // 方案2: 使用授权码调用登录API
      console.log('使用授权码调用登录API')
      result = await userStore.loginAction(code)
    } else {
      throw new Error('未获取到授权码或token')
    }

    if (result) {
      // 更新状态为成功
      isSuccess.value = true
      loadingText.value = '登录成功'

      // 根据用户类型跳转
      if (result.userInfo && result.userInfo.isNewUser) {
        router.replace({ name: ROUTE_NAMES.ONBOARDING_CHAT })
      } else {
        router.replace({ name: ROUTE_NAMES.CHAT })
      }
    } else {
      throw new Error('登录失败：未返回有效结果')
    }
  } catch (error: any) {
    console.error('微信登录回调处理失败:', error)

    // 更新状态为失败
    isError.value = true
    loadingText.value = error.message || '登录失败，请重试'

    // 延迟一下让用户看到错误信息，然后返回登录页
    setTimeout(() => {
      router.replace({ name: ROUTE_NAMES.LAUNCH })
    }, 2000)
  }
})
</script>

<style scoped>
.wechat-callback {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
}

.loading-container {
  text-align: center;
  padding: 40px;
  background: transparent;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.success-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.success-text {
  font-size: 15px;
  font-weight: 500;
  color: #4CAF50;
  margin-top: 4px;
}

.error-text {
  font-size: 14px;
  font-weight: 500;
  color: #ee0a24;
  text-align: center;
  line-height: 1.4;
  margin-top: 4px;
  max-width: 200px;
}
</style> 