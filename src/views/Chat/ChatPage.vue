<template>
  <div class="chat-page-root">
    <div class="gradient-background">
      <ChatHeader
        title="阿尔伯特·艾利斯"
        subtitle="临床心理学家"
        :is-typing="isTyping"
        :is-online="true"
        @menu-click="showProfile = true"
      />

      <div class="chat-content">
        <!-- 时长不足提示条 -->
        <!-- <div v-if="isChatDisabled" class="duration-warning">
          <div class="warning-content">
            <div class="warning-icon">⏰</div>
            <div class="warning-text">
              <div class="warning-title">陪伴时光已用完</div>
              <div class="warning-subtitle">请续费后继续使用AI聊天功能</div>
            </div>
            <div class="warning-action" @click="handleExtendMembership">
              续费
            </div>
          </div>
        </div> -->

        <!-- 聊天消息列表 -->
        <div class="message-list" ref="messageListRef">
          <!-- 顶部提示信息 -->
          <div v-if="messages.length === 0" style="text-align: center; padding: 20px; color: #999; font-size: 14px;">
            暂无消息
          </div>
          <div v-else style="text-align: center; padding: 10px; color: #999; font-size: 12px;">
            仅显示最近 {{ messages.length }} 条消息
          </div>

          <MessageItem
            v-for="(message, index) in messages"
            :key="index"
            :message="message"
          />
        </div>
      </div>
    </div>

    <!-- 调试信息（临时） -->
    <!-- <div style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; z-index: 1000; font-size: 12px;">
      录音状态: {{ isRecording ? '录音中' : '未录音' }}<br>
      流式识别: {{ isStreamingActive ? '激活' : '未激活' }}<br>
      实时文本: {{ currentRecognitionText || '无' }}<br>
      最终文本: {{ finalRecognitionText || '无' }}
    </div> -->

    <!-- 添加实时语音识别显示区域 -->
    <div
      v-if="isRecording"
      class="voice-recognition-overlay"
      @touchend.prevent="stopRecording"
      @touchcancel.prevent="cancelRecording"
      @mouseup.prevent="stopRecording"
    >
      <div class="recognition-text">
        <div class="recording-indicator">
          <span class="recording-dot"></span>
          正在录音...{{ recordingDuration.toFixed(1) }}s
        </div>
        <div class="recognition-content">
          {{ currentRecognitionText || '正在聆听...' }}
        </div>
        <div class="recognition-hint">松开发送</div>
      </div>
    </div>

    <!-- 聊天输入框 -->
    <ChatInput
      v-model="inputText"
      :disabled="isChatDisabled"
      :placeholder="isChatDisabled ? disabledMessage : '聊聊你的情绪...'"
      :is-recording="isRecording"
      @send="handleSendMessage"
      @start-recording="startRecording"
      @stop-recording="stopRecording"
      @cancel-recording="cancelRecording"
    />
    
    <!-- 个人中心侧边栏 -->
    <ProfileSidebar
      v-model="showProfile"
      :user-info="userStore.userInfo"
      @extend-subscription="handleExtendSubscription"
      @contact-support="handleContactSupport"
      @open-settings="handleOpenSettings"
      @edit-profile="handleEditProfile"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog, showToast } from 'vant'
import ChatHeader from '@/components/business/Chat/ChatHeader.vue'
import ChatInput from '@/components/business/Chat/ChatInput.vue'
import MessageItem from '@/components/business/Chat/MessageItem.vue'
import ProfileSidebar from '@/components/business/Profile/ProfileSidebar.vue'
import { useUserStore } from '@/stores/user'
import { useWebSocketStore } from '@/stores/websocket'
import type { ChatMessage } from '@/types/api'
import { UniversalRecorder } from '@/utils/universalRecorder'
import { isWechatBrowser, initWeChatJSSDK } from '@/utils/wechat'

const router = useRouter()
const userStore = useUserStore()
const wsStore = useWebSocketStore()

// 语音相关
const audioRecorder = ref<UniversalRecorder | null>(null)

// 语音识别配置
const voiceConfig = ref<any>(null)
const voiceRecognitionMode = ref<string>('auto')

// 弹窗测试模式开关
const isTestMode = ref(false) // 改为false，使用真实WebSocket连接

const showProfile = ref(false)
const inputText = ref('')
const messageListRef = ref<HTMLElement>()
const isRecording = ref(false)
const recordingDuration = ref(0)

// 流式语音识别状态
const isStreamingActive = ref(false)
const currentRecognitionText = ref('')
const finalRecognitionText = ref('')

// 使用全局WebSocket store中的数据
const messages = computed(() => wsStore.messages)
const isTyping = computed(() => wsStore.isTyping)

// 计算会员状态
const membershipStatus = computed(() => {
  // 测试模式下直接返回测试数据
  if (isTestMode.value) {
    // 根据用户剩余时长判断会员状态
    const userInfo = userStore.userInfo
    if (!userInfo || !userInfo.duration) {
      return {
        type: 'expired',
        title: '会员到期提醒',
        emoji: '🌸',
        message: '呜～ 发现您的会员已经悄悄到期啦～\n真舍不得和您说再见呢！\n要不要考虑续续会员？',
        subMessage: '\n期待很快能再次相遇 ♡'
      }
    }

    // 剩余时长小于等于0，表示已到期
    if (userInfo.duration <= 0) {
      return {
        type: 'expired',
        title: '会员到期提醒',
        emoji: '🌸',
        message: '呜～ 发现您的会员已经悄悄到期啦～\n真舍不得和您说再见呢！\n要不要考虑续续会员？',
        subMessage: '\n期待很快能再次相遇 ♡'
      }
    }
    // 剩余时长小于等于1天（86400秒），表示即将到期
    else if (userInfo.duration <= 24 * 60 * 60) {
      return {
        type: 'expiring',
        title: '温馨提醒',
        emoji: '⏰',
        message: '您的陪伴时光还剩最后1天啦～\n要记得续费哦，这样我们的故事才不\n会被打断呢！',
        subMessage: ''
      }
    }
  }

  const userInfo = userStore.userInfo
  if (!userInfo || !userInfo.expiryDate) {
    return null
  }

  const now = new Date()
  const expiryDate = new Date(userInfo.expiryDate)
  const timeDiff = expiryDate.getTime() - now.getTime()
  const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))

  if (daysDiff <= 0) {
    // 会员已到期
    return {
      type: 'expired',
      title: '会员到期提醒',
      emoji: '🌸',
      message: '呜～ 发现您的会员已经悄悄到期啦～\n真舍不得和您说再见呢！\n要不要考虑续续会员？',
      subMessage: '期待很快能再次相遇 ♡'
    }
  } else if (daysDiff === 1) {
    // 会员剩1天
    return {
      type: 'expiring',
      title: '温馨提醒',
      emoji: '⏰',
      message: '您的陪伴时光还剩最后1天啦～\n要记得续费哦，这样我们的故事才不\n会被打断呢！',
      subMessage: ''
    }
  }

  return null
})

// 检查用户是否有剩余时长
const hasRemainingDuration = computed(() => {
  const userInfo = userStore.userInfo
  if (!userInfo) return false

  // 测试模式下使用duration字段
  if (isTestMode.value) {
    return userInfo.duration > 0
  }

  // 正式模式下使用expiryDate判断
  if (!userInfo.expiryDate) return false

  const now = new Date()
  const expiryDate = new Date(userInfo.expiryDate)
  return now < expiryDate
})

// 检查聊天功能是否被禁用
const isChatDisabled = computed(() => {
  return !hasRemainingDuration.value
})

// 获取禁用状态的提示信息
const disabledMessage = computed(() => {
  if (hasRemainingDuration.value) return ''

  return '您的会员已经悄悄到期啦，期待和您的再次相遇'
})

// 显示会员提醒弹窗
const showMembershipReminder = () => {
  const status = membershipStatus.value
  if (!status) return

  showConfirmDialog({
    title: `${status.title} ${status.emoji}`,
    message: `${status.message}${status.subMessage ? '\n' + status.subMessage : ''}`,
    confirmButtonText: '延长陪伴',
    cancelButtonText: '稍后再说',
    confirmButtonColor: '#5F59FF',
  })
    .then(() => {
      // 确认 - 跳转到支付页面
      handleExtendMembership()
    })
    .catch(() => {
      // 取消 - 稍后再说
      handleLaterAction()
    })
}

// 检查是否需要显示会员提醒
const checkMembershipReminder = async () => {
  // 测试模式下直接显示弹窗
  if (isTestMode.value) {
    // 确保使用与 mockService 一致的用户信息
    if (import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL) {
      try {
        const { mockDataStore } = await import('@/utils/mockService')
        const currentUser = mockDataStore.getCurrentUser()
        // 只有在用户信息不存在时才设置
        if (!userStore.userInfo) {
          userStore.setUserInfo(currentUser)
        }
      } catch (error) {
        console.error('获取 mockService 用户信息失败:', error)
      }
    }
    
    setTimeout(() => {
      showMembershipReminder()
    }, 1500) // 延迟1.5秒弹出
    return
  }

  const userInfo = userStore.userInfo
  if (!userInfo || !membershipStatus.value) {
    return
  }

  // 检查今天是否已经显示过提醒（使用localStorage避免重复提醒）
  const today = new Date().toDateString()
  const lastReminderDate = localStorage.getItem('membership_reminder_date')
  
  if (lastReminderDate !== today) {
    showMembershipReminder()
    localStorage.setItem('membership_reminder_date', today)
  }
}

// 监听用户信息变化，检查会员状态
watch(
  () => userStore.userInfo,
  (newUserInfo) => {
    // 测试模式下不需要等待用户信息，直接执行
    if (isTestMode.value) {
      checkMembershipReminder()
      return
    }

    if (newUserInfo) {
      // 延迟3秒弹出，让用户先适应聊天界面
      setTimeout(() => {
        checkMembershipReminder()
      }, 1500)
    }
  },
  { immediate: true }
)

// 处理延长会员
const handleExtendMembership = () => {
  router.push({ name: 'Payment' })
}

// 处理稍后再说
const handleLaterAction = () => {
  // 可以考虑在几小时后再次提醒
}

// 滚动到底部
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// WebSocket连接现在由全局store管理，无需在组件中初始化

// 获取语音识别配置
const fetchVoiceConfig = async () => {
  try {
    const isWechat = isWechatBrowser()
    const response = await fetch(`/api/v1/airelief/chat/voice-recognition/config?is_wechat_env=${isWechat}`)
    const result = await response.json()

    if (result.code === 200) {
      voiceConfig.value = result.data
      voiceRecognitionMode.value = result.data.recommended_mode?.mode || 'auto'
      console.log('🔧 语音识别配置:', voiceConfig.value)
      console.log('🎯 推荐模式:', voiceRecognitionMode.value)
    } else {
      console.warn('获取语音识别配置失败:', result)
    }
  } catch (error) {
    console.error('获取语音识别配置异常:', error)
  }
}

// 初始化语音录制器
const initAudioRecorder = async () => {
  try {
    // 先获取语音识别配置
    // await fetchVoiceConfig()

    // 在微信环境中，先初始化JS-SDK
    // if (isWechatBrowser()) {
    //   try {
    //     console.log('检测到微信环境，初始化JS-SDK...')
    //     await initWeChatJSSDK()
    //     console.log('微信JS-SDK初始化成功')
    //   } catch (error) {
    //     console.warn('微信JS-SDK初始化失败，将使用浏览器录音:', error)
    //   }
    // }

    // 检查录音支持情况
    const support = await UniversalRecorder.checkSupport()
    console.log('🔍 录音支持情况:', support)

    if (support.recommended === 'none') {
      console.warn('❌ 当前环境不支持录音功能')
      showToast('当前环境不支持录音功能')
      return
    }

    // 创建录音器实例 - 强制使用浏览器录音，即使在微信环境中
    audioRecorder.value = new UniversalRecorder({
      maxDuration: 60,
      minDuration: 1,
      autoUploadWechat: false, // 不自动上传到微信服务器
      forceBrowser: true // 强制使用浏览器录音，不使用微信录音
    })

    // 初始化录音器
    await audioRecorder.value.initialize()
    
    // 设置事件回调
    audioRecorder.value.onStart(() => {
      isRecording.value = true
      recordingDuration.value = 0
      console.log('🎤 录音开始')
    })

    audioRecorder.value.onStop(async (result) => {
      console.log('🎤 录音完成，结果:', result)
      console.log('🎤 录音提供者:', result.provider)
      
      // 立即更新录音状态
      isRecording.value = false
      recordingDuration.value = 0
    })

    audioRecorder.value.onError((error) => {
      isRecording.value = false
      recordingDuration.value = 0
      console.error('❌ 录音错误:', error)
      showToast(`录音失败: ${error}`)
    })

    audioRecorder.value.onDataAvailable((duration) => {
      recordingDuration.value = duration
    })
    
    // 设置实时音频数据回调
    audioRecorder.value.onRealTimeData((audioData: string) => {
      // 如果流式识别已激活，发送音频数据
      if (isStreamingActive.value && wsStore.isConnected) {
        console.log(`🎵 发送实时音频数据: ${audioData.length} bytes`)
        wsStore.sendStreamingAudio(audioData)
      }
    })

    const status = audioRecorder.value.getStatus()
    console.log(`✅ 录音器初始化成功，推荐方式: ${support.recommended}，实际使用: ${status.provider}`)
    
  } catch (error) {
    console.error('❌ 录音器初始化失败:', error)
    showToast('录音功能初始化失败')
  }
}

const handleSendMessage = async (message: string) => {
  if (!message.trim()) return

  // 检查用户剩余时长
  if (isChatDisabled.value) {
    showToast(disabledMessage.value)
    return
  }

  if (!wsStore.isConnected) {
    showToast('连接已断开，请重新连接')
    return
  }

  // 通过WebSocket发送消息
  wsStore.sendTextMessage(message.trim())
  inputText.value = ''
}

// 处理语音录音完成
const handleVoiceRecordingComplete = async (result: any) => {
  console.log('🎯 进入 handleVoiceRecordingComplete，参数:', result)

  // 检查用户剩余时长
  if (isChatDisabled.value) {
    showToast(disabledMessage.value)
    return
  }

  try {
    let clientRecognition = ''
    const isWechat = isWechatBrowser()

    // 根据配置和环境决定是否进行客户端识别
    const shouldUseClientRecognition = (
      voiceRecognitionMode.value === 'client_first' ||
      (voiceRecognitionMode.value === 'auto' && isWechat && result.provider === 'wechat')
    )

    console.log('🎤 录音完成，分析识别策略:')
    console.log('  - provider:', result.provider)
    console.log('  - localId:', result.localId)
    console.log('  - serverId:', result.serverId)
    console.log('  - duration:', result.duration)
    console.log('  - 识别模式:', voiceRecognitionMode.value)
    console.log('  - 是否微信环境:', isWechat)
    console.log('  - 是否使用客户端识别:', shouldUseClientRecognition)

    // 如果需要客户端识别且是微信环境
    if (shouldUseClientRecognition && isWechat && result.provider === 'wechat' && result.localId) {
      try {
        console.log('🔄 尝试微信客户端语音识别...')
        showToast('正在进行语音识别...')

        // 使用微信客户端语音识别
        if (audioRecorder.value && typeof audioRecorder.value.translateVoice === 'function') {
          clientRecognition = await audioRecorder.value.translateVoice(result.localId)
          console.log('✅ 微信客户端识别成功:', clientRecognition)
        } else {
          console.warn('⚠️ 录音器不支持客户端识别功能')
        }
      } catch (error) {
        console.warn('⚠️ 微信客户端识别失败:', error)
        // 客户端识别失败，将由服务器端处理（如果启用了降级机制）
      }
    }

    // 发送语音消息到服务器
    if (wsStore.isConnected) {
      // 格式化音频时长为整数
      const audioDuration = Math.round(result.duration)

      // 构建发送数据
      const voiceData = {
        audio_data: '',
        duration: audioDuration,
        recognition_mode: voiceRecognitionMode.value,
        client_recognition: clientRecognition || undefined,
        is_wechat_env: isWechat,
        audio_format: result.provider === 'wechat' ? 'amr' : 'wav'
      }

      // 根据录音提供者设置音频数据
      if (result.provider === 'browser' && result.audioData) {
        voiceData.audio_data = result.audioData
      } else if (result.provider === 'wechat' && result.serverId) {
        voiceData.audio_data = result.serverId
      } else if (result.provider === 'wechat' && result.localId) {
        voiceData.audio_data = result.localId
      }

      console.log('📤 发送语音数据到服务器:', {
        ...voiceData,
        audio_data: voiceData.audio_data.substring(0, 50) + '...' // 只显示前50个字符
      })

      // 发送语音消息
      wsStore.sendVoiceMessage(voiceData.audio_data, audioDuration, {
        recognition_mode: voiceData.recognition_mode,
        client_recognition: voiceData.client_recognition,
        is_wechat_env: voiceData.is_wechat_env,
        audio_format: voiceData.audio_format
      })

      // 添加用户语音消息到界面
      const voiceMessage: ChatMessage = {
        id: `voice-${Date.now()}`,
        role: 'user',
        content: clientRecognition || '', // 如果有客户端识别结果，先显示
        type: 'audio',
        timestamp: Date.now(),
        audioDuration: audioDuration,
        transcription: clientRecognition || undefined,
        localId: result.localId // 保存localId用于播放
      }

      wsStore.addMessage(voiceMessage)
      nextTick(() => scrollToBottom())
    } else {
      showToast('连接已断开，无法发送语音')
    }

  } catch (error) {
    console.error('❌ 处理语音录音失败:', error)
    showToast('语音处理失败')
  }
}

// 注意：语音消息处理已集成到 handleVoiceRecordingComplete 函数中
// ChatInput 组件的录音功能通过 audioRecorder 的回调自动处理

// 开始录音
const startRecording = async () => {
  // 检查用户剩余时长
  if (isChatDisabled.value) {
    showToast(disabledMessage.value)
    return
  }

  if (!audioRecorder.value) {
    showToast('录音功能不可用')
    return
  }

  try {
    console.log('🎤 准备开始录音...')

    // 立即设置录音状态，显示遮罩层
    isRecording.value = true
    console.log('🎤 [ChatPage] 设置录音状态为 true，遮罩层应该显示')

    // 请求录音权限
    const hasPermission = await audioRecorder.value.requestPermission()
    if (!hasPermission) {
      showToast('需要录音权限才能使用语音功能')
      isRecording.value = false
      return
    }

    // 重置识别状态
    currentRecognitionText.value = ''
    finalRecognitionText.value = ''
    
    // 启动流式识别
    if (wsStore.isConnected) {
      console.log('🔄 启动流式识别...')
      const streamingStarted = wsStore.startStreamingRecognition()

      if (!streamingStarted) {
        throw new Error('启动流式识别失败')
      }

      // 等待流式识别启动确认（最多等待3秒）
      console.log('⏳ 等待流式识别启动确认...')
      let waitCount = 0
      const maxWait = 30 // 最多等待3秒（30 * 100ms）

      while (!isStreamingActive.value && waitCount < maxWait) {
        await new Promise(resolve => setTimeout(resolve, 100))
        waitCount++
      }

      if (!isStreamingActive.value) {
        console.warn('⚠️ 流式识别启动确认超时，但继续开始录音')
        showToast('流式识别启动可能有延迟，继续录音')
      } else {
        console.log('✅ 流式识别已确认启动')
      }
    } else {
      throw new Error('WebSocket未连接')
    }
    
    // 开始录音
    console.log('🎤 开始录音...')
    await audioRecorder.value.startRecording()
    
  } catch (error: any) {
    console.error('❌ 开始录音失败:', error)
    const errorMessage = error.message || '录音启动失败'
    showToast(errorMessage)
    isRecording.value = false
  }
}

// 停止录音（参考测试页面实现）
const stopRecording = async () => {
  if (!audioRecorder.value || !isRecording.value) {
    console.log('⚠️ [ChatPage] 录音器未在录音状态')
    return
  }

  try {
    console.log('🛑 [ChatPage] 准备停止录音...')

    // 停止录音
    console.log('🛑 [ChatPage] 停止录音...')
    await audioRecorder.value.stopRecording()

    isRecording.value = false

    // 结束流式识别
    if (wsStore.isConnected && wsStore.wsClient) {
      console.log('🛑 [ChatPage] 结束流式识别...')
      wsStore.endStreamingRecognition()
    }

    console.log('✅ [ChatPage] 录音已停止')

  } catch (error: any) {
    console.error('❌ [ChatPage] 停止录音失败:', error)
    showToast(`停止录音失败: ${error.message}`)
    isRecording.value = false
    isStreamingActive.value = false
  }
}

// 取消录音
const cancelRecording = () => {
  if (audioRecorder.value && isRecording.value) {
    console.log('🚫 取消录音...')

    audioRecorder.value.cancelRecording()
    isRecording.value = false
    recordingDuration.value = 0

    // 结束流式识别
    if (wsStore.isConnected && isStreamingActive.value) {
      console.log('🚫 取消流式识别...')
      wsStore.endStreamingRecognition()
    }

    // 重置识别状态
    isStreamingActive.value = false
    currentRecognitionText.value = ''
    finalRecognitionText.value = ''
  }
}

// 开始新对话功能已移到全局WebSocket管理中

// 个人中心相关处理方法
const handleExtendSubscription = () => {
  showProfile.value = false
  router.push({ name: 'Payment' })
}

const handleContactSupport = () => {
  showProfile.value = false
  router.push({ name: 'ContactUs' })
}

const handleOpenSettings = () => {
  showProfile.value = false
  router.push({ name: 'Settings' })
}

const handleEditProfile = () => {
  showProfile.value = false
  router.push({ name: 'ProfileEdit' })
}

// 输出用户状态到控制台
const logUserStatus = () => {
  console.log('===== 用户状态调试信息 =====')
  console.log('登录状态:', userStore.isLoggedIn ? '已登录' : '未登录')
  console.log('用户信息:', userStore.userInfo)
  
  if (userStore.userInfo) {
    console.log('用户类型:', userStore.userInfo.isNewUser ? '新用户' : '老用户')
    console.log('剩余时长:', `${userStore.userInfo.duration}秒 (${formatDuration(userStore.userInfo.duration)})`)
    console.log('总陪伴时长:', `${userStore.userInfo.totalDuration || 0}秒 (${formatDuration(userStore.userInfo.totalDuration || 0)})`)
    
    if (userStore.userInfo.expiryDate) {
      const expiryDate = new Date(userStore.userInfo.expiryDate)
      console.log('会员到期时间:', expiryDate.toLocaleString(), `(${membershipStatus.value?.type || '正常'})`)
    } else {
      console.log('会员到期时间: 未设置')
    }
  }
  console.log('===========================')
}

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  if (!seconds) return '0分钟'
  
  const days = Math.floor(seconds / (24 * 60 * 60))
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60))
  const minutes = Math.floor((seconds % (60 * 60)) / 60)
  
  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (minutes > 0 || (days === 0 && hours === 0)) result += `${minutes}分钟`
  
  return result
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.hidden) {
    // 页面隐藏时记录时间
    localStorage.setItem('chat_page_hidden_time', Date.now().toString())
    console.log('页面隐藏，记录时间')
  } else {
    // 页面重新显示时检查是否需要重连
    const hiddenTime = localStorage.getItem('chat_page_hidden_time')
    if (hiddenTime) {
      const hiddenDuration = Date.now() - parseInt(hiddenTime)
      const maxHiddenTime = 5 * 60 * 1000 // 5分钟

      console.log(`页面重新显示，隐藏时长: ${Math.round(hiddenDuration / 1000)}秒`)

      if (hiddenDuration > maxHiddenTime) {
        // 超过5分钟，提示用户开始新对话
        console.log('页面长时间隐藏后重新显示')
        if (!wsStore.isConnected) {
          showToast('连接已断开，请开始新对话')
        } else {
          showToast('欢迎回来')
        }
      } else {
        // 短时间隐藏，检查连接状态
        if (!wsStore.isConnected) {
          console.log('检测到连接断开')
          showToast('连接已断开，对话已结束')
        }
      }
      localStorage.removeItem('chat_page_hidden_time')
    }
  }
}

// 页面卸载前处理
const handleBeforeUnload = () => {
  // 如果正在录音，先取消录音
  if (audioRecorder.value && isRecording.value) {
    try {
      audioRecorder.value.cancelRecording()
    } catch (error) {
      console.log('页面卸载前取消录音出现错误（可忽略）:', error)
    }
  }

  // 页面卸载时不断开WebSocket连接，由App.vue统一管理
  console.log('ChatPage页面卸载，WebSocket连接保持（由App.vue管理）')

  // 不再显示确认对话框，允许用户自由离开和返回
}

// 开始新对话功能已移除，WebSocket连接由全局管理

// 初始化WebSocket事件监听
const initWebSocketEvents = () => {
  // 监听WebSocket系统消息（流式识别事件）
  if (wsStore.wsClient) {
    console.log('🔧 设置WebSocket系统消息监听器')
    console.log('🔧 WebSocket客户端状态:', wsStore.wsClient.isConnected())

    // 参考测试页面的实现方式，直接设置系统消息回调
    wsStore.wsClient.onSystemMessage((type, data) => {
      console.log('📨 [ChatPage] 收到WebSocket系统消息:', type, data)
      handleWebSocketMessage(type, data)
    })

    console.log('✅ WebSocket系统消息监听器设置完成')
  } else {
    console.warn('⚠️ WebSocket客户端不存在，无法设置事件监听')
  }
}

// 处理WebSocket消息（参考测试页面实现）
const handleWebSocketMessage = (type: string, data: any) => {
  console.log('📨 [ChatPage] 处理WebSocket消息:', type)

  // 详细记录消息内容
  if (data && Object.keys(data).length > 0) {
    console.log('📨 [ChatPage] 消息数据:', JSON.stringify(data))
  }

  switch (type) {
    case 'streaming_started':
      console.log('🔄 [ChatPage] 流式识别已开始')
      isStreamingActive.value = true
      showToast('开始语音识别')
      break

    case 'streaming_result':
      console.log('🔍 [ChatPage] 收到流式识别结果:', data)
      handleStreamingResult(data)
      break

    case 'streaming_ended':
      console.log('🛑 [ChatPage] 流式识别已结束')
      isStreamingActive.value = false

      // 如果有最终识别结果，使用它作为消息内容
      if (finalRecognitionText.value.trim()) {
        console.log('✅ [ChatPage] 使用最终识别结果发送消息:', finalRecognitionText.value)
        handleSendMessage(finalRecognitionText.value)

        // 重置识别状态
        currentRecognitionText.value = ''
        finalRecognitionText.value = ''
      } else {
        console.log('⚠️ [ChatPage] 没有识别到有效文本')
        showToast('没有识别到语音内容')
        // 重置当前识别文本
        currentRecognitionText.value = ''
      }
      break

    case 'error':
      console.error('❌ [ChatPage] 流式识别错误:', data)
      isStreamingActive.value = false
      currentRecognitionText.value = ''
      finalRecognitionText.value = ''
      showToast('语音识别出错')
      break

    default:
      console.log('⚠️ [ChatPage] 未处理的系统消息类型:', type)
  }
}

// 处理流式识别结果（参考测试页面实现）
const handleStreamingResult = (result: any) => {
  const { text, is_final, confidence } = result

  console.log('🔍 [ChatPage] 流式识别结果:', text, '(final:', is_final, ', confidence:', confidence, ')')

  if (is_final) {
    // 最终结果
    finalRecognitionText.value = text
    currentRecognitionText.value = '' // 清空实时结果
    console.log('✅ [ChatPage] 最终识别结果:', text)
  } else {
    // 实时结果
    currentRecognitionText.value = text
    console.log('🔄 [ChatPage] 实时识别结果:', text)
  }
}



onMounted(async () => {
  scrollToBottom()

  // 输出用户状态调试信息
  logUserStatus()

  // WebSocket连接现在由App.vue管理，这里只需要初始化语音录制器
  await initAudioRecorder()

  // 加载历史消息（无论WebSocket是否连接）
  if (userStore.userInfo?.user_id) {
    try {
      console.log('页面加载时恢复历史消息')

      // 先加载缓存的当前会话消息
      const currentSessionIdValue = wsStore.getCurrentSessionId()
      let cachedMessages: any[] = []
      if (currentSessionIdValue) {
        cachedMessages = wsStore.loadMessagesFromCache(currentSessionIdValue)
        console.log(`从缓存加载了 ${cachedMessages.length} 条消息`)
      }

      // 如果缓存中没有消息，或者消息很少，尝试加载历史消息
      if (cachedMessages.length < 5) {
        console.log('缓存消息较少，加载历史消息')
        const historyMessages = await wsStore.loadHistoryMessages(userStore.userInfo.user_id, 10)
        console.log(`从服务器加载了 ${historyMessages.length} 条历史消息`)

        if (historyMessages.length > 0) {
          const mergedMessages = wsStore.mergeHistoryWithCurrent(historyMessages, cachedMessages)
          // 直接设置到store中
          wsStore.messages.splice(0, wsStore.messages.length, ...mergedMessages)
          console.log(`合并后总共 ${mergedMessages.length} 条消息`)
        } else if (cachedMessages.length > 0) {
          // 只有缓存消息
          wsStore.messages.splice(0, wsStore.messages.length, ...cachedMessages)
        }
      } else {
        // 缓存消息足够，直接使用
        console.log('使用缓存消息')
        wsStore.messages.splice(0, wsStore.messages.length, ...cachedMessages)
      }

      nextTick(() => scrollToBottom())
    } catch (error) {
      console.error('加载历史消息失败:', error)
    }
  }

  // 调试：监听消息变化
  watch(
    () => messages.value.length,
    (newLength, oldLength) => {
      console.log(`消息数量变化: ${oldLength} -> ${newLength}`)
    }
  )

  // 调试：监听录音状态变化
  watch(
    () => isRecording.value,
    (newValue, oldValue) => {
      console.log(`🎤 [ChatPage] 录音状态变化: ${oldValue} -> ${newValue}`)
      if (newValue) {
        console.log('🎤 [ChatPage] 遮罩层应该显示')
      } else {
        console.log('🎤 [ChatPage] 遮罩层应该隐藏')
      }
    }
  )

  // 调试：监听流式识别状态变化
  watch(
    () => isStreamingActive.value,
    (newValue, oldValue) => {
      console.log(`🔄 [ChatPage] 流式识别状态变化: ${oldValue} -> ${newValue}`)
    }
  )

  // 调试：监听识别文本变化
  watch(
    () => currentRecognitionText.value,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        console.log(`🔄 [ChatPage] 实时识别文本变化: "${oldValue}" -> "${newValue}"`)
      }
    }
  )

  watch(
    () => finalRecognitionText.value,
    (newValue, oldValue) => {
      if (newValue !== oldValue) {
        console.log(`✅ [ChatPage] 最终识别文本变化: "${oldValue}" -> "${newValue}"`)
      }
    }
  )

  // 监听消息变化，自动滚动到底部
  watch(
    () => wsStore.messages.length,
    () => {
      nextTick(() => scrollToBottom())
    }
  )

  // 初始化用户信息
  if (!userStore.userInfo) {
    if (userStore.token) {
      await userStore.fetchUserInfo()
    } else {
      // 开发模式下的模拟用户数据
      userStore.setUserInfo({
        id: 1,
        user_id: 'test_user_001',
        wechat_openid: 'test_openid_001',
        nickname: '测试用户',
        avatar: '',
        gender: 0,
        wechat_privilege: [],
        is_wechat_subscribed: false,
        isNewUser: false,
        duration: 0, // 测试时长不足的情况
        totalDuration: 100 * 60 * 60,
        consumedDuration: 0,
        companionDays: 0,
        ipLocation: '北京市',
        expiryDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }
  }

  // 监听页面可见性变化（仅在ChatPage中处理）
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 监听页面卸载（仅在ChatPage中处理）
  window.addEventListener('beforeunload', handleBeforeUnload)

  // 初始化WebSocket事件监听（延迟执行，确保WebSocket连接已建立）
  // 使用watch监听WebSocket客户端的变化，确保在连接建立后设置事件监听
  watch(
    () => wsStore.wsClient,
    (newClient) => {
      if (newClient) {
        console.log('🔧 WebSocket客户端已创建，设置流式识别事件监听')
        initWebSocketEvents()
      }
    },
    { immediate: true }
  )
})

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理事件监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // WebSocket连接由App.vue管理，这里不需要断开

  // 只有在真正录音时才取消录音
  if (audioRecorder.value && isRecording.value) {
    try {
      audioRecorder.value.cancelRecording()
    } catch (error) {
      console.log('组件卸载时取消录音出现错误（可忽略）:', error)
    }
  }
})
</script>

<style scoped>
.chat-page-root {
  height: 100vh;
  position: relative; /* 为绝对定位的子元素提供定位上下文 */
  display: flex;
  flex-direction: column;
}

.gradient-background {
  flex: 1; /* 使内容区域填充可用的垂直空间 */
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F0F0FF 0%, #EEF0F4 100%);
  min-height: 0; /* 允许flex子元素收缩 */
}

.gradient-background .chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子元素收缩 */
}

.gradient-background .message-list {
  flex: 1;
  padding: 16px 0;
  padding-bottom: 80px; /* 为固定定位的输入框预留空间 */
  overflow-y: auto;
  min-height: 0; /* 确保可以滚动 */
}

/* 时长不足提示条样式 */
.duration-warning {
  background: linear-gradient(135deg, #FFE5E5 0%, #FFF0F0 100%);
  border-left: 4px solid #FF6B6B;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.1);
}

.warning-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.warning-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-title {
  font-size: 14px;
  font-weight: 600;
  color: #D63031;
  margin-bottom: 2px;
}

.warning-subtitle {
  font-size: 12px;
  color: #74B9FF;
  opacity: 0.8;
}

.warning-action {
  background: #5F59FF;
  color: white;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.warning-action:hover {
  background: #4C46E8;
  transform: translateY(-1px);
}

.warning-action:active {
  transform: translateY(0);
}

/* 实时语音识别显示区域样式 */
.voice-recognition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
}

.recognition-text {
  max-width: 80%;
  text-align: center;
  color: white;
  padding: 20px;
}

.recording-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 16px;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background-color: #ff4b4b;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 1.5s infinite;
}

.recognition-content {
  font-size: 24px;
  font-weight: 500;
  margin: 16px 0;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recognition-hint {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 16px;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
}
</style>
