<template>
  <div class="not-found-page">
    <van-empty
      image="error"
      description="页面不存在"
      class="empty-state"
    >
      <van-button
        type="primary"
        size="small"
        @click="goHome"
      >
        返回首页
      </van-button>
    </van-empty>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ROUTE_NAMES } from '@/constants'

const router = useRouter()

const goHome = () => {
  router.replace({ name: ROUTE_NAMES.LAUNCH })
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;

  .empty-state {
    padding: 40px 20px;
  }
}
</style>
