<template>
  <default-layout title="隐私政策" :show-back="true">
    <div class="policy-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-text">正在加载隐私政策...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-text">{{ error }}</div>
        <button @click="loadPrivacyPolicy" class="retry-button">重试</button>
      </div>

      <!-- 协议内容 -->
      <div v-else-if="agreementData" class="policy-section">
        <div class="policy-header">
          <!-- <h1>{{ agreementData.title }}</h1> -->
          <div class="policy-meta">
            <span class="version">版本：{{ agreementData.version }}</span>
            <span class="update-time">更新时间：{{ formatDate(agreementData.updated_at) }}</span>
          </div>
        </div>

        <!-- 使用 v-html 渲染 Markdown 内容 -->
        <div class="policy-content-body" v-html="renderedContent"></div>
      </div>

      <!-- 默认内容（如果API失败时的备用内容） -->
      <div v-else class="policy-section">
        <h2>隐私政策</h2>
        <p>正在加载最新的隐私政策内容，请稍候...</p>
      </div>
    </div>
  </default-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { getPrivacyPolicy } from '@/apis/agreement'
import type { PublicAgreementResponse } from '@/apis/agreement'

// 响应式数据
const loading = ref(true)
const error = ref<string | null>(null)
const agreementData = ref<PublicAgreementResponse | null>(null)

// 格式化日期
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}

// 简单的 Markdown 渲染（将换行转换为 <br>，将 # 转换为标题）
const renderedContent = computed(() => {
  if (!agreementData.value?.content) return ''

  let content = agreementData.value.content

  // 处理标题
  content = content.replace(/^### (.*$)/gm, '<h3>$1</h3>')
  content = content.replace(/^## (.*$)/gm, '<h2>$1</h2>')
  content = content.replace(/^# (.*$)/gm, '<h1>$1</h1>')

  // 处理粗体
  content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

  // 处理换行
  content = content.replace(/\n\n/g, '</p><p>')
  content = content.replace(/\n/g, '<br>')

  // 包装段落
  content = '<p>' + content + '</p>'

  // 清理空段落
  content = content.replace(/<p><\/p>/g, '')
  content = content.replace(/<p><br><\/p>/g, '')

  return content
})

// 加载隐私政策
const loadPrivacyPolicy = async () => {
  try {
    loading.value = true
    error.value = null

    const data = await getPrivacyPolicy()
    agreementData.value = data
  } catch (err: any) {
    console.error('加载隐私政策失败:', err)
    error.value = err.message || '加载隐私政策失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadPrivacyPolicy()
})
</script>

<style scoped>
.policy-content {
  max-width: 336px;
  margin: 0 auto;
  padding: 16px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-text {
  font-family: 'PingFang SC';
  font-size: 16px;
  color: #666666;
}

.error-text {
  font-family: 'PingFang SC';
  font-size: 16px;
  color: #ff4757;
  margin-bottom: 16px;
}

.retry-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #0056CC;
}

.policy-section {
  margin-bottom: 24px;
}

.policy-header {
  margin-bottom: 24px;
  text-align: center;
}

.policy-header h1 {
  font-family: 'PingFang SC';
  font-weight: 600;
  font-size: 20px;
  line-height: 28px;
  color: #222222;
  margin-bottom: 12px;
}

.policy-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999999;
  margin-bottom: 16px;
}

.policy-content-body {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  color: #222222;
  text-align: justify;
}

.policy-content-body :deep(h1) {
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 16px 0;
  color: #222222;
}

.policy-content-body :deep(h2) {
  font-size: 16px;
  font-weight: 500;
  margin: 20px 0 12px 0;
  color: #222222;
}

.policy-content-body :deep(h3) {
  font-size: 15px;
  font-weight: 500;
  margin: 16px 0 8px 0;
  color: #222222;
}

.policy-content-body :deep(p) {
  margin-bottom: 12px;
  line-height: 1.6;
}

.policy-content-body :deep(strong) {
  font-weight: 500;
  color: #222222;
}

.policy-content-body :deep(br) {
  line-height: 1.2;
}
</style>
