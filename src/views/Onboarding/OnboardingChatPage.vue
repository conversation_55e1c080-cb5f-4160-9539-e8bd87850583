<template>
  <div class="onboarding-chat-page">
    <div class="gradient-background">
      <ChatHeader
        title="阿尔伯特·艾利斯"
        subtitle="临床心理学家"
        :is-typing="isTyping"
        :is-online="true"
      />

      <div class="chat-content">
        <!-- 聊天消息列表 -->
        <div class="message-list" ref="messageListRef">
          <MessageItem
            v-for="(message, index) in messages"
            :key="index"
            :message="message"
          />
        </div>
      </div>
    </div>

    <!-- 用户信息收集弹窗 -->
    <UserInfoCollectorPopup
      v-model:show="showUserInfoPopup"
      @save="handleSaveUserInfo"
      @skip="handleSkipUserInfo"
    />

    <!-- 新人福利领取弹窗 -->
    <NewUserBenefitPopup
      v-model:show="showBenefitPopup"
      @claim="handleReceiveBenefit"
      @close="handleCloseBenefit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showSuccessToast, showFailToast } from 'vant'
import { ROUTE_NAMES } from '@/constants'
import ChatHeader from '@/components/business/Chat/ChatHeader.vue'
import MessageItem from '@/components/business/Chat/MessageItem.vue'
import UserInfoCollectorPopup from '@/components/business/Onboarding/UserInfoCollectorPopup.vue'
import NewUserBenefitPopup from '@/components/business/Onboarding/NewUserBenefitPopup.vue'
import { useUserStore } from '@/stores/user'
import { activateNewUserTrial } from '@/apis/benefit'
import { updateUserProfile } from '@/apis/user'
import type { ChatMessage } from '@/types/api'

const router = useRouter()
const userStore = useUserStore()
const messageListRef = ref<HTMLElement>()
const messages = ref<ChatMessage[]>([])
const isTyping = ref(false)

// 用户信息相关
const showUserInfoPopup = ref(false)
const showBenefitPopup = ref(false)

// 引导消息列表
const guidanceMessages = [
  "亲爱的朋友，欢迎来到愈言空间❤️",
  "每个人都值得被倾听，被理解。",
  "在这里，你可以：",
  "😊 安心倾诉：分享你的故事，我们认真聆听每一个细节",
  "💪 共同成长：携手制定暖心计划，一步步看见更好的自己", 
  "✍️ 成长记录：记录每一个微小进步，见证你的蜕变绽放",
  "在这里，你不必独自面对困扰，我们会一直陪伴在你身边。",
  "首先，我希望了解一些你的信息，这样可以为你提供更优质的服务；"
]

// 自动发送引导消息
const sendGuidanceMessage = async (index: number) => {
  if (index >= guidanceMessages.length) {
    // 引导消息发送完毕，显示用户信息收集弹窗

    showUserInfoPopup.value = true
    return
  }

  isTyping.value = true

  // 模拟打字延迟
  await new Promise(resolve => setTimeout(resolve, 1500))

  isTyping.value = false

  // 添加消息
  messages.value.push({
    id: `guidance-${index}`,
    role: 'assistant',
    content: guidanceMessages[index],
    type: 'text',
    timestamp: Date.now()
  })

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 继续发送下一条
  setTimeout(() => {
    sendGuidanceMessage(index + 1)
  }, 500)
}

// 滚动到底部
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 保存用户信息
const handleSaveUserInfo = async (data: { gender: number; birthday: string }) => {
  try {
    console.log('保存用户信息:', data)

    // 调用后端API保存用户信息
    const updatedUserInfo = await updateUserProfile({
      gender: data.gender,
      birthday: data.birthday
    })

    // 更新用户store中的信息
    userStore.setUserInfo(updatedUserInfo)

    console.log('✅ 用户信息保存成功:', updatedUserInfo)
    showSuccessToast('个人信息保存成功')

  } catch (error: any) {
    console.error('❌ 保存用户信息失败:', error)
    showFailToast(error.message || '保存失败，请稍后重试')
  }

  showUserInfoPopup.value = false
  showBenefitPopup.value = true
}

// 跳过用户信息
const handleSkipUserInfo = () => {
  showUserInfoPopup.value = false
  showBenefitPopup.value = true
}

// 领取新人福利
const handleReceiveBenefit = async () => {
  try {
    // 激活新用户试用
    const result = await activateNewUserTrial()
    
    // 更新用户store中的信息
    if (result.userInfo) {
      userStore.setUserInfo(result.userInfo)
    }
    
    // 显示成功消息
    showSuccessToast(result.message || '试用激活成功！')
    
    // 关闭福利弹窗
    showBenefitPopup.value = false
    
    console.log('🎉 新用户试用激活成功:', result)
    
  } catch (error: any) {
    console.error('❌ 试用激活失败:', error)
    showFailToast(error.message || '激活失败，请稍后重试')
  }

  showBenefitPopup.value = false

  // 添加完成消息
  messages.value.push({
    id: 'welcome-complete',
    role: 'assistant',
    content: '太好了！现在开始聊天吧～',
    type: 'text',
    timestamp: Date.now()
  })

  scrollToBottom()

  // 更新用户状态为非新用户
  if (userStore.userInfo) {
    userStore.setUserInfo({
      ...userStore.userInfo,
      isNewUser: false
    })
  }

  // 延迟跳转到聊天页面
  setTimeout(() => {
    router.replace({ name: ROUTE_NAMES.CHAT })
  }, 1000)
}

// 关闭新人福利领取弹窗
const handleCloseBenefit = () => {
  showBenefitPopup.value = false

  // 添加关闭消息
  messages.value.push({
    id: 'welcome-skip',
    role: 'assistant',
    content: '我想，我已经初步了解你了！',
    type: 'text',
    timestamp: Date.now()
  })

  scrollToBottom()

  // 更新用户状态为非新用户
  if (userStore.userInfo) {
    userStore.setUserInfo({
      ...userStore.userInfo,
      isNewUser: false
    })
  }

  // 延迟跳转到聊天页面
  setTimeout(() => {
    router.replace({ name: ROUTE_NAMES.CHAT })
  }, 1000)
}

// 初始化
onMounted(async () => {
  // 检查用户状态，老用户直接跳转到聊天页
  if (userStore.userInfo && !userStore.userInfo.isNewUser) {
    console.log('🔄 检测到老用户，跳转到聊天页面')
    await router.replace({ name: ROUTE_NAMES.CHAT })
    return
  }

  // 新用户或未登录用户，显示引导页面
  console.log('👋 欢迎新用户，开始引导流程')
  
  // 开始发送引导消息
  setTimeout(() => {
    sendGuidanceMessage(0)
  }, 500)
})
</script>

<style>
.onboarding-chat-page {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.gradient-background {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* background: linear-gradient(180deg, #F0F0FF 0%, #EEF0F4 100%); */
  overflow: hidden;
}

.gradient-background .chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.gradient-background .message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0; /* 与 ChatPage.vue 一致 */
}


</style>
