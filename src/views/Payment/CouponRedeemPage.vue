<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { validateCoupon, redeemCoupon } from '@/apis/payment'

const router = useRouter()
const couponCode = ref('')
const loading = ref(false)
const validating = ref(false)
const couponValid = ref(false)
const couponInfo = ref({
  discount: 0,
  expiry: '',
  message: ''
})

// 验证兑换码
const validateCouponCode = async () => {
  if (!couponCode.value) return

  validating.value = true
  try {
    const result = await validateCoupon(couponCode.value)
    couponValid.value = result.valid
    couponInfo.value = {
      discount: result.discount || 0,
      expiry: result.expiry || '',
      message: result.message || ''
    }

    if (!result.valid) {
      showToast(result.message || '兑换码无效')
    }
  } catch (error) {
    console.error('验证兑换码失败:', error)
    showToast('验证兑换码失败，请重试')
  } finally {
    validating.value = false
  }
}

// 兑换优惠券
const redeemCouponCode = async () => {
  if (!couponCode.value || !couponValid.value) return

  loading.value = true
  try {
    // 需要用户ID来兑换，这里应该从用户store获取
    const { useUserStore } = await import('@/stores/user')
    const userStore = useUserStore()
    
    if (!userStore.userInfo?.user_id) {
      showToast('用户信息不完整，请重新登录')
      return
    }

    const result = await redeemCoupon(couponCode.value, userStore.userInfo.user_id)
    showToast('兑换成功')

    // 跳转到充值页面
    router.push({
      name: 'Payment',
      query: { coupon: couponCode.value }
    })
  } catch (error) {
    console.error('兑换失败:', error)
    showToast('兑换失败，请重试')
  } finally {
    loading.value = false
  }
}

// showToast 函数已通过 auto-import 自动导入，无需手动定义
</script>

<template>
  <default-layout title="兑换码" :show-back="true">
    <div class="coupon-page">
      <div class="coupon-page__form">
        <van-field
          v-model="couponCode"
          placeholder="请输入兑换码"
          :disabled="validating || loading"
        />

        <div class="coupon-page__actions">
          <van-button
            type="primary"
            block
            :loading="validating"
            :disabled="!couponCode"
            @click="validateCouponCode"
          >
            验证兑换码
          </van-button>
        </div>
      </div>

      <div v-if="couponValid" class="coupon-page__result">
        <div class="coupon-page__info">
          <div class="coupon-page__discount">
            优惠金额: ¥{{ couponInfo.discount }}
          </div>
          <div v-if="couponInfo.expiry" class="coupon-page__expiry">
            有效期至: {{ couponInfo.expiry }}
          </div>
        </div>

        <div class="coupon-page__confirm">
          <van-button
            type="primary"
            block
            :loading="loading"
            @click="redeemCouponCode"
          >
            确认兑换
          </van-button>
        </div>
      </div>

      <div class="coupon-page__tips">
        <p>兑换码可在充值页面直接使用</p>
        <p>如有问题请联系客服</p>
      </div>
    </div>
  </default-layout>
</template>

<style scoped>
.coupon-page {
  padding: 16px;
}

.coupon-page__form {
  margin-bottom: 24px;
}

.coupon-page__actions {
  margin-top: 16px;
}

.coupon-page__result {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.coupon-page__info {
  margin-bottom: 16px;
}

.coupon-page__discount {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b00;
  margin-bottom: 8px;
}

.coupon-page__expiry {
  font-size: 14px;
  color: #666;
}

.coupon-page__tips {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.coupon-page__tips p {
  margin-bottom: 4px;
}
</style>