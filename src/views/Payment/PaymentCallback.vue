<template>
  <div class="payment-callback">
    <div class="loading-container">
      <van-loading size="24px" vertical>{{ loadingText }}</van-loading>

      <!-- 显示当前状态 -->
      <div v-if="currentStatus" class="status-info">
        <p class="status-text">当前状态：{{ getStatusText(currentStatus) }}</p>
        <p v-if="retryCount > 0" class="retry-info">
          正在重试... ({{ retryCount }}/{{ maxRetries }})
        </p>
      </div>

      <!-- 取消按钮 -->
      <van-button
        v-if="showCancelButton"
        type="default"
        size="small"
        @click="cancelPayment"
        class="cancel-btn"
      >
        取消支付
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { useUserStore } from '@/stores/user'
import { ROUTE_NAMES } from '@/constants'
import { getOrderStatus } from '@/apis/payment'
import type { OrderStatus } from '@/types/api'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式状态
const loadingText = ref('处理支付结果...')
const currentStatus = ref<OrderStatus | null>(null)
const retryCount = ref(0)
const maxRetries = 10
const retryInterval = 2000 // 2秒
const showCancelButton = ref(false)
let pollingTimer: NodeJS.Timeout | null = null

// 获取状态文本
const getStatusText = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    pending: '待支付',
    paid: '已支付',
    failed: '支付失败',
    cancelled: '已取消',
    refunded: '已退款',
    expired: '已过期'
  }
  return statusMap[status] || '未知状态'
}

// 取消支付
const cancelPayment = async () => {
  try {
    await showConfirmDialog({
      title: '确认取消',
      message: '确定要取消当前支付吗？',
    })

    // 清除轮询
    if (pollingTimer) {
      clearTimeout(pollingTimer)
      pollingTimer = null
    }

    // 清除本地存储
    localStorage.removeItem('pending_order')

    // 返回支付页面
    router.replace({ name: ROUTE_NAMES.PAYMENT })
  } catch {
    // 用户取消确认对话框
  }
}

// 检查支付状态
const checkStatus = async (orderId: string): Promise<void> => {
  try {
    loadingText.value = '查询支付状态...'
    const paymentResult = await getOrderStatus(orderId)
    currentStatus.value = paymentResult.status

    if (paymentResult.status === 'paid') {
      // 支付成功
      loadingText.value = '支付成功，正在跳转...'
      showSuccessToast('支付成功')

      // 清除本地存储的订单信息
      localStorage.removeItem('pending_order')

      // 刷新用户信息
      await userStore.fetchUserInfo()

      // 跳转到支付结果页面
      router.replace({
        name: 'PaymentResult',
        params: { orderId }
      })
    } else if (paymentResult.status === 'failed' || paymentResult.status === 'cancelled') {
      // 支付失败或取消
      throw new Error(paymentResult.status === 'failed' ? '支付失败' : '支付已取消')
    } else if (paymentResult.status === 'expired') {
      // 订单已过期
      throw new Error('订单已过期')
    } else if (paymentResult.status === 'pending') {
      // 支付处理中，继续轮询
      retryCount.value++
      loadingText.value = `支付处理中... (${retryCount.value}/${maxRetries})`

      if (retryCount.value < maxRetries) {
        // 显示取消按钮（在第3次重试后）
        if (retryCount.value >= 3) {
          showCancelButton.value = true
        }

        pollingTimer = setTimeout(() => checkStatus(orderId), retryInterval)
      } else {
        throw new Error('支付状态确认超时，请稍后查看支付记录')
      }
    }
  } catch (error) {
    if (retryCount.value < maxRetries) {
      retryCount.value++
      loadingText.value = `重试中... (${retryCount.value}/${maxRetries})`

      // 显示取消按钮
      showCancelButton.value = true

      pollingTimer = setTimeout(() => checkStatus(orderId), retryInterval)
    } else {
      throw error
    }
  }
}

onMounted(async () => {
  try {
    // 获取本地存储的订单信息
    const pendingOrderStr = localStorage.getItem('pending_order')
    if (!pendingOrderStr) {
      throw new Error('未找到待支付订单信息')
    }

    const pendingOrder = JSON.parse(pendingOrderStr)
    const { orderId } = pendingOrder

    if (!orderId) {
      throw new Error('订单信息不完整')
    }

    // 开始检查支付状态
    await checkStatus(orderId)
  } catch (error: any) {
    console.error('支付回调处理失败:', error)
    showToast(error.message || '支付状态确认失败')

    // 清除本地存储的订单信息
    localStorage.removeItem('pending_order')

    // 返回支付页面
    router.replace({ name: ROUTE_NAMES.PAYMENT })
  }
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (pollingTimer) {
    clearTimeout(pollingTimer)
    pollingTimer = null
  }
})
</script>

<style scoped>
.payment-callback {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
}

.loading-container {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  max-width: 300px;
  width: 90%;
}

.status-info {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
}

.status-text {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 500;
}

.retry-info {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.cancel-btn {
  margin-top: 20px;
  width: 100px;
}
</style>