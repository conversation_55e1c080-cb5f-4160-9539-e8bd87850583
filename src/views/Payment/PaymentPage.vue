<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import PaymentIdentityCard from '@/components/business/Profile/PaymentIdentityCard.vue'
import RedeemCodeInput from '@/components/business/Profile/RedeemCodeInput.vue'

import { getPaymentPackages, createOrder } from '@/apis/payment'
import { useUserStore } from '@/stores/user'
import type { PaymentPackage } from '@/types/api'
import { formatExpiryDate, formatDuration } from '@/utils/date'

interface RemainingTimeDisplay {
  type: 'days' | 'time'
  value: number
  text: string
  companionDays?: number // 已陪伴天数
}

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const packages = ref<PaymentPackage[]>([])
const selectedPackageId = ref<number | null>(null)
const loading = ref(false)

// 兑换码组件引用
const redeemCodeRef = ref()

// 兑换码相关状态
const couponInfo = ref({
  valid: false,
  discount: 0,
  expiry: '',
  message: ''
})



// 计算剩余时长显示格式
const remainingTimeDisplay = computed((): RemainingTimeDisplay => {
  if (!userStore.userInfo?.duration) return {
    type: 'days',
    value: 0,
    text: '0天',
    companionDays: userStore.userInfo?.companionDays || 0
  }

  const totalSeconds = userStore.userInfo.duration
  const totalHours = totalSeconds / (60 * 60)

  if (totalHours > 24) {
    // 大于24小时，显示天数+剩余时间倒计时格式
    const days = Math.floor(totalSeconds / (24 * 60 * 60))
    const remainingSeconds = totalSeconds % (24 * 60 * 60)
    const hours = Math.floor(remainingSeconds / (60 * 60))
    const minutes = Math.floor((remainingSeconds % (60 * 60)) / 60)
    const seconds = Math.floor(remainingSeconds % 60)

    const timeText = `${days}天 + ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    return {
      type: 'time',
      value: totalSeconds,
      text: timeText,
      companionDays: userStore.userInfo?.companionDays || 0
    }
  } else {
    // 小于等于24小时，显示 hh:mm:ss 格式
    const hours = Math.floor(totalSeconds / (60 * 60))
    const minutes = Math.floor((totalSeconds % (60 * 60)) / 60)
    const seconds = Math.floor(totalSeconds % 60)

    const timeText = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    return {
      type: 'time',
      value: totalSeconds,
      text: timeText,
      companionDays: userStore.userInfo?.companionDays || 0
    }
  }
})

// 计算剩余天数（兼容性保留）
const remainingDays = computed(() => {
  if (!userStore.userInfo?.duration) return 0
  return Math.floor(userStore.userInfo.duration / (24 * 60 * 60))
})

// 获取套餐列表
const fetchPackages = async () => {
  loading.value = true
  try {
    packages.value = await getPaymentPackages()
    if (packages.value.length > 0) {
      selectedPackageId.value = packages.value[0].id
    }
  } catch (error) {
    console.error('获取套餐失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理兑换码验证结果
const handleCouponValidated = async (data: { valid: boolean; discount: number; expiry: string; message: string }) => {
  console.log('支付页面兑换码验证结果:', data)
  
  // 如果兑换码验证成功并且是折扣券，需要进一步验证是否可用于当前套餐
  if (data.valid && data.discount > 0 && selectedPackageId.value && userStore.userInfo?.user_id) {
    try {
      const { validateCouponForPayment } = await import('@/apis/payment')
      const couponCode = redeemCodeRef.value?.getCouponInfo()?.code
      
      if (couponCode) {
        const paymentValidation = await validateCouponForPayment(
          couponCode, 
          userStore.userInfo.user_id, 
          selectedPackageId.value
        )
        
        // 更新兑换码信息，包含最终价格信息
        couponInfo.value = {
          valid: paymentValidation.valid,
          discount: paymentValidation.discount_amount || data.discount,
          expiry: data.expiry,
          message: paymentValidation.message || data.message
        }
        
        console.log('💰 支付验证结果:', paymentValidation)
      } else {
        couponInfo.value = data
      }
    } catch (error) {
      console.error('支付验证失败:', error)
      couponInfo.value = {
        valid: false,
        discount: 0,
        expiry: data.expiry,
        message: '验证支付优惠失败'
      }
    }
  } else {
    couponInfo.value = data
  }
}

// 处理兑换码清空
const handleCouponCleared = () => {
  console.log('支付页面兑换码已清空')
  couponInfo.value = {
    valid: false,
    discount: 0,
    expiry: '',
    message: ''
  }
}

// 获取选中的套餐
const selectedPackage = computed(() => {
  return packages.value.find(pkg => pkg.id === selectedPackageId.value)
})

// 计算最终价格
const finalPrice = computed(() => {
  if (!selectedPackage.value) return 0

  if (couponInfo.value.valid && couponInfo.value.discount > 0) {
    return Math.max(0, selectedPackage.value.price - couponInfo.value.discount)
  }

  return selectedPackage.value.price
})

// 计算套餐显示价格（考虑兑换码优惠）
const getPackageDisplayPrice = (pkg: PaymentPackage) => {
  // 如果有有效的兑换码，并且当前套餐是选中的套餐，显示优惠后价格
  if (couponInfo.value.valid && 
      couponInfo.value.discount > 0 && 
      selectedPackageId.value === pkg.id) {
    return Math.max(0, pkg.price - couponInfo.value.discount)
  }
  return pkg.price
}

// 判断套餐是否有优惠价格
const hasDiscountPrice = (pkg: PaymentPackage) => {
  return couponInfo.value.valid && 
         couponInfo.value.discount > 0 && 
         selectedPackageId.value === pkg.id
}

// 创建订单并支付
const handlePayment = async () => {
  if (!selectedPackageId.value) {
    showToast('请选择套餐')
    return
  }

  // 检查用户信息
  if (!userStore.userInfo?.user_id) {
    showToast('用户信息不完整，请重新登录')
    return
  }

  loading.value = true
  try {
    // 通过组件引用获取兑换码信息
    let couponCode = undefined
    if (couponInfo.value.valid && redeemCodeRef.value) {
      const couponData = redeemCodeRef.value.getCouponInfo()
      couponCode = couponData.code
    }

    // 检查是否在Mock环境
    const isMockEnv = import.meta.env.VITE_ENABLE_MOCK === 'true' || 
                     (!import.meta.env.VITE_API_BASE_URL && import.meta.env.DEV)

    if (isMockEnv) {
      // Mock环境：模拟支付成功
      showToast('支付成功！正在更新您的会员状态...')
      
      // 打印支付前的用户状态
      console.log('🔧 支付前用户状态:', {
        duration: `${userStore.userInfo?.duration}秒 (${formatDuration(userStore.userInfo?.duration || 0)})`,
        totalDuration: `${userStore.userInfo?.totalDuration}秒 (${formatDuration(userStore.userInfo?.totalDuration || 0)})`,
        expiryDate: formatExpiryDate(userStore.userInfo?.expiryDate || '')
      })
      
      // 刷新用户信息以同步最新的会员状态
      await userStore.fetchUserInfo()
      
      // 打印支付后的用户状态
      console.log('🔧 支付后用户状态:', {
        duration: `${userStore.userInfo?.duration}秒 (${formatDuration(userStore.userInfo?.duration || 0)})`,
        totalDuration: `${userStore.userInfo?.totalDuration}秒 (${formatDuration(userStore.userInfo?.totalDuration || 0)})`,
        expiryDate: formatExpiryDate(userStore.userInfo?.expiryDate || ''),
        remainingDays: remainingDays.value
      })
      
      // 跳转到支付结果页面
      router.push({
        name: 'PaymentResult',
        params: { orderId: 'MOCK_ORDER_' + Date.now() }
      })
    } else {
      // 真实支付环境：调用微信支付
      const orderResult = await createOrder(
        userStore.userInfo.user_id,
        selectedPackageId.value,
        couponCode
      )

      if (orderResult.success) {
        // 支付成功
        showToast('支付成功！正在更新您的会员状态...')

        // 刷新用户信息
        await userStore.fetchUserInfo()

        // 跳转到支付结果页面
        router.push({
          name: 'PaymentResult',
          params: { orderId: orderResult.orderId }
        })
      } else {
        // 支付创建成功但用户可能取消了支付，需要查询订单状态
        console.log('支付流程完成，但结果未知，建议查询订单状态')
        showToast('支付状态确认中，请稍候...')

        // 可以添加订单状态查询逻辑
        // const { getOrderStatus } = await import('@/apis/payment')
        // const status = await getOrderStatus(orderResult.orderId)
      }
    }
  } catch (error: any) {
    console.error('支付失败:', error)
    
    // 根据错误类型显示不同的提示
    if (error.message?.includes('微信客户端')) {
      showToast('请在微信客户端中打开')
    } else if (error.message?.includes('用户取消')) {
      showToast('已取消支付')
    } else if (error.message?.includes('支付失败')) {
      showToast('支付失败，请重试')
    } else {
      showToast(error.message || '支付过程中出现错误，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 处理充值记录点击
const handleRecordClick = () => {
  router.push({ name: 'OrderHistory' })
}

// 处理联系开发者团队点击
const handleContactClick = () => {
  router.push({ name: 'ContactUs' })
}

onMounted(async () => {
  // 获取套餐列表
  fetchPackages()
  
  // 确保页面加载时获取最新的用户信息
  await userStore.fetchUserInfo()
  
  // 处理从侧边栏传递的兑换码信息
  if (route.query.coupon_validated === 'true') {
    const couponCode = route.query.coupon_code as string || ''
    const discount = parseFloat(route.query.coupon_discount as string) || 0
    const expiry = route.query.coupon_expiry as string || ''
    const message = route.query.coupon_message as string || ''
    
    // 设置兑换码信息到状态中
    couponInfo.value = {
      valid: true,
      discount: discount,
      expiry: expiry,
      message: message
    }
    
    // 如果有兑换码，设置到RedeemCodeInput组件中
    if (couponCode && redeemCodeRef.value) {
      setTimeout(() => {
        redeemCodeRef.value?.setCouponInfo({
          code: couponCode,
          valid: true,
          discount: discount,
          expiry: expiry,
          message: message
        })
      }, 100) // 稍微延迟确保组件已渲染
    }
    
    console.log('✅ 从侧边栏同步兑换码信息:', couponInfo.value)
    
    // 清理URL查询参数，避免刷新页面时重复设置
    router.replace({ name: 'Payment' })
  }
  
  // 初始化微信JS-SDK（真实微信支付需要）
  if (import.meta.env.VITE_API_BASE_URL) {
    try {
      const { isWechatBrowser, initWeChatJSSDK } = await import('@/utils/wechat')
      if (isWechatBrowser()) {
        console.log('🔧 正在初始化微信JS-SDK...')
        await initWeChatJSSDK()
        console.log('✅ 微信JS-SDK初始化成功')
      }
    } catch (error) {
      console.error('❌ 微信JS-SDK初始化失败:', error)
      // 不要因为JS-SDK初始化失败就阻止页面使用
    }
  }
  
  // 如果启用了Mock服务，确保与 mockService 同步
  if (import.meta.env.VITE_ENABLE_MOCK === 'true' || (!import.meta.env.VITE_API_BASE_URL && import.meta.env.DEV)) {
    try {
      const { mockDataStore } = await import('@/utils/mockService')
      const currentUser = mockDataStore.getCurrentUser()
      
      // 只有在用户信息不存在或不一致时才设置
      if (!userStore.userInfo || 
          userStore.userInfo.duration !== currentUser.duration || 
          userStore.userInfo.expiryDate !== currentUser.expiryDate) {
        console.log('🔧 支付页面: 同步用户信息', currentUser)
        userStore.setUserInfo(currentUser)
      }
    } catch (error) {
      console.error('获取 mockService 用户信息失败:', error)
    }
  }
})
</script>

<template>
  <default-layout
    title="陪伴加时"
    :show-back="true"
    right-text="充值记录"
    @right-click="handleRecordClick"
  >
    <div class="payment-page">
      <div class="payment-page__header">

        <!-- AI Relief 身份卡片 -->
        <div class="identity-card-container">
          <PaymentIdentityCard
            :remaining-time-display="remainingTimeDisplay"
            :show-arrow="false"
          />
        </div>

        <div class="payment-page__tip">
          开通会员后可享受相应时长的畅聊特权，每月可对话上限1000万字 (每月自动刷新) ✨
        </div>
      </div>

      <div class="payment-page__packages">
        <div class="payment-page__title">陪伴时长卡</div>

        <van-loading v-if="loading" />

        <template v-else>
          <van-radio-group v-model="selectedPackageId">
            <div class="payment-page__packages-container">
              <div
                v-for="pkg in packages"
                :key="pkg.id"
                class="payment-page__package"
                :class="{ 'payment-page__package--selected': selectedPackageId === pkg.id }"
                @click="selectedPackageId = pkg.id"
              >
                <!-- 限时特惠标签 -->
                <div v-if="pkg.tag" class="payment-page__package-tag">
                  {{ pkg.tag }}
                </div>

                <div class="payment-page__package-content">
                  <div class="payment-page__package-name">{{ pkg.name }}</div>

                  <div class="payment-page__package-price">
                    <div class="payment-page__price-current">¥{{ getPackageDisplayPrice(pkg) }}</div>
                    <div v-if="pkg.original_price" class="payment-page__price-original">
                      ¥{{ pkg.original_price }}
                    </div>
                  </div>
                </div>

                <!-- 隐藏的radio按钮 -->
                <van-radio :name="pkg.id" style="display: none;" />
              </div>
            </div>
          </van-radio-group>
        </template>
      </div>

      <!-- 兑换码输入组件 -->
      <div class="payment-page__coupon">
        <RedeemCodeInput 
          ref="redeemCodeRef"
          @coupon-validated="handleCouponValidated"
          @coupon-cleared="handleCouponCleared"
        />
      </div>

      <div class="payment-page__actions">
        <van-button
          type="primary"
          block
          :loading="loading"
          @click="handlePayment"
        >
          确认支付 ¥{{ finalPrice }}
        </van-button>
      </div>

      <div class="payment-page__tips">
        <p>支付问题请<span class="contact-link" @click="handleContactClick">联系开发者团队</span></p>
      </div>
    </div>
  </default-layout>
</template>

<style scoped>
.payment-page {
  padding: 0;
}

.payment-page__header {
  margin-bottom: 24px;
}

/* AI Relief 身份卡片容器 */
.identity-card-container {
  width: 100%;
  margin: 24px auto 0;
}

.payment-page__tip {
  font-size: 12px;
  color: #666;
  margin-top: 16px;
  text-align: center;
}

/* 陪伴时长卡标题 */
.payment-page__title {
  width: 100%;
  height: 24px;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 24px;
  color: #39359B;
  margin-bottom: 12px;
}

.payment-page__packages {
  margin-bottom: 24px;
}

/* 套餐容器 - 水平布局 */
.payment-page__packages-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0px;
  gap: 8px;
  width: 100%;
  overflow: hidden;
}

/* 单个套餐卡片 */
.payment-page__package {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 16px;
  gap: 10px;
  flex: 1;
  min-width: 0;
  width: calc(33.333% - 6px);
  height: 143px;
  background: #FFFFFF;
  border: 1px solid #F0F0FF;
  border-radius: 8px;
  position: relative;
  cursor: pointer;
}

/* 选中状态 */
.payment-page__package--selected {
  background: #F6F6FF;
  border: 1px solid #5F59FF;
}

/* 套餐内容容器 */
.payment-page__package-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px;
  gap: 12px;
  width: 100%;
  height: 84px;
  flex: none;
  flex-grow: 1;
}

/* 套餐名称 */
.payment-page__package-name {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #39359B;
  text-align: center;
  flex: none;
}

/* 价格容器 */
.payment-page__package-price {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 8px;
  width: 100%;
  height: 48px;
  flex: none;
}

/* 当前价格 */
.payment-page__price-current {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 20px;
  color: #39359B;
  flex: none;
}

/* 原价 */
.payment-page__price-original {
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  text-decoration-line: line-through;
  color: #8A88B5;
  flex: none;
}

/* 限时特惠标签 */
.payment-page__package-tag {
  position: absolute;
  width: 56px;
  height: 20px;
  right: 0px;
  top: 0px;
  background: #5F59FF;
  border-radius: 0 8px 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 20px;
  color: #FFFFFF;
}

.payment-page__coupon {
  margin-bottom: 24px;
}

.payment-page__actions {
  margin-bottom: 16px;
}

.payment-page__tips {
  font-size: 12px;
  color: #8A88B5;
  text-align: center;
}

.payment-page__tips p {
  margin-bottom: 4px;
}

.contact-link {
  color: #8A88B5;
  text-decoration: underline;
  cursor: pointer;
}

.contact-link:hover {
  color: #4842d4;
}

/* 响应式布局 - 小屏幕优化 */
@media (max-width: 375px) {
  .payment-page__packages-container {
    gap: 6px;
  }
  
  .payment-page__package {
    padding: 16px 12px;
    width: calc(33.333% - 4px);
  }
  
  .payment-page__package-content {
    gap: 8px;
    width: auto;
  }
  
  .payment-page__package-name {
    font-size: 14px;
    line-height: 20px;
  }
  
  .payment-page__price-current {
    font-size: 20px;
    line-height: 18px;
  }
  
  .payment-page__price-original {
    font-size: 11px;
  }
  
  .payment-page__package-tag {
    width: 50px;
    height: 18px;
    font-size: 10px;
    line-height: 18px;
  }
}

/* 超小屏幕 - 垂直布局 */
@media (max-width: 320px) {
  .payment-page__packages-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .payment-page__package {
    width: 100%;
    padding: 20px;
  }
  
  .payment-page__package-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: auto;
  }
  
  .payment-page__package-price {
    align-items: flex-end;
    width: auto;
    height: auto;
  }
}
</style>
