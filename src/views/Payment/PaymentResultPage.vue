<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { getOrderStatus, getOrderDetail } from '@/apis/payment'
import { useUserStore } from '@/stores/user'
import { formatPaymentTime, formatDuration } from '@/utils/date'
import type { OrderStatus } from '@/types/api'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const orderId = ref('')
const loading = ref(true)
const paymentSuccess = ref(false)
const orderStatus = ref<OrderStatus>('pending')
const orderInfo = ref({
  packageName: '',
  duration: '',
  durationSeconds: 0,
  amount: 0,
  originalAmount: 0,
  discountAmount: 0,
  couponCode: '',
  payTime: '',
  createdAt: '',
  wechatTransactionId: ''
})
const errorMessage = ref('')

// 获取订单状态和详情
const fetchOrderInfo = async () => {
  if (!orderId.value) return

  loading.value = true
  errorMessage.value = ''

  try {
    // 检查是否是Mock订单
    if (orderId.value.startsWith('MOCK_ORDER_')) {
      // Mock环境直接显示成功
      paymentSuccess.value = true
      orderStatus.value = 'paid'
      orderInfo.value = {
        packageName: '体验套餐',
        duration: '30天',
        durationSeconds: 30 * 24 * 60 * 60,
        amount: 0,
        originalAmount: 0,
        discountAmount: 0,
        couponCode: '',
        payTime: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        wechatTransactionId: 'MOCK_TRANSACTION'
      }
      await userStore.fetchUserInfo()
      return
    }

    // 首先获取订单状态
    const statusResult = await getOrderStatus(orderId.value)
    console.log('订单状态API返回数据:', statusResult)
    orderStatus.value = statusResult.status
    paymentSuccess.value = statusResult.status === 'paid'

    // 如果支付成功，获取详细订单信息
    if (paymentSuccess.value) {
      try {
        const detailResult = await getOrderDetail(orderId.value)
        console.log('订单详情API返回数据:', detailResult)

        orderInfo.value = {
          packageName: detailResult.package_info?.name || detailResult.package_name || '',
          duration: detailResult.package_info?.duration || '未知',
          durationSeconds: detailResult.package_info?.duration_seconds || 0,
          amount: Number(detailResult.order_details?.amount || detailResult.amount || 0),
          originalAmount: Number(detailResult.order_details?.original_amount || detailResult.original_amount || 0),
          discountAmount: Number(detailResult.order_details?.discount_amount || detailResult.discount_amount || 0),
          couponCode: detailResult.order_details?.coupon_code || detailResult.coupon_code || '',
          payTime: detailResult.timestamps?.pay_time || detailResult.pay_time || '',
          createdAt: detailResult.timestamps?.created_at || detailResult.created_at || '',
          wechatTransactionId: detailResult.wechat_pay_info?.transaction_id || detailResult.wechat_transaction_id || ''
        }
      } catch (detailError) {
        console.warn('获取订单详情失败，使用基础信息:', detailError)
        // 使用基础状态信息
        orderInfo.value = {
          packageName: '套餐',
          duration: '未知',
          durationSeconds: 0,
          amount: Number(statusResult.amount || 0),
          originalAmount: Number(statusResult.amount || 0),
          discountAmount: 0,
          couponCode: '',
          payTime: statusResult.pay_time || '',
          createdAt: '',
          wechatTransactionId: ''
        }
      }

      // 刷新用户信息以获取最新的会员状态
      await userStore.fetchUserInfo()
    } else {
      // 支付未成功，使用基础信息
      orderInfo.value = {
        packageName: '套餐',
        duration: '未知',
        durationSeconds: 0,
        amount: Number(statusResult.amount || 0),
        originalAmount: Number(statusResult.amount || 0),
        discountAmount: 0,
        couponCode: '',
        payTime: statusResult.pay_time || '',
        createdAt: '',
        wechatTransactionId: ''
      }
    }
  } catch (error: any) {
    console.error('获取订单信息失败:', error)
    errorMessage.value = error.message || '获取订单信息失败'
    paymentSuccess.value = false
  } finally {
    loading.value = false
  }
}

// 获取状态文本
const getStatusText = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    pending: '待支付',
    paid: '支付成功',
    failed: '支付失败',
    cancelled: '支付取消',
    refunded: '已退款',
    expired: '订单过期'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态颜色
const getStatusColor = (status: OrderStatus): string => {
  const colorMap: Record<OrderStatus, string> = {
    pending: '#ff9500',
    paid: '#07c160',
    failed: '#ee0a24',
    cancelled: '#969799',
    refunded: '#ff9500',
    expired: '#969799'
  }
  return colorMap[status] || '#969799'
}

// 返回聊天页面
const backToChat = () => {
  router.push({ name: 'Chat' })
}

// 返回充值页面
const backToPayment = () => {
  router.push({ name: 'Payment' })
}

// 查看订单详情
const viewOrderDetail = () => {
  // 可以跳转到订单详情页面或显示更多信息
  console.log('查看订单详情:', orderId.value)
}

onMounted(() => {
  // 从路由参数获取订单ID
  orderId.value = route.params.orderId as string

  if (orderId.value) {
    fetchOrderInfo()
  } else {
    loading.value = false
    errorMessage.value = '缺少订单信息'
  }
})
</script>

<template>
  <default-layout title="支付结果" :show-back="false">
    <div class="payment-result">
      <van-loading v-if="loading" vertical>
        获取订单信息...
      </van-loading>

      <!-- 错误状态 -->
      <div v-else-if="errorMessage" class="error-container">
        <van-icon name="warning-o" size="64" color="#ee0a24" />
        <div class="error-message">{{ errorMessage }}</div>
        <van-button type="primary" @click="backToPayment" class="error-btn">
          返回充值
        </van-button>
      </div>

      <template v-else>
        <div class="payment-result__icon">
          <van-icon
            :name="paymentSuccess ? 'success' : 'cross'"
            :color="getStatusColor(orderStatus)"
            size="64"
          />
        </div>

        <div class="payment-result__status">
          {{ getStatusText(orderStatus) }}
        </div>

        <div v-if="paymentSuccess" class="payment-result__info">
          <div class="payment-result__item">
            <span class="payment-result__label">套餐名称</span>
            <span class="payment-result__value">{{ orderInfo.packageName }}</span>
          </div>

          <div class="payment-result__item">
            <span class="payment-result__label">获得时长</span>
            <span class="payment-result__value">
              {{ orderInfo.durationSeconds > 0 ? formatDuration(orderInfo.durationSeconds, 'full') : orderInfo.duration }}
            </span>
          </div>

          <div v-if="orderInfo.discountAmount > 0" class="payment-result__item">
            <span class="payment-result__label">原价</span>
            <span class="payment-result__value">¥{{ orderInfo.originalAmount.toFixed(2) }}</span>
          </div>

          <div v-if="orderInfo.discountAmount > 0" class="payment-result__item">
            <span class="payment-result__label">优惠</span>
            <span class="payment-result__value discount">-¥{{ orderInfo.discountAmount.toFixed(2) }}</span>
          </div>

          <div v-if="orderInfo.couponCode" class="payment-result__item">
            <span class="payment-result__label">优惠券</span>
            <span class="payment-result__value">{{ orderInfo.couponCode }}</span>
          </div>

          <div class="payment-result__item">
            <span class="payment-result__label">实付金额</span>
            <span class="payment-result__value amount">¥{{ orderInfo.amount.toFixed(2) }}</span>
          </div>

          <div class="payment-result__item">
            <span class="payment-result__label">支付时间</span>
            <span class="payment-result__value">{{ formatPaymentTime(orderInfo.payTime) }}</span>
          </div>

          <div v-if="orderInfo.wechatTransactionId" class="payment-result__item">
            <span class="payment-result__label">交易单号</span>
            <span class="payment-result__value transaction-id">{{ orderInfo.wechatTransactionId }}</span>
          </div>
        </div>

        <!-- 非成功状态的基本信息 -->
        <div v-else-if="orderInfo.amount > 0" class="payment-result__info">
          <div class="payment-result__item">
            <span class="payment-result__label">订单金额</span>
            <span class="payment-result__value">¥{{ orderInfo.amount.toFixed(2) }}</span>
          </div>

          <div v-if="orderInfo.createdAt" class="payment-result__item">
            <span class="payment-result__label">创建时间</span>
            <span class="payment-result__value">{{ formatPaymentTime(orderInfo.createdAt) }}</span>
          </div>
        </div>

        <div class="payment-result__actions">
          <van-button
            v-if="paymentSuccess"
            type="primary"
            block
            @click="backToChat"
          >
            开始聊天
          </van-button>

          <template v-else>
            <van-button
              type="primary"
              block
              @click="backToPayment"
              class="action-btn"
            >
              重新支付
            </van-button>

            <van-button
              v-if="orderStatus === 'pending'"
              type="default"
              block
              @click="fetchOrderInfo"
              class="action-btn"
            >
              刷新状态
            </van-button>
          </template>
        </div>
      </template>
    </div>
  </default-layout>
</template>

<style scoped>
.payment-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 16px;
  min-height: calc(100vh - 100px);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.error-message {
  font-size: 16px;
  color: #666;
  margin: 16px 0 24px 0;
}

.error-btn {
  width: 120px;
}

.payment-result__icon {
  margin-bottom: 16px;
}

.payment-result__status {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 24px;
  text-align: center;
}

.payment-result__info {
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.payment-result__item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.4;
}

.payment-result__item:last-child {
  margin-bottom: 0;
}

.payment-result__label {
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  margin-right: 12px;
}

.payment-result__value {
  font-weight: 500;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.payment-result__value.discount {
  color: #ff6b35;
}

.payment-result__value.amount {
  color: #07c160;
  font-size: 16px;
  font-weight: 600;
}

.payment-result__value.transaction-id {
  font-size: 12px;
  color: #999;
  font-family: monospace;
}

.payment-result__actions {
  width: 100%;
}

.action-btn {
  margin-bottom: 12px;
}

.action-btn:last-child {
  margin-bottom: 0;
}
</style>
