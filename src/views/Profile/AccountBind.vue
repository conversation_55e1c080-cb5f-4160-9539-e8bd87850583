<template>
  <default-layout title="账号与绑定" :show-back="true">
    <div class="account-bind">
      <van-cell-group inset>
        <van-cell
          title="手机"
          :value="phoneDisplay"
          is-link
          @click="handlePhoneBind"
        />
        <van-cell
          title="微信"
          :value="wechatDisplay"
          is-link
          @click="handleWechatBind"
        />
      </van-cell-group>

      <div class="deactivate-section">
        <van-button
          class="deactivate-button"
          block
          @click="handleDeactivateAccount"
        >
          注销账号
        </van-button>
      </div>
    </div>
  </default-layout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { deactivateAccount, unbindPhoneNumber, unbindWechat } from '@/apis/user'
import { useUserStore } from '@/stores/user'
import { ROUTE_NAMES } from '@/constants'

const router = useRouter()
const userStore = useUserStore()

const phoneDisplay = computed(() => {
  const phone = userStore.userInfo?.phone
  return phone ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '绑定手机号'
})

const wechatDisplay = computed(() => {
  const userInfo = userStore.userInfo
  // 检查是否有有效的微信授权
  // 条件：有openid 且 (有访问令牌 或 令牌未过期 或 有昵称)
  const hasValidAuth = userInfo?.wechat_openid && (
    userInfo.wechat_access_token || 
    (userInfo.wechat_token_expires_in && userInfo.wechat_token_expires_in > 0) ||
    userInfo.nickname
  )
  
  if (hasValidAuth) {
    // 优先显示微信昵称，如果没有则显示"已绑定微信"
    return userInfo.nickname || '已绑定微信'
  }
  return '绑定微信账户'
})

const isWechatBound = computed(() => {
  const userInfo = userStore.userInfo
  // 更精确的微信绑定状态检查：需要有openid且有有效的授权信息
  return !!(userInfo?.wechat_openid && (
    userInfo.wechat_access_token || 
    (userInfo.wechat_token_expires_in && userInfo.wechat_token_expires_in > 0) ||
    userInfo.nickname
  ))
})

const handlePhoneBind = async () => {
  const phone = userStore.userInfo?.phone

  if (phone) {
    try {
      await showConfirmDialog({
        title: '确定解绑手机号',
        message: '解绑后将收不到相关消息提醒，是否确认解绑?',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonColor: '#ee0a24'
      })

      await unbindPhoneNumber()
      showToast('手机号解绑成功')
      await userStore.fetchUserInfo()
    } catch (error: any) {
      if (error.message) {
        console.error('解绑手机号失败:', error)
        showToast(error.message || '解绑失败，请重试')
      }
    }
  } else {
    router.push({ name: 'BindPhone' })
  }
}

const handleWechatBind = async () => {
  if (isWechatBound.value) {
    // 已绑定微信，执行解绑操作
    try {
      await showConfirmDialog({
        title: '确定解绑微信',
        message: '解绑后将清空微信授权信息，需要重新登录，是否确认解绑?',
        confirmButtonText: '确定解绑',
        cancelButtonText: '取消',
        confirmButtonColor: '#ee0a24'
      })

      // 调用微信解绑API
      await unbindWechat()
      
      showToast({
        message: '微信授权已清除',
        type: 'success',
        duration: 2000
      })
      
      // 清除用户状态和所有本地存储
      await userStore.logoutAction()
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        router.replace({ name: ROUTE_NAMES.LAUNCH })
      }, 1500)
      
    } catch (error: any) {
      console.error('解绑微信失败:', error)
      
      // 如果是用户取消操作，不显示错误
      if (error.message && !error.message.includes('用户取消')) {
        showToast({
          message: error.message || '解绑失败，请重试',
          type: 'fail',
          duration: 3000
        })
      }
    }
  } else {
    // 未绑定微信，提示或跳转到绑定流程
    showToast({
      message: '微信绑定功能开发中...',
      type: 'loading',
      duration: 2000
    })
  }
}

const handleDeactivateAccount = async () => {
  try {
    await showConfirmDialog({
      title: '确认注销账号',
      message: '确认注销这个账号吗？\n注销后，聊天记录、加时卡、个人信息等都将无法找回，请谨慎操作！',
      confirmButtonText: '确定注销',
      cancelButtonText: '取消',
      confirmButtonColor: '#ee0a24'
    })

    await deactivateAccount()
    showToast('账号注销成功')
    await userStore.logoutAction()
    router.replace({ name: ROUTE_NAMES.LAUNCH })
  } catch (error: any) {
    if (error.message) {
      console.error('注销账号失败:', error)
      showToast(error.message || '注销失败，请重试')
    }
  }
}
</script>

<style scoped>
.account-bind {
  /*
   * 使用 calc() 精确计算可用高度，确保按钮能推到页面底部
   * 100vh - 导航栏高度(46px) - DefaultLayout的padding(16px*2)
   */
  min-height: calc(100vh - 46px - 32px);
  display: flex;
  flex-direction: column;
}

:deep(.van-cell-group--inset) {
  margin: 0;
}

.deactivate-section {
  margin-top: auto;
}

.deactivate-button {
  background-color: white;
  color: #ee0a24;
  border: none;
}

:deep(.deactivate-button .van-button__text) {
  color: #ee0a24;
}
</style>