<template>
    <default-layout title="绑定手机号" :show-back="true">
      <div class="bind-phone-container">
        <!-- 手机号输入步骤 -->
        <div v-if="currentStep === 'phone'" class="step-content">
          <div class="form-section">
            <van-form @submit="handleSendCode">
              <van-field
                v-model="phoneNumber"
                name="phone"
                label="手机"
                placeholder="请输入手机号"
                type="tel"
                maxlength="11"
                :rules="phoneRules"
                class="phone-input"
              />
            </van-form>
          </div>

          <div class="button-section">
            <van-button
              type="primary"
              block
              round
              :loading="sendingCode"
              :disabled="!isPhoneValid || sendingCode"
              @click="handleSendCode"
              class="send-code-btn"
            >
              发送验证码
            </van-button>
          </div>
        </div>

        <!-- 验证码输入步骤 -->
        <div v-else-if="currentStep === 'code'" class="step-content">
          <div class="code-info">
            <p class="code-tip">
              已发送4位验证码至
              <span class="phone-display">{{ maskedPhone }}</span>
            </p>
          </div>

          <div class="form-section code-input-section">
            <verification-code-input
              v-model="verificationCode"
              :length="4"
              @filled="handleBindPhone"
            />
          </div>

          <div class="button-section">
            <van-button
              type="primary"
              block
              round
              :loading="binding"
              :disabled="!isCodeValid || binding"
              @click="handleBindPhone"
              class="bind-btn"
            >
              绑定
            </van-button>

            <div class="resend-section">
              <van-button
                v-if="canResend"
                type="default"
                size="small"
                @click="handleResendCode"
                class="resend-btn"
              >
                重新获取
              </van-button>
              <span v-else class="countdown-text">
                重新获取 ({{ countdown }}s)
              </span>
            </div>
          </div>
        </div>
      </div>
    </default-layout>
    </template>

    <script setup lang="ts">
    import { ref, computed, onUnmounted } from 'vue'
    import { useRouter } from 'vue-router'
    import { showToast, showSuccessToast } from 'vant'
    import DefaultLayout from '@/layouts/DefaultLayout.vue'
    import { sendSmsCode, bindPhoneNumber } from '@/apis/user'
    import { useUserStore } from '@/stores/user'
    import VerificationCodeInput from '@/components/business/Profile/VerificationCodeInput.vue';

    const router = useRouter()
    const userStore = useUserStore()

    // 响应式数据
    const currentStep = ref<'phone' | 'code'>('phone')
    const phoneNumber = ref('')
    const verificationCode = ref('')
    const sendingCode = ref(false)
    const binding = ref(false)
    const countdown = ref(0)
    const countdownTimer = ref<number | null>(null)

    // 计算属性
    const isPhoneValid = computed(() => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phoneNumber.value)
    })

    const isCodeValid = computed(() => {
    return verificationCode.value.length === 4 && /^\d{4}$/.test(verificationCode.value)
    })

    const canResend = computed(() => {
    return countdown.value === 0
    })

    const maskedPhone = computed(() => {
    if (phoneNumber.value.length === 11) {
      return phoneNumber.value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
    return phoneNumber.value
    })

    // 表单验证规则
    const phoneRules = [
    { required: true, message: '请输入手机号' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
    ]

    // 开始倒计时
    const startCountdown = () => {
    countdown.value = 60
    countdownTimer.value = window.setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value!)
        countdownTimer.value = null
      }
    }, 1000)
    }

    // 发送验证码
    const handleSendCode = async () => {
      if (!isPhoneValid.value) {
        showToast('请输入正确的手机号')
        return
      }

      sendingCode.value = true
      try {
        await sendSmsCode(phoneNumber.value)
        showSuccessToast('验证码发送成功')
        currentStep.value = 'code'
        startCountdown()
      } catch (error: any) {
        console.error('发送验证码失败:', error)
        showToast(error.message || '发送验证码失败，请重试')
      } finally {
        sendingCode.value = false
      }
    }

    // 重新发送验证码
    const handleResendCode = async () => {
      if (!canResend.value) return

      try {
        await sendSmsCode(phoneNumber.value)
        showSuccessToast('验证码重新发送成功')
        startCountdown()
      } catch (error: any) {
        console.error('重新发送验证码失败:', error)
        showToast(error.message || '重新发送失败，请重试')
      }
    }

    // 绑定手机号
    const handleBindPhone = async () => {
      if (!isCodeValid.value) {
        showToast('请输入正确的验证码')
        return
      }

      binding.value = true

      try {
        const result = await bindPhoneNumber(phoneNumber.value, verificationCode.value)
        showSuccessToast(result.message || '手机号绑定成功')

        // 更新用户信息
        if (userStore.userInfo) {
          userStore.setUserInfo({
            ...userStore.userInfo,
            phone: phoneNumber.value
          })
        }

        // 清除倒计时
        if (countdownTimer.value) {
          clearInterval(countdownTimer.value)
          countdownTimer.value = null
        }

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          router.back()
        }, 1500)
      } catch (error: any) {
        console.error('绑定手机号失败:', error)
        const errorMsg = error.message || '绑定失败，请重试'
        showToast(errorMsg)
      } finally {
        binding.value = false
      }
    }

    // 清理定时器
    onUnmounted(() => {
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
    }
    })
    </script>

    <style scoped>
    .bind-phone-container {
      padding: 24px 16px;
    }

    .step-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .form-section {
      width: 100%;
      margin-bottom: 0px;
    }

    .phone-input {
      --van-field-label-color: #333;
      --van-field-input-text-color: #333;
      --van-field-placeholder-text-color: #999;
      background: white;
      border-radius: 8px;
      padding: 10px 15px;
    }

    :deep(.phone-input .van-field__label) {
      margin-right: 10px;
    }

    :deep(.phone-input .van-field__control) {
      font-size: 16px;
      text-align: right;
    }

    :deep(.phone-input .van-field__control input) {
      text-align: right;
    }

    .code-info {
      margin-bottom: 24px;
      text-align: center;
    }

    .code-tip {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      margin: 0;
    }

    .phone-display {
      color: #2b314c;
      font-weight: 500;
      font-size: 16px;
    }

    .code-input-section {
      display: flex;
      justify-content: center;
      width: 100%;
      margin-bottom: 0px;
    }

    .button-section {
      margin-top: 32px;
      width: 100%;
      max-width: 400px;
    }

    .send-code-btn,
    .bind-btn {
      background: #7459FF;
      border-color: #7459FF;
      height: 44px;
      font-size: 16px;
      font-weight: 500;
      border-radius: 8px;
    }

    .send-code-btn:disabled,
    .bind-btn:disabled {
      background: #B4A8FF;
      border-color: #B4A8FF;
      opacity: 0.8;
    }

    .resend-section {
      margin-top: 12px;
      text-align: center;
    }

    .resend-btn {
      background: transparent;
      border: none;
      color: #7459FF;
      font-size: 14px;
      padding: 0;
      height: auto;
      min-height: 20px;
      line-height: 20px;
    }

    .resend-btn:disabled {
      color: #B4A8FF;
    }

    .countdown-text {
      font-size: 14px;
      color: #B4A8FF;
      line-height: 20px;
      min-height: 20px;
      display: inline-block;
    }
    </style>
