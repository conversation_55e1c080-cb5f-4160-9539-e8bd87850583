<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { getUserOrders, cancelOrder as cancelOrderAPI } from '@/apis/payment'
import { useUserStore } from '@/stores/user'
import { formatPaymentTime } from '@/utils/date'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import type { OrderInfo, OrderStatus } from '@/types/api'
import emptyImage from '@/assets/images/empty.svg'

const userStore = useUserStore()

const orders = ref<OrderInfo[]>([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const page = ref(1)
const pageSize = 10
const total = ref(0)
const error = ref('')
const listRef = ref()

// 计算属性
const hasOrders = computed(() => orders.value.length > 0)

// 获取订单状态文本
const getStatusText = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    pending: '待支付',
    paid: '支付成功',
    failed: '支付失败',
    cancelled: '已取消',
    refunded: '已退款',
    expired: '已过期'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态样式类
const getStatusClass = (status: OrderStatus): string => {
  const classMap: Record<OrderStatus, string> = {
    pending: 'status-pending',
    paid: 'status-success',
    failed: 'status-failed',
    cancelled: 'status-cancelled',
    refunded: 'status-refunded',
    expired: 'status-expired'
  }
  return classMap[status] || 'status-unknown'
}

// 获取订单记录
const fetchOrders = async (skipAutoCheck = false) => {
  console.log(`fetchOrders调用: finished=${finished.value}, loading=${loading.value}, page=${page.value}, skipAutoCheck=${skipAutoCheck}`)

  // 检查用户是否登录
  if (!userStore.userInfo?.user_id) {
    error.value = '用户未登录'
    return
  }

  // 如果正在刷新，且不是刷新触发的调用，则跳过
  if (refreshing.value && !skipAutoCheck) {
    console.log('正在刷新中，跳过van-list触发的加载')
    return
  }

  try {
    console.log(`正在加载第${page.value}页数据...`)

    const result = await getUserOrders(
      userStore.userInfo.user_id,
      page.value,
      pageSize
    )

    console.log('API返回结果:', result)

    // 处理标准分页格式
    const orderItems = result.items || []
    const totalCount = result.total || 0

    console.log(`当前页数据: ${orderItems.length}条, 总数: ${totalCount}条, 已加载: ${orders.value.length}条`)

    // 添加新数据到列表
    if (orderItems.length > 0) {
      orders.value.push(...orderItems)
      total.value = totalCount

      // 增加页码准备下次请求
      page.value++
      console.log(`数据加载成功，页码增加到: ${page.value}`)
    }

    // 检查是否已加载完所有数据
    if (orderItems.length < pageSize || orders.value.length >= totalCount) {
      finished.value = true
      console.log('数据加载完成，finished设为true')
    }

    // 如果没有数据，也标记为完成
    if (orderItems.length === 0) {
      finished.value = true
      console.log('没有数据，标记为完成')
    }

    console.log(`finished状态: ${finished.value}, 下次请求页码: ${page.value}, 已加载: ${orders.value.length}条`)

    // 在数据加载完成后，手动检查是否需要继续加载（除非明确跳过）
    if (!finished.value && !skipAutoCheck) {
      nextTick(() => {
        console.log('手动触发van-list检查')
        listRef.value?.check()
      })
    }

  } catch (err: any) {
    console.error('获取订单记录失败:', err)
    error.value = err.message || '获取订单记录失败'
    showToast(error.value)
    finished.value = true // 出错时也要停止加载
  } finally {
    // 无论成功还是失败，都要重置loading状态
    loading.value = false
  }
}

// 刷新数据
const onRefresh = async () => {
  console.log('开始刷新数据')

  // 重置状态
  page.value = 1
  orders.value = []
  finished.value = false
  loading.value = false
  error.value = ''

  try {
    // 刷新时跳过fetchOrders中的自动检查，由这里统一处理
    await fetchOrders(true)

    // 刷新后手动检查是否需要加载更多
    if (!finished.value) {
      nextTick(() => {
        console.log('刷新后手动触发van-list检查')
        listRef.value?.check()
      })
    }
  } finally {
    refreshing.value = false
  }
}

// 复制订单号
const copyOrderId = async (orderId: string) => {
  try {
    await navigator.clipboard.writeText(orderId)
    showSuccessToast('订单号已复制')
  } catch (err) {
    console.error('复制失败:', err)
    showToast('复制失败')
  }
}

// 查看订单详情
const viewOrderDetail = (order: OrderInfo) => {
  // 可以跳转到订单详情页面或显示弹窗
  console.log('查看订单详情:', order.order_id)
}

// 取消订单
const cancelOrder = async (order: OrderInfo) => {
  try {
    await showConfirmDialog({
      title: '确认取消',
      message: '确定要取消这个订单吗？',
    })

    // 调用取消订单API
    console.log('正在取消订单:', order.order_id)
    const result = await cancelOrderAPI(order.order_id, '用户主动取消')

    console.log('取消订单结果:', result)
    showSuccessToast('订单取消成功')

    // 更新本地订单状态
    const orderIndex = orders.value.findIndex(o => o.order_id === order.order_id)
    if (orderIndex !== -1) {
      orders.value[orderIndex].status = 'cancelled'
    }
  } catch (error: any) {
    console.error('取消订单失败:', error)
    if (error.message && error.message !== 'cancel') {
      showToast(error.message || '取消订单失败')
    }
    // 如果是用户取消确认对话框，error.message 会是 'cancel'，不显示错误提示
  }
}

// 重新支付
const retryPayment = (order: OrderInfo) => {
  // 跳转到支付页面，可以传递套餐信息
  console.log('重新支付:', order.order_id)
}

onMounted(() => {
  console.log('组件挂载，开始加载数据')
  // 手动触发第一次加载
  fetchOrders()
})
</script>

<template>
  <default-layout title="充值记录" :show-back="true" :fullscreen="!hasOrders && finished">
    <!-- 错误状态 -->
    <div v-if="error && !loading" class="order-history__error">
      <van-empty
        description="加载失败"
        :image="emptyImage"
        image-size="100"
      >
        <div class="error-message">{{ error }}</div>
        <van-button
          type="primary"
          @click="onRefresh"
          class="retry-btn"
        >
          重新加载
        </van-button>
      </van-empty>
    </div>

    <!-- 当记录为空且加载完成时，显示空状态 -->
    <div v-else-if="!hasOrders && finished && !loading" class="order-history__empty">
      <van-empty
        description="当前暂无充值记录"
        :image="emptyImage"
        image-size="100"
      >
        <div class="extend-button">
          <van-button
            type="primary"
            block
            @click="$router.push('/payment')"
            style="width: 100%; margin: 0 auto;"
          >
            延长陪伴
          </van-button>
        </div>
      </van-empty>
    </div>

    <!-- 否则，显示订单历史列表 -->
    <div class="order-history" v-else>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          ref="listRef"
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          :offset="100"
          :immediate-check="false"
          @load="fetchOrders"
        >
          <!-- 订单卡片容器 -->
          <div class="payment-container">
            <div
              class="payment-card"
              v-for="(order, index) in orders"
              :key="`order-${order.id}-${order.order_id}`"
              @click="viewOrderDetail(order)"
            >
              <div class="card-content">
                <div class="card-row card-header">
                  <div class="card-title">{{ order.package_name }}</div>
                  <div class="payment-status" :class="getStatusClass(order.status)">
                    {{ getStatusText(order.status) }}
                  </div>
                </div>

                <!-- 订单详细信息 -->
                <div class="card-details">
                  <!-- 主要金额信息 -->
                  <div class="main-amount">
                    <span class="main-info-label">实付金额</span>
                    <span class="main-amount-value">¥{{ order.amount }}</span>
                  </div>

                  <!-- 次要信息 -->
                  <div class="secondary-info">
                    <div v-if="order.package_duration" class="info-row">
                      <span class="info-label">套餐时长</span>
                      <span class="info-value">{{ order.package_duration }}</span>
                    </div>
                    <div v-if="order.discount_amount && order.discount_amount > 0" class="info-row">
                      <span class="info-label">优惠金额</span>
                      <span class="info-value">-¥{{ order.discount_amount }}</span>
                    </div>
                    <div v-if="order.original_amount && order.original_amount !== order.amount" class="info-row">
                      <span class="info-label">套餐原价</span>
                      <span class="info-value" style="text-decoration: line-through;">¥{{ order.original_amount }}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">创建时间</span>
                      <span class="info-value">{{ formatPaymentTime(order.created_at) }}</span>
                    </div>
                    <div v-if="order.pay_time" class="info-row">
                      <span class="info-label">支付时间</span>
                      <span class="info-value">{{ formatPaymentTime(order.pay_time) }}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">订单号</span>
                      <div class="order-id-group">
                        <span class="info-value">{{ order.order_id }}</span>
                        <button class="copy-btn" @click.stop="copyOrderId(order.order_id)">
                          <span class="copy-icon"></span>复制
                        </button>
                      </div>
                    </div>
                    <div v-if="order.coupon_code" class="info-row">
                      <span class="info-label">兑换码</span>
                      <span class="coupon-code">{{ order.coupon_code }}</span>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div v-if="order.status === 'pending' || order.status === 'failed'" class="card-actions">
                  <van-button
                    v-if="order.status === 'pending'"
                    size="small"
                    type="default"
                    @click.stop="cancelOrder(order)"
                  >
                    取消订单
                  </van-button>
                  <van-button
                    v-if="order.status === 'failed'"
                    size="small"
                    type="primary"
                    @click.stop="retryPayment(order)"
                  >
                    重新支付
                  </van-button>
                </div>
              </div>
              <div class="card-divider" v-if="index < orders.length - 1"></div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
  </default-layout>
</template>

<style scoped>

.order-history__empty,
.order-history__error {
  padding: 16px 0;
  width: 100%;
}

.extend-button {
  margin: 4px auto 0;
  width: 100%;
  max-width: 240px;
}

.error-message {
  color: #ee0a24;
  font-size: 14px;
  margin: 8px 0 16px 0;
  text-align: center;
}

.retry-btn {
  width: 120px;
}

/* 调整van-empty描述文字的样式 */
:deep(.van-empty__description) {
  font-size: 18px;
  color: #1D1D1F;
  font-weight: 400;
}

/* 调整van-empty底部区域的样式 */
:deep(.van-empty__bottom) {
  margin-top: 8px;
  padding: 0 16px;
  min-height: auto;
}

.payment-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.payment-card {
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
}

.payment-card:hover {
  background-color: #f8f9fa;
}

.card-content {
  padding: 16px 20px;
}

.card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header {
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #222222;
}

.payment-status {
  font-size: 14px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-success {
  color: #07c160;
  background-color: #f0f9ff;
}

.status-failed {
  color: #ee0a24;
  background-color: #fff1f0;
}

.status-pending {
  color: #ff976a;
  background-color: #fff7e6;
}

.status-cancelled {
  color: #969799;
  background-color: #f7f8fa;
}

.status-refunded {
  color: #ff976a;
  background-color: #fff7e6;
}

.status-expired {
  color: #969799;
  background-color: #f7f8fa;
}

.card-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 主要金额信息 */
.main-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main-info-label {
  font-size: 14px;
  color: #222;
  flex-shrink: 0;
}

.main-amount-value {
  font-size: 20px;
  font-weight: 600;
  color: #222;
}

/* 次要信息区域 */
.secondary-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.info-label {
  font-size: 12px;
  color: #999;
  flex-shrink: 0;
}

.info-value {
  font-size: 12px;
  color: #999;
  text-align: right;
  word-break: break-all;
}

.order-id-group {
  display: flex;
  align-items: center;
  gap: 2px;
}

.copy-btn {
  display: inline-flex;
  align-items: center;
  background: transparent;
  border: none;
  padding: 2px 4px;
  font-size: 12px;
  color: #999;
  cursor: pointer;
  border-radius: 4px;
  flex-shrink: 0;
}

.copy-btn:hover {
  background-color: #f5f5f5;
  color: #666;
}

.copy-icon {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 2px;
  background-image: url('@/assets/icon/copy.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.6;
}

.coupon-code {
  font-size: 12px;
  color: #999;
}

.card-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.card-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 0 20px;
}

@media (max-width: 480px) {
  .payment-container {
    border-radius: 8px;
    margin: 0 8px;
  }

  .card-content {
    padding: 14px 16px;
  }

  .card-title {
    font-size: 15px;
  }

  .main-info-label {
    font-size: 13px;
  }

  .main-amount-value {
    font-size: 18px;
  }

  .info-label,
  .info-value {
    font-size: 11px;
  }

  .copy-btn {
    padding: 1px 3px;
    font-size: 11px;
  }

  .copy-icon {
    width: 8px;
    height: 8px;
  }

  .coupon-code {
    font-size: 11px;
  }
}
</style>
