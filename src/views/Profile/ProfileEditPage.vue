<template>
  <default-layout title="编辑个人资料" :show-back="true">
    <div class="profile-edit-content">
      <div class="avatar-section">
        <div class="avatar-container" @click="handleAvatarClick">
          <div class="avatar-wrapper">
            <van-image
              round
              width="100"
              height="100"
              :src="form.avatar || defaultAvatar"
              class="avatar"
            />
            <div class="edit-icon-container">
              <img src="@/assets/icon/edit.svg" alt="编辑" class="edit-icon" />
            </div>
          </div>
          <div class="avatar-tip">点击更换头像</div>
          <input
            type="file"
            ref="fileInputRef"
            accept="image/*"
            style="display: none"
            @change="handleFileChange"
          />
        </div>
      </div>

      <van-form @submit="handleSave">
        <van-cell-group>
          <van-field
            v-model="form.nickname"
            name="nickname"
            label="昵称"
            placeholder="请输入昵称"
            :rules="[{ required: true, message: '请输入昵称' }]"
          />

          <van-field
            :model-value="genderOptions.find(item => item.value === form.gender)?.text || '请选择性别'"
            is-link
            readonly
            name="gender"
            label="性别"
            placeholder="请选择性别"
            @click="showGenderPicker = true"
          />

          <van-field
            :model-value="formatBirthday(form.birthday)"
            is-link
            readonly
            name="birthday"
            label="生日"
            placeholder="请选择生日"
            @click="showBirthdayPicker = true"
          />
        </van-cell-group>

        <div class="save-button-container">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
            class="save-button"
          >
            保存
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 性别选择器 -->
    <van-popup v-model:show="showGenderPicker" position="bottom">
      <van-picker
        :columns="genderOptions"
        @confirm="onGenderConfirm"
        @cancel="showGenderPicker = false"
      />
    </van-popup>

    <!-- 生日选择器 -->
    <van-popup v-model:show="showBirthdayPicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        title="选择生日"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onBirthdayConfirm"
        @cancel="showBirthdayPicker = false"
      />
    </van-popup>
  </default-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast } from 'vant'
import { useUserStore } from '@/stores/user'
import defaultAvatarImg from '@/assets/images/user-avtar.png'
import { uploadAvatar, updateUserProfile } from '@/apis/user'
import { HTTP_STATUS } from '@/constants/apiConstants'
import DefaultLayout from '@/layouts/DefaultLayout.vue'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const defaultAvatar = defaultAvatarImg
const fileInputRef = ref<HTMLInputElement | null>(null)

const form = ref({
  nickname: '',
  avatar: '',
  gender: 0, // 0=未知, 1=女性, 2=男性
  birthday: ''
})

// 性别选项
const genderOptions = [
  { text: '未知', value: 0 },
  { text: '女性', value: 1 },
  { text: '男性', value: 2 }
]

// 性别选择相关
const showGenderPicker = ref(false)

// 生日选择相关
const showBirthdayPicker = ref(false)
const minDate = new Date(1950, 0, 1)
const maxDate = new Date()
const currentDate = ref(['2000', '06', '15']) // 默认日期

// 格式化生日显示
const formatBirthday = (date: string) => {
  if (!date) return '请选择生日'

  // 将 YYYY-MM-DD 格式转换为更友好的显示格式
  try {
    const dateObj = new Date(date)
    const year = dateObj.getFullYear()
    const month = dateObj.getMonth() + 1
    const day = dateObj.getDate()
    return `${year}年${month}月${day}日`
  } catch {
    return date // 如果解析失败，返回原始字符串
  }
}

// 性别选择确认
const onGenderConfirm = ({ selectedOptions }: any) => {
  form.value.gender = selectedOptions[0].value
  showGenderPicker.value = false
}

// 生日选择确认
const onBirthdayConfirm = ({ selectedValues }: any) => {
  const [year, month, day] = selectedValues
  form.value.birthday = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
  showBirthdayPicker.value = false
}

// 点击头像触发文件选择
const handleAvatarClick = () => {
  fileInputRef.value?.click()
}

// ========================================
// 头像上传处理函数 - 生产环境就绪
// ========================================
// 处理文件选择变化
// 此函数在生产环境中无需修改，会自动调用真实API
const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const files = input.files

  if (!files || files.length === 0) return

  const file = files[0]

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    showToast('请选择图片文件')
    return
  }

  // 验证文件大小（限制为5MB）
  if (file.size > 5 * 1024 * 1024) {
    showToast('图片大小不能超过5MB')
    return
  }

  // 显示上传中提示
  const loadingToast = showLoadingToast({
    message: '上传中...',
    forbidClick: true,
  })

  try {
    // 调用上传API
    const result = await uploadAvatar(file)

    // 更新表单中的头像URL
    form.value.avatar = result.avatarUrl
    console.log('头像上传成功，新的头像URL:', result.avatarUrl)
    console.log('更新后的form:', form.value)

    showToast('头像上传成功')
  } catch (error: any) {
    console.error('上传头像失败:', error)

    // ========================================
    // 生产环境错误处理 - 根据HTTP状态码显示不同提示
    // ========================================
    // 根据错误类型显示不同提示
    if (error.response?.status === HTTP_STATUS.PAYLOAD_TOO_LARGE) {
      showToast('文件大小超出限制')
    } else if (error.response?.status === HTTP_STATUS.BAD_REQUEST) {
      showToast('文件格式不支持')
    } else if (error.response?.status === HTTP_STATUS.UNAUTHORIZED) {
      showToast('请先登录')
    } else if (error.response?.status === HTTP_STATUS.TOO_MANY_REQUESTS) {
      showToast('上传过于频繁，请稍后再试')
    } else {
      showToast('上传失败，请重试')
    }
    // 如果上传失败，保持本地预览
  } finally {
    // 关闭loading提示
    loadingToast.close()
    // 清空文件输入，以便再次选择同一文件时也能触发change事件
    input.value = ''
  }
}

const handleSave = async () => {
  console.log('保存用户信息:', form.value)
  loading.value = true
  try {
    // 调用API保存用户信息
    await updateUserProfile({
      nickname: form.value.nickname,
      avatar: form.value.avatar,
      gender: form.value.gender,
      birthday: form.value.birthday
    })

    // 更新本地用户信息
    if (userStore.userInfo) {
      userStore.setUserInfo({
        ...userStore.userInfo,
        nickname: form.value.nickname,
        avatar: form.value.avatar,
        gender: form.value.gender,
        birthday: form.value.birthday
      })
    }

    showToast({ type: 'success', message: '保存成功' })

    // 1s延迟跳转，让用户看到成功提示
    setTimeout(() => {
      router.push({ name: 'Chat' })
    }, 1000)
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败' })
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 初始化表单数据
  if (userStore.userInfo) {
    form.value.nickname = userStore.userInfo.nickname || ''
    form.value.avatar = userStore.userInfo.avatar || ''
    form.value.gender = userStore.userInfo.gender || 0
    form.value.birthday = userStore.userInfo.birthday || ''

    // 如果有生日，设置日期选择器的默认值
    if (userStore.userInfo.birthday) {
      const date = new Date(userStore.userInfo.birthday)
      currentDate.value = [
        date.getFullYear().toString(),
        (date.getMonth() + 1).toString().padStart(2, '0'),
        date.getDate().toString().padStart(2, '0')
      ]
    }
  }
  console.log('组件挂载完成，初始化form数据:', form.value)
  console.log('用户信息:', userStore.userInfo)
})

// 监听form变化
watch(form, (newForm) => {
  console.log('Form数据变化:', newForm)
}, { deep: true })
</script>

<style scoped>
.profile-edit-content {
  padding: 20px 0;
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.avatar-container {
  text-align: center;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 12px;
  cursor: pointer;
}

.avatar {
  border: 2px solid #FFFFFF;
}

.edit-icon-container {
  position: absolute;
  bottom: 10%;
  right: 10%;
  width: 20px;
  height: 20px;
  background-color: #FFFFFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edit-icon {
  width: 12px;
  height: 12px;
}

.avatar-tip {
  font-size: 14px;
  color: #666;
}

.save-button-container {
  margin-top: 40px;
}

:deep(.van-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

/* 选择器样式优化 */
:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
}

:deep(.van-picker__toolbar) {
  padding: 16px 20px;
}

:deep(.van-date-picker__toolbar) {
  padding: 16px 20px;
}

:deep(.van-picker__confirm),
:deep(.van-date-picker__confirm) {
  color: #6366f1;
  font-weight: 500;
}

:deep(.van-picker__cancel),
:deep(.van-date-picker__cancel) {
  color: #6b7280;
}

</style>
