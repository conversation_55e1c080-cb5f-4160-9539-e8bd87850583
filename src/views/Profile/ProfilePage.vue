<template>
  <div class="profile-page">
    <van-nav-bar title="个人中心" left-arrow @click-left="goBack" />

    <div class="profile-content">
      <van-empty description="个人中心功能开发中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.back()
}
</script>

<style scoped>
.profile-page {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .profile-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
