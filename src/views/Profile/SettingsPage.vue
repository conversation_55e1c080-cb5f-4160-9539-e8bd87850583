<template>
  <default-layout title="设置" :show-back="true">
    <div class="settings">
      <van-cell-group inset>
        <van-cell title="账号与绑定" is-link to="account-bind" />
      </van-cell-group>

      <div class="logout-section">
        <van-button
          class="logout-button"
          block
          @click="handleLogout"
        >
          退出登录
        </van-button>
      </div>
    </div>
  </default-layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { showConfirmDialog, showSuccessToast } from 'vant'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import { useUserStore } from '@/stores/user'
import { ROUTE_NAMES } from '@/constants'

const router = useRouter()
const userStore = useUserStore()

const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确定退出登录',
      message: '确认退出这个账号吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      confirmButtonColor: '#5F59FF'
    })

    // 用户点击确定后执行退出登录逻辑
    console.log('用户确认退出登录')
    
    // 清除用户状态和本地存储
    await userStore.logoutAction()
    
    // 清除协议同意状态（可选，根据产品需求决定）
    // localStorage.removeItem('agreementStatus')
    
    // 清除微信授权状态
    localStorage.removeItem('wechat_auth_state')
    
    showSuccessToast('退出登录成功')
    
    // 跳转到启动页面
    router.replace({ name: ROUTE_NAMES.LAUNCH })

  } catch (error) {
    // 用户点击取消或关闭弹窗
    console.log('用户取消退出登录')
  }
}
</script>

<style scoped>
.settings {
  /*
   * 使用 calc() 精确计算可用高度，确保按钮能推到页面底部
   * 100vh - 导航栏高度(46px) - DefaultLayout的padding(16px*2)
   */
  min-height: calc(100vh - 46px - 32px);
  display: flex;
  flex-direction: column;
}

:deep(.van-cell-group--inset) {
  margin: 0;
}

.logout-section {
  margin-top: auto;
}

.logout-button {
  background-color: white;
  color: #8B87FF;
  border: none;
}

:deep(.logout-button .van-button__text) {
  color: #8B87FF;
}
</style>
