<template>
  <div class="streaming-voice-test">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>🎤 流式语音识别测试</h2>
      <div class="status-indicators">
        <span class="status-badge" :class="connectionStatus">
          {{ connectionStatusText }}
        </span>
        <span class="status-badge" :class="streamingStatus">
          {{ streamingStatusText }}
        </span>
      </div>
    </div>

    <!-- 用户登录区域 -->
    <div v-if="!userStore.userInfo" class="login-section">
      <div class="login-card">
        <h3>🔑 需要登录才能测试</h3>
        <button @click="loginTestUser" class="login-btn" :disabled="userStore.loading">
          {{ userStore.loading ? '登录中...' : '登录测试用户' }}
        </button>
      </div>
    </div>

    <!-- 主要测试区域 -->
    <div v-else class="test-area">
      <!-- 录音控制区域 -->
      <div class="recording-control">
        <div class="recording-status">
          <div class="status-circle" :class="{ active: isRecording, streaming: isStreamingActive }">
            <div v-if="isRecording" class="pulse-animation"></div>
            🎤
          </div>
          <div class="status-text">
            <div class="primary-status">
              {{ isRecording ? `录音中 ${recordingDuration.toFixed(1)}s` : '准备就绪' }}
            </div>
            <div class="secondary-status">
              {{ isStreamingActive ? '流式识别激活' : '流式识别未激活' }}
            </div>
          </div>
        </div>

        <div class="control-buttons">
          <button
            v-if="!isRecording"
            @click="startRecording"
            class="record-btn start"
            :disabled="!canStartRecording"
          >
            开始录音
          </button>
          <button
            v-else
            @click="stopRecording"
            class="record-btn stop"
          >
            停止录音
          </button>
        </div>
      </div>

      <!-- 实时数据监控 -->
      <div class="data-monitor">
        <h3>📊 实时数据监控</h3>
        <div class="monitor-grid">
          <div class="monitor-item">
            <div class="monitor-label">音频数据包</div>
            <div class="monitor-value">{{ audioPacketCount }}</div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">数据传输率</div>
            <div class="monitor-value">{{ dataTransferRate }} KB/s</div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">WebSocket状态</div>
            <div class="monitor-value">{{ wsConnectionState }}</div>
          </div>
          <div class="monitor-item">
            <div class="monitor-label">最后音频包大小</div>
            <div class="monitor-value">{{ lastAudioPacketSize }} bytes</div>
          </div>
        </div>
      </div>

      <!-- 识别结果显示 -->
      <div class="recognition-results">
        <h3>🔍 识别结果</h3>
        <div class="results-container">
          <div v-if="!currentRecognitionText && !finalRecognitionText" class="no-results">
            暂无识别结果
          </div>
          <div v-if="currentRecognitionText" class="partial-result">
            <div class="result-label">实时识别:</div>
            <div class="result-text">{{ currentRecognitionText }}</div>
          </div>
          <div v-if="finalRecognitionText" class="final-result">
            <div class="result-label">最终结果:</div>
            <div class="result-text">{{ finalRecognitionText }}</div>
            <div class="result-confidence">置信度: {{ recognitionConfidence }}%</div>
          </div>
        </div>
      </div>

      <!-- 消息历史 -->
      <div class="message-history">
        <h3>💬 消息历史</h3>
        <div class="messages-container" ref="messagesContainer">
          <div v-if="messages.length === 0" class="no-messages">
            暂无消息记录
          </div>
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message-item"
            :class="message.role"
          >
            <div class="message-header">
              <span class="message-role">{{ message.role === 'user' ? '用户' : 'AI' }}</span>
              <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              <span v-if="message.type === 'audio'" class="message-type">🎵 语音</span>
            </div>
            <div class="message-content">{{ message.content }}</div>

            <!-- 语音消息播放控件 -->
            <div v-if="message.type === 'audio' && message.audioUrl" class="audio-player">
              <button
                @click="playAudio(message.audioUrl, message.id)"
                class="play-btn"
                :disabled="playingAudioId === message.id"
              >
                {{ playingAudioId === message.id ? '🔊 播放中...' : '▶️ 播放语音' }}
              </button>
              <span class="audio-duration">{{ message.audioDuration }}秒</span>
            </div>

            <div v-if="message.transcription" class="message-transcription">
              识别文本: {{ message.transcription }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 调试日志面板 -->
    <div class="debug-panel" :class="{ collapsed: !showDebugPanel }">
      <div class="debug-header" @click="toggleDebugPanel">
        <span class="debug-title">🔧 调试日志</span>
        <span class="debug-toggle">{{ showDebugPanel ? '▼' : '▲' }}</span>
      </div>
      <div v-if="showDebugPanel" class="debug-content">
        <div class="debug-controls">
          <button @click="clearDebugLogs" class="clear-btn">清空日志</button>
          <label class="auto-scroll-label">
            <input type="checkbox" v-model="autoScrollDebug" />
            自动滚动
          </label>
        </div>
        <div class="debug-logs" ref="debugLogsContainer">
          <div
            v-for="(log, index) in debugLogs"
            :key="index"
            class="debug-log-item"
            :class="log.level"
          >
            <span class="log-time">{{ log.timestamp }}</span>
            <span class="log-level">{{ log.level.toUpperCase() }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="debugLogs.length === 0" class="no-logs">
            暂无调试日志
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { AudioRecorder } from '@/utils/audioRecorder'
import { WebSocketChatClient } from '@/utils/websocketClient'
import { appConfig } from '@/config/env'
import type { ChatMessage } from '@/types/api'

// 用户store
const userStore = useUserStore()

// 核心组件引用
const audioRecorder = ref<AudioRecorder | null>(null)
const wsClient = ref<WebSocketChatClient | null>(null)

// 录音状态
const isRecording = ref(false)
const recordingDuration = ref(0)
const recordingTimer = ref<number | null>(null)

// 流式识别状态
const isStreamingActive = ref(false)
const currentRecognitionText = ref('')
const finalRecognitionText = ref('')
const recognitionConfidence = ref(0)

// 数据监控
const audioPacketCount = ref(0)
const dataTransferRate = ref(0)
const lastAudioPacketSize = ref(0)
const dataTransferHistory = ref<Array<{ timestamp: number, size: number }>>([])

// 消息历史
const messages = ref<ChatMessage[]>([])
const messagesContainer = ref<HTMLElement>()

// 音频播放状态
const playingAudioId = ref<string | null>(null)
const currentAudio = ref<HTMLAudioElement | null>(null)

// 调试日志
const debugLogs = ref<Array<{
  timestamp: string
  level: 'info' | 'warn' | 'error' | 'success'
  message: string
}>>([])
const showDebugPanel = ref(true)
const autoScrollDebug = ref(true)
const debugLogsContainer = ref<HTMLElement>()

// 计算属性
const connectionStatus = computed(() => {
  if (!wsClient.value) return 'disconnected'
  return wsClient.value.isConnected() ? 'connected' : 'disconnected'
})

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '🟢 已连接'
    case 'disconnected': return '🔴 未连接'
    default: return '🟡 连接中'
  }
})

const streamingStatus = computed(() => {
  return isStreamingActive.value ? 'active' : 'inactive'
})

const streamingStatusText = computed(() => {
  return isStreamingActive.value ? '🔄 流式激活' : '⏸️ 流式未激活'
})

const wsConnectionState = computed(() => {
  if (!wsClient.value) return 'NULL'
  const ws = (wsClient.value as any).ws
  if (!ws) return 'NULL'
  
  switch (ws.readyState) {
    case WebSocket.CONNECTING: return 'CONNECTING'
    case WebSocket.OPEN: return 'OPEN'
    case WebSocket.CLOSING: return 'CLOSING'
    case WebSocket.CLOSED: return 'CLOSED'
    default: return 'UNKNOWN'
  }
})

const canStartRecording = computed(() => {
  return audioRecorder.value && wsClient.value && wsClient.value.isConnected()
})

// 调试日志函数
const addDebugLog = (level: 'info' | 'warn' | 'error' | 'success', message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  debugLogs.value.push({
    timestamp,
    level,
    message
  })

  // 限制日志数量
  if (debugLogs.value.length > 200) {
    debugLogs.value.shift()
  }

  // 自动滚动
  if (autoScrollDebug.value) {
    nextTick(() => {
      if (debugLogsContainer.value) {
        debugLogsContainer.value.scrollTop = debugLogsContainer.value.scrollHeight
      }
    })
  }
}

// 清空调试日志
const clearDebugLogs = () => {
  debugLogs.value = []
}

// 切换调试面板
const toggleDebugPanel = () => {
  showDebugPanel.value = !showDebugPanel.value
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 播放音频
const playAudio = async (audioUrl: string, messageId: string) => {
  try {
    addDebugLog('info', `开始播放音频: ${audioUrl}`)

    // 如果有正在播放的音频，先停止
    if (currentAudio.value) {
      currentAudio.value.pause()
      currentAudio.value = null
      playingAudioId.value = null
    }

    // 创建新的音频对象
    const audio = new Audio(audioUrl)
    currentAudio.value = audio
    playingAudioId.value = messageId

    // 设置音频事件监听
    audio.onloadstart = () => {
      addDebugLog('info', '音频开始加载...')
    }

    audio.oncanplay = () => {
      addDebugLog('success', '音频可以播放')
    }

    audio.onplay = () => {
      addDebugLog('success', '音频开始播放')
    }

    audio.onended = () => {
      addDebugLog('success', '音频播放完成')
      playingAudioId.value = null
      currentAudio.value = null
    }

    audio.onerror = (error) => {
      addDebugLog('error', `音频播放错误: ${error}`)
      showToast('音频播放失败')
      playingAudioId.value = null
      currentAudio.value = null
    }

    // 开始播放
    await audio.play()

  } catch (error: any) {
    addDebugLog('error', `播放音频失败: ${error.message}`)
    showToast(`播放失败: ${error.message}`)
    playingAudioId.value = null
    currentAudio.value = null
  }
}

// 计算数据传输率
const updateDataTransferRate = () => {
  const now = Date.now()
  const oneSecondAgo = now - 1000
  
  // 过滤最近1秒的数据
  const recentData = dataTransferHistory.value.filter(item => item.timestamp > oneSecondAgo)
  
  // 计算总字节数
  const totalBytes = recentData.reduce((sum, item) => sum + item.size, 0)
  
  // 转换为KB/s
  dataTransferRate.value = Math.round(totalBytes / 1024 * 10) / 10
  
  // 清理旧数据
  dataTransferHistory.value = dataTransferHistory.value.filter(item => item.timestamp > now - 5000)
}

// 登录测试用户
const loginTestUser = async () => {
  try {
    addDebugLog('info', '开始登录测试用户...')
    showToast('正在登录测试用户...')

    // 使用测试用户登录方法，绕过微信认证（限制用户ID长度为16字符）
    const timestamp = Date.now().toString().slice(-6) // 取时间戳后6位
    const testUserId = `stream${timestamp}` // 最多12字符
    const loginResult = await userStore.testUserLogin(testUserId)

    if (loginResult && loginResult.userInfo) {
      addDebugLog('success', `测试用户登录成功: ${loginResult.userInfo.nickname}`)
      showToast(`登录成功: ${loginResult.userInfo.nickname}`)

      // 登录成功后初始化WebSocket和录音器
      await initializeComponents()
    } else {
      addDebugLog('error', '测试用户登录失败')
      showToast('登录失败，请重试')
    }
  } catch (error: any) {
    addDebugLog('error', `测试用户登录异常: ${error.message}`)
    showToast(`登录失败: ${error.message}`)
  }
}

// 初始化组件
const initializeComponents = async () => {
  try {
    addDebugLog('info', '开始初始化组件...')

    // 初始化WebSocket
    await initWebSocket()

    // 初始化录音器
    await initAudioRecorder()

    addDebugLog('success', '所有组件初始化完成')
  } catch (error: any) {
    addDebugLog('error', `组件初始化失败: ${error.message}`)
  }
}

// 初始化WebSocket
const initWebSocket = async () => {
  try {
    const userId = userStore.userInfo?.user_id
    if (!userId) {
      throw new Error('用户ID不存在')
    }

    addDebugLog('info', `初始化WebSocket连接，用户ID: ${userId}`)

    // 创建WebSocket客户端
    wsClient.value = new WebSocketChatClient({
      url: `${appConfig.wsBaseUrl}/api/v1/airelief/chat/ws/${userId}`,
      userId: userId
    })

    // 设置事件监听
    setupWebSocketEvents()

    // 连接WebSocket
    await wsClient.value.connect()

    addDebugLog('success', 'WebSocket连接成功')
  } catch (error: any) {
    addDebugLog('error', `WebSocket初始化失败: ${error.message}`)
    throw error
  }
}

// 设置WebSocket事件监听
const setupWebSocketEvents = () => {
  if (!wsClient.value) return

  // 连接成功
  wsClient.value.onConnected(() => {
    addDebugLog('success', 'WebSocket连接已建立')
  })

  // 连接断开
  wsClient.value.onDisconnected(() => {
    addDebugLog('warn', 'WebSocket连接已断开')
    isStreamingActive.value = false
  })

  // 连接错误
  wsClient.value.onError((error) => {
    addDebugLog('error', `WebSocket错误: ${error}`)
  })

  // 系统消息处理
  wsClient.value.onSystemMessage((type, data) => {
    addDebugLog('info', `收到系统消息: ${type}`)
    handleWebSocketMessage(type, data)
  })

  // AI消息处理
  wsClient.value.onMessage((message) => {
    addDebugLog('info', `收到AI消息: ${message.content}`)
    // 转换为本地消息格式
    const localMessage: ChatMessage = {
      id: message.message_id || `ai_${Date.now()}`,
      role: message.role,
      content: message.content,
      type: message.message_type || 'text',
      timestamp: Date.now(),
      transcription: message.transcription,
      audioDuration: message.audio_duration
    }
    messages.value.push(localMessage)
    scrollToMessagesBottom()
  })
}

// 处理WebSocket消息
const handleWebSocketMessage = (type: string, data: any) => {
  addDebugLog('info', `收到WebSocket消息: ${type}`)

  // 详细记录消息内容
  if (data && Object.keys(data).length > 0) {
    addDebugLog('info', `消息数据: ${JSON.stringify(data)}`)
  }

  switch (type) {
    case 'streaming_started':
      addDebugLog('success', '流式识别已启动')
      isStreamingActive.value = true
      showToast('流式识别已启动')
      break

    case 'streaming_result':
      addDebugLog('success', `收到流式识别结果: ${JSON.stringify(data)}`)
      handleStreamingResult(data)
      break

    case 'streaming_ended':
      addDebugLog('success', '流式识别已结束')
      isStreamingActive.value = false
      showToast('流式识别已结束')
      break

    case 'voice_message_sent':
      addDebugLog('success', `语音消息已发送: ${JSON.stringify(data)}`)
      // 添加用户消息到历史
      const userMessage: ChatMessage = {
        id: data.message_id || `msg_${Date.now()}`,
        role: 'user',
        content: data.transcription || '语音消息',
        type: 'audio',
        timestamp: Date.now(),
        transcription: data.transcription,
        audioDuration: data.audioDuration,
        audioUrl: data.audioUrl // 重要：包含音频文件URL用于播放
      }
      messages.value.push(userMessage)
      scrollToMessagesBottom()
      break

    case 'error':
      addDebugLog('error', `服务器错误: ${data.message || JSON.stringify(data)}`)
      showToast(`错误: ${data.message || '未知错误'}`)
      break

    default:
      addDebugLog('warn', `未处理的消息类型: ${type}, 数据: ${JSON.stringify(data)}`)
  }
}

// 处理流式识别结果
const handleStreamingResult = (result: any) => {
  const { text, is_final, confidence } = result

  addDebugLog('info', `流式识别结果: ${text} (final: ${is_final})`)

  if (is_final) {
    // 最终结果
    finalRecognitionText.value = text
    recognitionConfidence.value = Math.round((confidence || 0) * 100)
    currentRecognitionText.value = '' // 清空实时结果
    addDebugLog('success', `最终识别结果: ${text}`)
  } else {
    // 实时结果
    currentRecognitionText.value = text
  }
}

// 初始化录音器
const initAudioRecorder = async () => {
  try {
    addDebugLog('info', '初始化录音器...')

    // 创建录音器实例
    audioRecorder.value = new AudioRecorder({
      sampleRate: 16000,
      channels: 1,
      maxDuration: 60,
      minDuration: 1
    })

    // 设置事件监听
    setupAudioRecorderEvents()

    // 请求麦克风权限
    const hasPermission = await audioRecorder.value.requestPermission()
    if (!hasPermission) {
      throw new Error('麦克风权限被拒绝')
    }

    addDebugLog('success', '录音器初始化成功')
  } catch (error: any) {
    addDebugLog('error', `录音器初始化失败: ${error.message}`)
    throw error
  }
}

// 设置录音器事件监听
const setupAudioRecorderEvents = () => {
  if (!audioRecorder.value) return

  // 录音开始
  audioRecorder.value.onStart(() => {
    addDebugLog('success', '录音已开始')
    startRecordingTimer()
  })

  // 录音结束
  audioRecorder.value.onStop((result) => {
    addDebugLog('success', `录音已结束，时长: ${result.duration}s`)
    stopRecordingTimer()
  })

  // 录音错误
  audioRecorder.value.onError((error) => {
    addDebugLog('error', `录音错误: ${error}`)
    stopRecordingTimer()
    isRecording.value = false
  })

  // 实时音频数据
  audioRecorder.value.onRealTimeData((audioData) => {
    handleRealTimeAudioData(audioData)
  })
}

// 处理实时音频数据
const handleRealTimeAudioData = (audioData: string) => {
  if (!isStreamingActive.value || !wsClient.value || !wsClient.value.isConnected()) {
    addDebugLog('warn', `跳过音频数据发送 - 流式激活: ${isStreamingActive.value}, WebSocket连接: ${wsClient.value?.isConnected()}`)
    return
  }

  try {
    // 发送音频数据到WebSocket
    const success = wsClient.value.sendStreamingAudio(audioData)

    if (success) {
      // 更新数据监控
      audioPacketCount.value++
      lastAudioPacketSize.value = Math.round(audioData.length * 0.75) // base64解码后的大小估算

      // 记录数据传输历史
      dataTransferHistory.value.push({
        timestamp: Date.now(),
        size: lastAudioPacketSize.value
      })

      // 更新传输率
      updateDataTransferRate()

      // 每10个包记录一次详细信息，避免日志过多
      if (audioPacketCount.value % 10 === 0) {
        addDebugLog('info', `已发送${audioPacketCount.value}个音频包，最新包大小: ${lastAudioPacketSize.value} bytes`)
      }
    } else {
      addDebugLog('warn', '音频数据发送失败')
    }
  } catch (error: any) {
    addDebugLog('error', `处理实时音频数据失败: ${error.message}`)
  }
}

// 开始录音
const startRecording = async () => {
  try {
    addDebugLog('info', '准备开始录音...')

    // 检查前置条件
    if (!audioRecorder.value) {
      throw new Error('录音器未初始化')
    }

    if (!wsClient.value || !wsClient.value.isConnected()) {
      throw new Error('WebSocket未连接')
    }

    // 重置状态
    currentRecognitionText.value = ''
    finalRecognitionText.value = ''
    recognitionConfidence.value = 0
    audioPacketCount.value = 0
    dataTransferRate.value = 0
    lastAudioPacketSize.value = 0

    // 启动流式识别
    addDebugLog('info', '启动流式识别...')
    const streamingStarted = wsClient.value.startStreamingRecognition()

    if (!streamingStarted) {
      throw new Error('启动流式识别失败')
    }

    // 等待流式识别启动确认（最多等待3秒）
    addDebugLog('info', '等待流式识别启动确认...')

    let waitCount = 0
    const maxWait = 30 // 最多等待3秒（30 * 100ms）

    while (!isStreamingActive.value && waitCount < maxWait) {
      await new Promise(resolve => setTimeout(resolve, 100))
      waitCount++
    }

    if (!isStreamingActive.value) {
      addDebugLog('warn', '流式识别启动确认超时，但继续开始录音')
      showToast('流式识别启动可能有延迟，继续录音')
    } else {
      addDebugLog('success', '流式识别已确认启动')
    }

    // 开始录音
    addDebugLog('info', '开始录音...')
    await audioRecorder.value.startRecording()

    isRecording.value = true
    addDebugLog('success', `录音已开始，流式识别状态: ${isStreamingActive.value ? '已激活' : '未激活'}`)

  } catch (error: any) {
    addDebugLog('error', `开始录音失败: ${error.message}`)
    showToast(`开始录音失败: ${error.message}`)
    isRecording.value = false
  }
}

// 停止录音
const stopRecording = async () => {
  try {
    addDebugLog('info', '准备停止录音...')

    if (!audioRecorder.value || !isRecording.value) {
      addDebugLog('warn', '录音器未在录音状态')
      return
    }

    // 停止录音
    addDebugLog('info', '停止录音...')
    await audioRecorder.value.stopRecording()

    isRecording.value = false

    // 结束流式识别
    if (wsClient.value && wsClient.value.isConnected()) {
      addDebugLog('info', '结束流式识别...')
      wsClient.value.endStreamingRecognition()
    }

    addDebugLog('success', '录音已停止')

  } catch (error: any) {
    addDebugLog('error', `停止录音失败: ${error.message}`)
    showToast(`停止录音失败: ${error.message}`)
    isRecording.value = false
  }
}

// 录音计时器
const startRecordingTimer = () => {
  recordingDuration.value = 0
  recordingTimer.value = window.setInterval(() => {
    recordingDuration.value += 0.1
  }, 100)
}

const stopRecordingTimer = () => {
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value)
    recordingTimer.value = null
  }
}

// 滚动到消息底部
const scrollToMessagesBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 组件挂载
onMounted(async () => {
  addDebugLog('info', '组件已挂载')

  // 如果用户已登录，直接初始化组件
  if (userStore.userInfo) {
    await initializeComponents()
  }
})

// 组件卸载
onUnmounted(() => {
  addDebugLog('info', '组件即将卸载，清理资源...')

  // 停止录音
  if (isRecording.value && audioRecorder.value) {
    audioRecorder.value.cancelRecording()
  }

  // 清理计时器
  stopRecordingTimer()

  // 停止音频播放
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    playingAudioId.value = null
  }

  // 断开WebSocket连接
  if (wsClient.value) {
    wsClient.value.disconnect()
  }

  addDebugLog('info', '资源清理完成')
})
</script>

<style scoped>
.streaming-voice-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面标题 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.page-header h2 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.status-indicators {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-badge.connected {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.status-badge.disconnected {
  background: #ffebee;
  color: #c62828;
  border: 1px solid #f44336;
}

.status-badge.active {
  background: #e3f2fd;
  color: #1565c0;
  border: 1px solid #2196f3;
  animation: pulse-blue 2s infinite;
}

.status-badge.inactive {
  background: #f5f5f5;
  color: #757575;
  border: 1px solid #bdbdbd;
}

/* 登录区域 */
.login-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.login-card h3 {
  margin: 0 0 30px 0;
  color: #333;
  font-size: 20px;
}

.login-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 测试区域 */
.test-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 录音控制区域 */
.recording-control {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.recording-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.status-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  position: relative;
  background: #f5f5f5;
  border: 3px solid #ddd;
  transition: all 0.3s ease;
}

.status-circle.active {
  background: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.status-circle.streaming {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1565c0;
}

.pulse-animation {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid #f44336;
  border-radius: 50%;
  animation: pulse-red 1.5s infinite;
}

.status-text {
  text-align: left;
}

.primary-status {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.secondary-status {
  font-size: 14px;
  color: #666;
}

.control-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.record-btn {
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.record-btn.start {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.record-btn.start:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
}

.record-btn.stop {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
}

.record-btn.stop:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.6);
}

.record-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 数据监控 */
.data-monitor {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.data-monitor h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.monitor-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.monitor-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.monitor-value {
  font-size: 20px;
  font-weight: 700;
  color: #333;
}

/* 识别结果 */
.recognition-results {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.recognition-results h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.results-container {
  min-height: 100px;
}

.no-results {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 0;
}

.partial-result, .final-result {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #2196f3;
}

.final-result {
  border-left-color: #4caf50;
  background: #f1f8e9;
}

.result-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 600;
  text-transform: uppercase;
}

.result-text {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.result-confidence {
  font-size: 12px;
  color: #4caf50;
  font-weight: 600;
}

/* 消息历史 */
.message-history {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.message-history h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.messages-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 15px;
}

.no-messages {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px 0;
}

.message-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #ddd;
}

.message-item.user {
  border-left-color: #2196f3;
  background: #e3f2fd;
}

.message-item.assistant {
  border-left-color: #4caf50;
  background: #f1f8e9;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-role {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
}

.message-time {
  font-size: 11px;
  color: #999;
}

.message-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.message-transcription {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.message-type {
  font-size: 11px;
  color: #2196f3;
  font-weight: 600;
  background: rgba(33, 150, 243, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 音频播放控件 */
.audio-player {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 10px 0;
  padding: 10px;
  background: rgba(33, 150, 243, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.play-btn {
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.play-btn:hover:not(:disabled) {
  background: #1976d2;
  transform: translateY(-1px);
}

.play-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.audio-duration {
  font-size: 11px;
  color: #666;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 调试面板 */
.debug-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.debug-panel.collapsed {
  margin-bottom: 0;
}

.debug-header {
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9ecef;
  user-select: none;
}

.debug-header:hover {
  background: rgba(0, 0, 0, 0.02);
}

.debug-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.debug-toggle {
  font-size: 14px;
  color: #666;
  transition: transform 0.3s ease;
}

.debug-content {
  padding: 20px;
}

.debug-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.clear-btn {
  background: #f44336;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #d32f2f;
}

.auto-scroll-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
}

.debug-logs {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 10px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.debug-log-item {
  display: flex;
  gap: 10px;
  padding: 4px 0;
  font-size: 11px;
  line-height: 1.4;
  border-bottom: 1px solid #e9ecef;
}

.debug-log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.log-level {
  min-width: 60px;
  flex-shrink: 0;
  font-weight: 600;
}

.debug-log-item.info .log-level {
  color: #2196f3;
}

.debug-log-item.success .log-level {
  color: #4caf50;
}

.debug-log-item.warn .log-level {
  color: #ff9800;
}

.debug-log-item.error .log-level {
  color: #f44336;
}

.log-message {
  flex: 1;
  color: #333;
  word-break: break-word;
}

.no-logs {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px 0;
}

/* 动画 */
@keyframes pulse-red {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .streaming-voice-test {
    padding: 15px;
  }

  .recording-status {
    flex-direction: column;
    gap: 15px;
  }

  .status-text {
    text-align: center;
  }

  .monitor-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .control-buttons {
    flex-direction: column;
    align-items: center;
  }

  .debug-controls {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
