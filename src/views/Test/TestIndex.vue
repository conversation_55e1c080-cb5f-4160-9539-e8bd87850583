<template>
  <div class="test-index">
    <van-nav-bar title="测试中心" left-arrow @click-left="$router.back()" />
    
    <div class="test-container">
      <h2>API 测试</h2>
      <div class="api-test-section">
        <van-cell-group>
          <van-cell title="模拟状态" :value="mockStatus" />
          <van-cell title="用户登录状态" :value="userStore.isLoggedIn ? '已登录' : '未登录'" />
          <van-cell title="用户类型" :value="userStore.isNewUser ? '新用户' : '老用户'" />
        </van-cell-group>

        <div class="test-buttons">
          <van-button type="primary" @click="testLogin" :loading="loading">
            测试登录
          </van-button>
          <van-button @click="testGetProfile" :loading="loading">
            获取用户信息
          </van-button>
          <van-button @click="testLogout" :loading="loading">
            测试登出
          </van-button>
          <van-button @click="testFullFlow" :loading="loading" type="success">
            完整流程测试
          </van-button>
          <van-button 
            @click="toggleUserType" 
            :loading="loading"
            :type="userStore.isNewUser ? 'success' : 'warning'"
          >
            切换为{{ userStore.isNewUser ? '老用户' : '新用户' }}
          </van-button>
          <van-button type="warning" @click="resetUserInfo('existing')" :loading="loading">
            重置为老用户
          </van-button>
          <van-button type="warning" @click="resetUserInfo('new')" :loading="loading">
            重置为新用户
          </van-button>
        </div>

        <div class="test-results">
          <van-field
            v-model="testResults"
            type="textarea"
            label="测试结果"
            placeholder="测试结果将显示在这里"
            readonly
            rows="6"
          />
        </div>
      </div>

      <h2>后端连接测试</h2>
      <div class="backend-test-section">
        <van-cell-group>
          <van-cell title="后端连接状态" :value="backendStatus" />
          <van-cell title="最后检查时间" :value="lastCheckTime" />
          <van-cell title="响应时间" :value="responseTime" />
        </van-cell-group>

        <div class="test-buttons">
          <van-button type="primary" @click="testBackendHealth" :loading="healthLoading">
            健康检查
          </van-button>
          <van-button @click="testBackendConnection" :loading="healthLoading">
            测试连接
          </van-button>
          <van-button type="warning" @click="clearBackendResults" :loading="healthLoading">
            清除结果
          </van-button>
        </div>

        <div class="test-results">
          <van-field
            v-model="backendResults"
            type="textarea"
            label="后端测试结果"
            placeholder="后端测试结果将显示在这里"
            readonly
            rows="6"
          />
        </div>
      </div>

      <h2>路由守卫测试</h2>
      <div class="route-test-section">
        <div class="status-section">
          <h3>当前状态</h3>
          <div class="status-grid">
            <div class="status-item">
              <label>登录状态:</label>
              <span :class="userStore.isLoggedIn ? 'success' : 'error'">
                {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
              </span>
            </div>
            <div class="status-item">
              <label>Token:</label>
              <span>{{ userStore.token ? '有效' : '无' }}</span>
            </div>
            <div class="status-item">
              <label>用户类型:</label>
              <span>{{ userStore.isNewUser ? '新用户' : '老用户' }}</span>
            </div>
            <div class="status-item">
              <label>用户信息:</label>
              <span>{{ userStore.userInfo ? '已加载' : '未加载' }}</span>
            </div>
          </div>
        </div>

        <div class="test-section">
          <h3>路由测试</h3>
          <div class="button-grid">
            <van-button type="primary" @click="testRoute('/chat')">
              测试聊天页面
            </van-button>
            <van-button type="primary" @click="testRoute('/profile')">
              测试个人中心
            </van-button>
            <van-button type="primary" @click="testRoute('/onboarding')">
              测试引导页面
            </van-button>
            <van-button type="primary" @click="testRoute('/launch')">
              测试启动页面
            </van-button>
            <van-button type="warning" @click="testRoute('/auth/gowechat')">
              微信引导页面
            </van-button>
          </div>
        </div>

        <div class="action-section">
          <h3>操作测试</h3>
          <div class="button-grid">
            <van-button type="success" @click="simulateLogin">
              模拟登录
            </van-button>
            <van-button type="warning" @click="simulateLogout">
              模拟登出
            </van-button>
            <van-button type="default" @click="clearStorage">
              清除存储
            </van-button>
            <van-button type="default" @click="refreshPage">
              刷新页面
            </van-button>
          </div>
        </div>

        <div class="log-section">
          <h3>测试日志</h3>
          <div class="log-container">
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <h2>会员到期弹窗测试</h2>
      <div class="membership-test-section">
        <van-cell-group>
          <van-cell title="当前剩余时长" :value="formatDuration(userStore.userInfo?.duration || 0)" />
          <van-cell title="会员状态" :value="getMembershipStatus()" />
        </van-cell-group>
        
        <div class="test-buttons">
          <van-button type="danger" @click="setDuration(0)" :loading="loading">
            设为已过期
          </van-button>
          <van-button type="warning" @click="setDuration(12 * 60 * 60)" :loading="loading">
            设为剩余12小时
          </van-button>
          <van-button type="primary" @click="setDuration(24 * 60 * 60)" :loading="loading">
            设为剩余1天
          </van-button>
          <van-button type="success" @click="setDuration(7 * 24 * 60 * 60)" :loading="loading">
            设为剩余7天
          </van-button>
          <van-button @click="testMembershipPopup" type="primary" :loading="loading">
            测试会员弹窗
          </van-button>
        </div>
      </div>

      <h2>订单记录测试</h2>
      <div class="order-test-section">
        <div class="test-buttons">
          <van-button type="danger" @click="clearPaymentRecords" :loading="loading">
            清空充值记录
          </van-button>
          <van-button type="primary" @click="resetPaymentRecords" :loading="loading">
            恢复默认记录
          </van-button>
          <van-button type="default" @click="viewOrderHistory" :loading="loading">
            查看订单记录
          </van-button>
        </div>
      </div>

      <h2>微信引导页测试</h2>
      <div class="wechat-guide-test-section">
        <div class="test-buttons">
          <van-button type="primary" @click="testRoute('/auth/gowechat')" :loading="loading">
            访问微信引导页
          </van-button>
          <van-button type="success" @click="simulateWechatOpen" :loading="loading">
            模拟微信打开
          </van-button>
          <van-button type="warning" @click="testWechatDetection" :loading="loading">
            测试微信环境检测
          </van-button>
        </div>
        
        <van-cell-group>
          <van-cell title="当前浏览器环境" :value="getBrowserInfo()" />
          <van-cell title="是否微信浏览器" :value="isWechatBrowser() ? '是' : '否'" />
          <van-cell title="用户代理" :value="getUserAgent()" />
        </van-cell-group>
      </div>

      <h2>专项测试页面</h2>
      <div class="special-tests">
        <van-button type="primary" @click="$router.push('/test/voice')" block>
          🎤 语音录音测试
        </van-button>
        <van-button type="success" @click="$router.push('/test/streaming-voice')" block>
          🔄 流式语音识别测试
        </van-button>
      </div>

      <h2>快速操作</h2>
      <div class="quick-actions">
        <van-button type="primary" @click="showQuickDialog" block>
          快速测试 Dialog
        </van-button>

        <van-button type="success" @click="showToast('测试 Toast')" block>
          快速测试 Toast
        </van-button>

        <van-button type="warning" @click="testLoading" block>
          快速测试 Loading
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { healthCheck, testConnection } from '@/apis'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const testResults = ref('')
const logs = ref<Array<{ time: string; message: string }>>([])

// 后端连接测试相关变量
const healthLoading = ref(false)
const backendResults = ref('')
const backendStatus = ref('未检查')
const lastCheckTime = ref('')
const responseTime = ref('')

// ===== Mock API 测试部分 =====
const mockStatus = computed(() => {
  return import.meta.env.DEV && !import.meta.env.VITE_API_BASE_URL ? '启用' : '禁用'
})

const addResult = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  testResults.value += `[${timestamp}] ${message}\n`
}

const toggleUserType = () => {
  if (userStore.userInfo) {
    // 直接修改用户信息中的 isNewUser 状态
    userStore.setUserInfo({
      ...userStore.userInfo,
      isNewUser: !userStore.userInfo.isNewUser
    })
    const newType = userStore.isNewUser ? '新用户' : '老用户'
    addResult(`用户类型已切换为: ${newType}`)
    showToast(`已切换为${newType}`)
  } else {
    addResult('无法切换用户类型: 未登录或无用户信息')
    showToast('请先登录')
  }
}

const testLogin = async () => {
  loading.value = true
  try {
    addResult('开始测试登录...')
    addResult(`模拟状态: ${mockStatus.value}`)
    addResult(`环境变量 DEV: ${import.meta.env.DEV}`)
    addResult(`环境变量 VITE_API_BASE_URL: ${import.meta.env.VITE_API_BASE_URL || '未设置'}`)

    // 先清除之前的状态
    addResult('清除之前的登录状态...')
    await userStore.logoutAction()
    
    // 直接测试模拟服务
    const { mockApiResponse } = await import('@/utils/mockService')
    addResult('直接测试模拟服务...')

    // 根据当前用户类型生成测试授权码
    const userType = userStore.isNewUser ? 'new' : 'old'
    const mockCode = `mock_test_${Date.now()}_${userType}`
    addResult(`使用授权码: ${mockCode} (用户类型: ${userType})`)

    try {
      const mockResult = await mockApiResponse('/api/auth/wechat-login', 'POST', { code: mockCode })
      addResult(`模拟服务直接调用成功: ${JSON.stringify(mockResult, null, 2)}`)
    } catch (mockError: any) {
      addResult(`模拟服务直接调用失败: ${mockError.message}`)
    }

    // 通过userStore登录
    const result = await userStore.loginAction(mockCode)

    addResult(`原始返回结果: ${JSON.stringify(result, null, 2)}`)

    if (result) {
      addResult(`登录成功: token=${result.token}`)
      addResult(`用户信息: ${JSON.stringify(result.userInfo, null, 2)}`)
      
      // 验证状态是否正确设置
      addResult(`验证 - userStore.token: ${userStore.token}`)
      addResult(`验证 - userStore.isLoggedIn: ${userStore.isLoggedIn}`)
      addResult(`验证 - userStore.userInfo: ${userStore.userInfo ? '已设置' : '未设置'}`)
      
      showToast('登录测试成功')
    } else {
      addResult('登录失败: 返回结果为空')
    }
  } catch (error: any) {
    addResult(`登录失败: ${error.message}`)
    addResult(`错误堆栈: ${error.stack}`)
    showToast('登录测试失败')
  } finally {
    loading.value = false
  }
}

const testGetProfile = async () => {
  loading.value = true
  try {
    addResult('开始获取用户信息...')
    
    // 诊断检查
    addResult(`当前token状态: ${userStore.token ? '有token' : '无token'}`)
    addResult(`当前token值: ${userStore.token || '空'}`)
    addResult(`用户登录状态: ${userStore.isLoggedIn}`)
    addResult(`当前用户信息: ${userStore.userInfo ? JSON.stringify(userStore.userInfo, null, 2) : 'null'}`)
    
    // 如果没有token，尝试模拟登录
    if (!userStore.token) {
      addResult('检测到无token，先尝试模拟登录...')
      const mockCode = `mock_test_${Date.now()}_auto`
      try {
        const loginResult = await userStore.loginAction(mockCode)
        addResult(`自动登录成功: ${JSON.stringify(loginResult, null, 2)}`)
      } catch (loginError: any) {
        addResult(`自动登录失败: ${loginError.message}`)
        showToast('需要先登录')
        return
      }
    }
    
    // 直接测试模拟API
    addResult('直接测试模拟API获取用户信息...')
    try {
      const { mockDataStore } = await import('@/utils/mockService')
      const directResult = mockDataStore.getCurrentUser()
      addResult(`模拟服务直接调用结果: ${JSON.stringify(directResult, null, 2)}`)
    } catch (error: any) {
      addResult(`模拟服务直接调用失败: ${error.message}`)
    }
    
    // 通过userStore获取
    const userInfo = await userStore.fetchUserInfo()

    if (userInfo) {
      addResult(`用户信息获取成功: ${JSON.stringify(userInfo, null, 2)}`)
      showToast('获取用户信息成功')
    } else {
      addResult('获取用户信息失败: 返回null')
      addResult('可能原因: 1) token无效 2) API调用失败 3) 模拟服务问题')
    }
  } catch (error: any) {
    addResult(`获取用户信息失败: ${error.message}`)
    addResult(`错误堆栈: ${error.stack}`)
    showToast('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

const testLogout = async () => {
  loading.value = true
  try {
    addResult('开始测试登出...')
    await userStore.logoutAction()
    addResult('登出成功')
    showToast('登出测试成功')
  } catch (error: any) {
    addResult(`登出失败: ${error.message}`)
    showToast('登出测试失败')
  } finally {
    loading.value = false
  }
}

const testFullFlow = async () => {
  loading.value = true
  try {
    addResult('=== 开始完整流程测试 ===')
    
    // 1. 清除状态
    addResult('1. 清除当前状态...')
    await userStore.logoutAction()
    addResult(`状态清除后 - token: ${userStore.token || '无'}, userInfo: ${userStore.userInfo || '无'}`)
    
    // 2. 测试登录
    addResult('2. 执行登录...')
    const mockCode = `mock_full_test_${Date.now()}`
    const loginResult = await userStore.loginAction(mockCode)
    addResult(`登录结果: ${JSON.stringify(loginResult, null, 2)}`)
    addResult(`登录后状态 - token: ${userStore.token ? '有' : '无'}, isLoggedIn: ${userStore.isLoggedIn}`)
    
    // 3. 测试获取用户信息
    addResult('3. 获取用户信息...')
    const userInfo = await userStore.fetchUserInfo()
    addResult(`获取用户信息结果: ${userInfo ? JSON.stringify(userInfo, null, 2) : 'null'}`)
    
    // 4. 验证最终状态
    addResult('4. 验证最终状态...')
    addResult(`最终 userStore.userInfo: ${userStore.userInfo ? '有数据' : 'null'}`)
    addResult(`最终 userStore.isLoggedIn: ${userStore.isLoggedIn}`)
    addResult(`最终 userStore.token: ${userStore.token ? '有token' : '无token'}`)
    
    if (userStore.userInfo && userStore.isLoggedIn && userStore.token) {
      addResult('=== 完整流程测试成功 ===')
      showToast('完整流程测试成功')
    } else {
      addResult('=== 完整流程测试失败 - 状态不完整 ===')
      showToast('流程测试失败')
    }
    
  } catch (error: any) {
    addResult(`=== 完整流程测试失败 ===`)
    addResult(`错误信息: ${error.message}`)
    addResult(`错误堆栈: ${error.stack}`)
    showToast('完整流程测试失败')
  } finally {
    loading.value = false
  }
}

// 添加重置用户信息方法
const resetUserInfo = async (userType: 'new' | 'existing' = 'existing') => {
  loading.value = true
  try {
    addResult(`开始重置用户信息为${userType === 'new' ? '新用户' : '老用户'}...`)
    
    // 导入 mockService
    const { mockDataStore } = await import('@/utils/mockService')
    
    // 重置用户信息
    const resetInfo = mockDataStore.resetUserInfo(userType)
    
    // 更新 userStore
    userStore.setUserInfo(resetInfo)
    
    addResult(`用户信息已重置: ${JSON.stringify(resetInfo, null, 2)}`)
    showToast(`已重置为${userType === 'new' ? '新用户' : '老用户'}`)
  } catch (error: any) {
    addResult(`重置用户信息失败: ${error.message}`)
    showToast('重置失败')
  } finally {
    loading.value = false
  }
}

// ===== 后端连接测试部分 =====
const addBackendResult = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  backendResults.value += `[${timestamp}] ${message}\n`
}

// 测试后端健康检查
const testBackendHealth = async () => {
  healthLoading.value = true
  const startTime = Date.now()
  
  try {
    addBackendResult('开始健康检查...')
    addBackendResult('调用API: healthCheck()')
    
    // 调用健康检查API
    const data = await healthCheck()
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    // 更新状态
    backendStatus.value = '连接正常'
    lastCheckTime.value = new Date().toLocaleString()
    responseTime.value = `${duration}ms`
    
    addBackendResult(`健康检查成功！响应时间: ${duration}ms`)
    addBackendResult(`响应数据: ${JSON.stringify(data, null, 2)}`)
    
    showToast({
      type: 'success',
      message: '后端连接正常'
    })
    
  } catch (error: any) {
    const endTime = Date.now()
    const duration = endTime - startTime
    
    // 更新状态
    backendStatus.value = '连接失败'
    lastCheckTime.value = new Date().toLocaleString()
    responseTime.value = `${duration}ms (失败)`
    
    addBackendResult(`健康检查失败: ${error.message}`)
    
    // 处理不同类型的错误
    if (error.name === 'BusinessError') {
      addBackendResult('业务错误: API返回了错误响应')
    } else if (error.message.includes('Network Error') || error.message.includes('timeout')) {
      addBackendResult('网络错误: 无法连接到后端服务器')
      addBackendResult('可能原因: 1) 后端服务未启动 2) 端口配置错误 3) 网络问题')
    } else {
      addBackendResult(`其他错误: ${error.message}`)
    }
    
    showToast({
      type: 'fail',
      message: '后端连接失败'
    })
  } finally {
    healthLoading.value = false
  }
}

// 测试后端基本连接
const testBackendConnection = async () => {
  healthLoading.value = true
  const startTime = Date.now()
  
  try {
    addBackendResult('开始测试连接...')
    addBackendResult('调用API: testConnection()')
    
    // 调用测试连接API
    const data = await testConnection()
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    // 更新状态
    backendStatus.value = '连接正常'
    lastCheckTime.value = new Date().toLocaleString()
    responseTime.value = `${duration}ms`
    
    addBackendResult(`连接测试成功！响应时间: ${duration}ms`)
    addBackendResult(`响应数据: ${JSON.stringify(data, null, 2)}`)
    
    showToast({
      type: 'success',
      message: '后端连接测试成功'
    })
    
  } catch (error: any) {
    const endTime = Date.now()
    const duration = endTime - startTime
    
    // 更新状态
    backendStatus.value = '连接失败'
    lastCheckTime.value = new Date().toLocaleString()
    responseTime.value = `${duration}ms (失败)`
    
    addBackendResult(`连接测试失败: ${error.message}`)
    
    // 处理不同类型的错误
    if (error.name === 'BusinessError') {
      addBackendResult('业务错误: API返回了错误响应')
    } else if (error.message.includes('Network Error') || error.message.includes('timeout')) {
      addBackendResult('网络错误: 无法连接到后端服务器')
      addBackendResult('检查项目: 1) 后端是否在9999端口运行 2) 代理配置是否正确')
    } else {
      addBackendResult(`其他错误: ${error.message}`)
    }
    
    showToast({
      type: 'fail',
      message: '后端连接测试失败'
    })
  } finally {
    healthLoading.value = false
  }
}

// 清除后端测试结果
const clearBackendResults = () => {
  backendResults.value = ''
  backendStatus.value = '未检查'
  lastCheckTime.value = ''
  responseTime.value = ''
  showToast('后端测试结果已清除')
}

// ===== 路由守卫测试部分 =====
const addLog = (message: string) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message
  })
}

const testRoute = (path: string) => {
  addLog(`尝试访问路由: ${path}`)
  router.push(path).catch(error => {
    addLog(`路由跳转失败: ${error.message}`)
  })
}

const simulateLogin = async () => {
  try {
    addLog('开始模拟登录...')
    // 生成模拟的微信授权码
    const mockCode = `mock_wechat_code_${Date.now()}_new`
    const result = await userStore.loginAction(mockCode)

    if (result) {
      addLog('模拟登录成功')
      showToast('登录成功')
    }
  } catch (error: any) {
    addLog(`模拟登录失败: ${error.message}`)
    showToast('登录失败')
  }
}

const simulateLogout = async () => {
  try {
    addLog('开始模拟登出...')
    await userStore.logoutAction()
    addLog('模拟登出成功')
    showToast('登出成功')
  } catch (error: any) {
    addLog(`模拟登出失败: ${error.message}`)
  }
}

const clearStorage = () => {
  localStorage.clear()
  addLog('已清除本地存储')
  showToast('存储已清除')
}

const refreshPage = () => {
  addLog('刷新页面')
  window.location.reload()
}

// ===== 快速操作部分 =====
const showQuickDialog = () => {
  showDialog({
    title: '快速测试',
    message: '这是一个快速测试的对话框',
  }).then(() => {
    showToast('确认')
  })
}

const testLoading = () => {
  const toast = showToast({
    type: 'loading',
    message: '加载中...',
    forbidClick: true,
    duration: 0
  })
  
  setTimeout(() => {
    toast.close()
    showToast({
      type: 'success',
      message: '加载完成'
    })
  }, 2000)
}

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  if (!seconds) return '0分钟'
  
  const days = Math.floor(seconds / (24 * 60 * 60))
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60))
  const minutes = Math.floor((seconds % (60 * 60)) / 60)
  
  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (minutes > 0 || (days === 0 && hours === 0)) result += `${minutes}分钟`
  
  return result
}

// 获取会员状态描述
const getMembershipStatus = (): string => {
  const duration = userStore.userInfo?.duration || 0
  
  if (duration <= 0) {
    return '已过期'
  } else if (duration <= 24 * 60 * 60) {
    return '即将到期（剩余1天内）'
  } else {
    return '正常'
  }
}

// 设置用户剩余时长
const setDuration = async (duration: number) => {
  loading.value = true
  try {
    addResult(`设置用户剩余时长为: ${duration}秒 (${formatDuration(duration)})`)
    
    // 导入 mockService
    const { mockDataStore } = await import('@/utils/mockService')
    
    // 获取当前用户信息
    const currentUser = mockDataStore.getCurrentUser()
    
    // 更新到期时间
    let expiryDate = ''
    if (duration > 0) {
      const now = new Date()
      expiryDate = new Date(now.getTime() + duration * 1000).toISOString()
    }
    
    // 更新用户信息
    const updatedUser = {
      ...currentUser,
      duration: duration,
      expiryDate: expiryDate
    }
    
    // 更新 mockService 中的用户信息
    mockDataStore.setCurrentUser(updatedUser)
    
    // 更新 userStore
    userStore.setUserInfo(updatedUser)
    
    addResult(`用户剩余时长已更新: ${formatDuration(duration)}`)
    showToast(`已设置为${formatDuration(duration)}`)
  } catch (error: any) {
    addResult(`设置用户剩余时长失败: ${error.message}`)
    showToast('设置失败')
  } finally {
    loading.value = false
  }
}

// 测试会员到期弹窗
const testMembershipPopup = () => {
  addResult('跳转到聊天页面测试会员弹窗...')
  router.push('/chat')
}

// 添加清空充值记录的方法
const clearPaymentRecords = async () => {
  loading.value = true
  try {
    addResult('开始清空充值记录...')
    
    // 导入 mockService
    const { mockDataStore } = await import('@/utils/mockService')
    
    // 清空充值记录
    mockDataStore.clearPaymentRecords()
    
    addResult('充值记录已清空')
    showToast('充值记录已清空')
  } catch (error: any) {
    addResult(`清空充值记录失败: ${error.message}`)
    showToast('操作失败')
  } finally {
    loading.value = false
  }
}

// 添加恢复默认充值记录的方法
const resetPaymentRecords = async () => {
  loading.value = true
  try {
    addResult('开始恢复默认充值记录...')
    
    // 导入 mockService
    const { mockDataStore } = await import('@/utils/mockService')
    
    // 恢复默认充值记录
    mockDataStore.resetPaymentRecords()
    
    addResult('充值记录已恢复默认')
    showToast('充值记录已恢复')
  } catch (error: any) {
    addResult(`恢复充值记录失败: ${error.message}`)
    showToast('操作失败')
  } finally {
    loading.value = false
  }
}

// 添加查看订单记录的方法
const viewOrderHistory = () => {
  router.push('/profile/orders')
}

// ===== 微信引导页测试部分 =====
const simulateWechatOpen = () => {
  addLog('模拟微信打开操作...')
  showToast('模拟微信打开')
}

const testWechatDetection = () => {
  const isWechat = isWechatBrowser()
  const userAgent = getUserAgent()
  const browserInfo = getBrowserInfo()
  
  addLog(`微信环境检测结果: ${isWechat ? '是微信浏览器' : '非微信浏览器'}`)
  addLog(`浏览器信息: ${browserInfo}`)
  addLog(`用户代理: ${userAgent}`)
  
  showToast(`${isWechat ? '检测到微信环境' : '非微信环境'}`)
}

const isWechatBrowser = (): boolean => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

const getUserAgent = (): string => {
  return navigator.userAgent
}

const getBrowserInfo = (): string => {
  const ua = navigator.userAgent
  if (ua.includes('MicroMessenger')) {
    return '微信浏览器'
  } else if (ua.includes('Chrome')) {
    return 'Chrome'
  } else if (ua.includes('Safari')) {
    return 'Safari'
  } else if (ua.includes('Firefox')) {
    return 'Firefox'
  } else {
    return '其他浏览器'
  }
}

onMounted(() => {
  addLog('测试页面已加载')
  addLog(`当前路由: ${router.currentRoute.value.path}`)
})
</script>

<style scoped>
.test-index {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.test-container {
  padding: 16px;
}

.test-container h2 {
  margin: 24px 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  padding-left: 4px;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
}

.test-results {
  margin-top: 16px;
}

.api-test-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.backend-test-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.route-test-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.status-section,
.test-section,
.action-section {
  margin-bottom: 16px;
}

.status-section h3,
.test-section h3,
.action-section h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #323233;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.status-item {
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
  padding: 8px;
  border-radius: 4px;
}

.status-item label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 4px;
}

.status-item span {
  font-size: 14px;
  color: #323233;
}

.status-item span.success {
  color: #07c160;
}

.status-item span.error {
  color: #ee0a24;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.log-section {
  margin-top: 16px;
}

.log-container {
  background-color: #f7f8fa;
  border-radius: 4px;
  padding: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  font-size: 12px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.log-time {
  color: #969799;
  margin-right: 8px;
}

.log-message {
  color: #323233;
}

.special-tests {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.membership-test-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
}

.order-test-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.wechat-guide-test-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}
</style>
