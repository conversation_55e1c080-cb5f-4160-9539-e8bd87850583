<template>
  <div class="voice-test-page">
    <!-- 简化的控制面板 -->
    <div class="control-panel">
      <div class="mode-selectors">
        <select v-model="recordingMode" class="mode-select">
          <option value="browser">🌐 浏览器录音（实时流）</option>
          <option value="wechat">💬 微信录音</option>
        </select>
        <select v-model="recognitionMode" class="mode-select">
          <option value="streaming" :disabled="recordingMode !== 'browser'">🔄 流式识别</option>
          <option value="server">🖥️ 服务器识别</option>
          <option value="client" :disabled="recordingMode !== 'wechat'">📱 客户端识别</option>
          <option value="none">❌ 不识别</option>
        </select>
      </div>

      <!-- 状态指示器 -->
      <div class="status-bar">
        <span class="status-item" :class="audioRecorder ? 'ready' : 'not-ready'">
          {{ audioRecorder ? '✅ 录音就绪' : '⏳ 初始化中' }}
        </span>
        <span v-if="isStreamingActive" class="status-item streaming">
          🔄 流式识别中
        </span>
        <span v-if="!userStore.userInfo" class="status-item not-ready">
          ❌ 未登录
        </span>
        <span v-else class="status-item ready">
          👤 {{ userStore.userInfo.nickname }}
        </span>
      </div>

      <!-- 登录按钮 -->
      <div v-if="!userStore.userInfo" class="login-section">
        <button @click="loginTestUser" class="login-btn" :disabled="userStore.loading">
          {{ userStore.loading ? '登录中...' : '🔑 登录测试用户' }}
        </button>
      </div>
    </div>

    <!-- 消息列表 - 优先展示 -->
    <div class="message-container">
      <div class="message-list" ref="messageListRef">
        <div v-if="messages.length === 0" class="empty-message">
          🎤 开始录音测试吧！
        </div>
        <MessageItem
          v-for="(message, index) in messages"
          :key="index"
          :message="message"
        />
      </div>
    </div>

    <!-- 录音控制区域 -->
    <div class="voice-input-area">
      <button
        v-if="!isRecording"
        @click="startRecording"
        class="voice-button start"
        :disabled="!audioRecorder"
      >
        <div class="mic-icon">🎤</div>
        <div class="button-text">开始录音</div>
      </button>

      <button
        v-else
        @click="stopRecording"
        class="voice-button stop"
      >
        <div class="recording-animation">
          <div class="pulse"></div>
          <div class="mic-icon">🎤</div>
        </div>
        <div class="button-text">
          <div class="recording-text">录音中 {{ recordingDuration.toFixed(1) }}s</div>
          <div class="stop-text">点击停止</div>
        </div>
      </button>
    </div>

    <!-- 控制台日志区域 -->
    <div class="console-panel" v-if="showConsole">
      <div class="console-header">
        <span class="console-title">🔍 控制台日志</span>
        <div class="console-controls">
          <button @click="clearLogs" class="clear-btn">清空</button>
          <button @click="showConsole = false" class="close-btn">隐藏</button>
        </div>
      </div>
      <div class="console-logs" ref="consoleLogsRef">
        <div
          v-for="(log, index) in consoleLogs"
          :key="index"
          :class="['log-item', `log-${log.level}`]"
        >
          <span class="log-timestamp">{{ log.timestamp }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        <div v-if="consoleLogs.length === 0" class="no-logs">
          暂无日志输出
        </div>
      </div>
    </div>

    <!-- 显示控制台按钮 -->
    <div v-if="!showConsole" class="show-console-btn" @click="showConsole = true">
      📋 显示控制台
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted, watch } from 'vue'
import { showToast } from 'vant'
import MessageItem from '@/components/business/Chat/MessageItem.vue'
import { UniversalRecorder } from '@/utils/universalRecorder'
import { isWechatBrowser, initWeChatJSSDK } from '@/utils/wechat'
import { appConfig } from '@/config/env'
import { useUserStore } from '@/stores/user'
import type { ChatMessage } from '@/types/api'
import { WebSocketChatClient } from '@/utils/websocketClient'

// 用户store
const userStore = useUserStore()

// 响应式数据
const audioRecorder = ref<UniversalRecorder | null>(null)
const isRecording = ref(false)
const recordingDuration = ref(0)
const messages = ref<ChatMessage[]>([])
const messageListRef = ref<HTMLElement>()

// 录音模式：browser(浏览器录音), wechat(微信录音)
const recordingMode = ref<'browser' | 'wechat'>('browser')

// 识别模式：client(客户端), server(服务器端), streaming(流式识别), none(不识别)
const recognitionMode = ref<'client' | 'server' | 'streaming' | 'none'>('streaming')

// 控制台日志
const consoleLogs = ref<Array<{
  timestamp: string
  level: 'log' | 'info' | 'warn' | 'error'
  message: string
}>>([])
const showConsole = ref(true)
const maxLogs = 100

// 服务器端语音识别状态
const serverRecognitionStatus = ref<any>(null)

// 流式识别相关状态
const isStreamingActive = ref(false)
const wsClient = ref<WebSocketChatClient | null>(null)
const currentStreamingMessage = ref<ChatMessage | null>(null)

// 滚动到底部
const scrollToBottom = () => {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 日志记录函数
const addLog = (level: 'log' | 'info' | 'warn' | 'error', message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  consoleLogs.value.push({
    timestamp,
    level,
    message
  })

  // 限制日志数量
  if (consoleLogs.value.length > maxLogs) {
    consoleLogs.value.shift()
  }

  // 自动滚动到底部
  nextTick(() => {
    const consoleElement = document.querySelector('.console-logs')
    if (consoleElement) {
      consoleElement.scrollTop = consoleElement.scrollHeight
    }
  })
}

// 重写console方法
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error
}

console.log = (...args) => {
  originalConsole.log(...args)
  addLog('log', args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '))
}

console.info = (...args) => {
  originalConsole.info(...args)
  addLog('info', args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '))
}

console.warn = (...args) => {
  originalConsole.warn(...args)
  addLog('warn', args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '))
}

console.error = (...args) => {
  originalConsole.error(...args)
  addLog('error', args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '))
}

// 清空日志
const clearLogs = () => {
  consoleLogs.value = []
}

// 登录测试用户
const loginTestUser = async () => {
  try {
    console.log('🔑 开始登录测试用户...')
    showToast('正在登录测试用户...')

    // 使用测试用户登录方法，绕过微信认证（限制用户ID长度为16字符）
    const timestamp = Date.now().toString().slice(-6) // 取时间戳后6位
    const testUserId = `voice${timestamp}` // 最多11字符
    const loginResult = await userStore.testUserLogin(testUserId)

    if (loginResult && loginResult.userInfo) {
      console.log('✅ 测试用户登录成功:', loginResult.userInfo)
      showToast(`登录成功: ${loginResult.userInfo.nickname}`)

      // 登录成功后初始化WebSocket
      initWebSocket()
    } else {
      console.error('❌ 测试用户登录失败')
      showToast('登录失败，请重试')
    }
  } catch (error: any) {
    console.error('❌ 测试用户登录异常:', error)
    showToast(`登录失败: ${error.message}`)
  }
}

// 检查服务器端语音识别状态
const checkServerRecognitionStatus = async () => {
  try {
    const isWechat = isWechatBrowser()
    const response = await fetch(`${appConfig.apiBaseUrl}/api/v1/airelief/chat/voice-recognition/config?is_wechat_env=${isWechat}`)
    const result = await response.json()

    if (result.code === 200) {
      serverRecognitionStatus.value = result.data
      console.log('🔍 服务器语音识别配置:', result.data)
      console.log('🎯 推荐模式:', result.data.recommended_mode)
    } else {
      console.error('❌ 获取服务器语音识别配置失败:', result.msg)
    }
  } catch (error) {
    console.error('❌ 检查服务器语音识别配置异常:', error)
  }
}

// 初始化WebSocket连接
const initWebSocket = () => {
  try {
    // 获取用户ID，优先使用store中的用户信息
    const userId = userStore.userInfo?.user_id
    if (!userId) {
      console.error('❌ 没有用户信息，无法建立WebSocket连接')
      showToast('请先登录')
      return
    }

    console.log('🔌 使用用户信息:', {
      user_id: userId,
      wechat_openid: userStore.userInfo?.wechat_openid,
      nickname: userStore.userInfo?.nickname
    })

    // 创建WebSocket客户端配置
    const wsConfig = {
      url: `${appConfig.wsBaseUrl}/api/v1/airelief/chat/ws/${userId}`,
      userId: userId
    }

    // 创建WebSocket客户端
    wsClient.value = new WebSocketChatClient(wsConfig)

    // 设置连接成功回调
    wsClient.value.onConnected(() => {
      console.log('✅ WebSocket连接成功')
      console.log('🔍 连接详情:')
      console.log('  - URL:', wsConfig.url)
      console.log('  - 用户ID:', wsConfig.userId)
      console.log('  - 连接状态:', wsClient.value?.isConnected())
      showToast('WebSocket连接成功')
    })

    // 设置断开连接回调
    wsClient.value.onDisconnected(() => {
      console.log('🔌 WebSocket连接关闭')
    })

    // 设置错误回调
    wsClient.value.onError((error) => {
      console.error('❌ WebSocket连接错误:', error)
      showToast('WebSocket连接失败')
    })

    // 设置系统消息回调（处理流式识别消息）
    wsClient.value.onSystemMessage((type, data) => {
      handleWebSocketMessage({ type, data })
    })

    // 连接WebSocket
    wsClient.value.connect()

  } catch (error) {
    console.error('❌ 初始化WebSocket失败:', error)
  }
}

// 处理WebSocket消息
const handleWebSocketMessage = (data: any) => {
  console.log('📨 收到WebSocket消息:', data)

  switch (data.type) {
    case 'streaming_started':
      console.log('🎤 流式识别已开始')
      isStreamingActive.value = true
      console.log('✅ 设置 isStreamingActive = true')
      showToast('流式识别已开始')
      break

    case 'streaming_result':
      console.log('🔍 流式识别结果:', data.data)
      handleStreamingResult(data.data)
      break

    case 'streaming_ended':
      console.log('🛑 流式识别已结束')
      isStreamingActive.value = false
      console.log('✅ 设置 isStreamingActive = false')
      showToast('流式识别已结束')
      break

    case 'error':
      console.error('❌ 服务器错误:', data.data.message)
      showToast(`错误: ${data.data.message}`)
      break

    default:
      console.log('📨 其他消息类型:', data.type, '数据:', data.data)
  }
}

// 处理流式识别结果
const handleStreamingResult = (result: any) => {
  const { text, is_final } = result

  if (currentStreamingMessage.value) {
    if (is_final) {
      // 最终结果
      currentStreamingMessage.value.content = text
      currentStreamingMessage.value.transcription = text
      console.log('✅ 流式识别最终结果:', text)
      showToast(`识别完成: ${text}`)
    } else {
      // 部分结果
      currentStreamingMessage.value.content = text
      console.log('🔄 流式识别部分结果:', text)
    }
  }
}



// 开始流式识别
const startStreamingRecognition = () => {
  console.log('🔍 检查WebSocket连接状态...')
  console.log('wsClient.value:', wsClient.value)
  console.log('wsClient.value?.isConnected():', wsClient.value?.isConnected())

  if (!wsClient.value || !wsClient.value.isConnected()) {
    console.error('❌ WebSocket未连接')
    console.log('🔍 WebSocket状态详情:')
    console.log('  - wsClient存在:', !!wsClient.value)
    console.log('  - 连接状态:', wsClient.value?.isConnected())
    showToast('WebSocket未连接，请检查连接状态')
    return false
  }

  try {
    // 使用WebSocketChatClient发送开始流式识别消息
    const success = wsClient.value.startStreamingRecognition()

    if (!success) {
      console.error('❌ 发送流式识别开始消息失败')
      showToast('开始流式识别失败')
      return false
    }

    // 创建流式识别消息
    currentStreamingMessage.value = {
      id: `streaming-${Date.now()}`,
      role: 'user',
      content: '正在识别...',
      type: 'audio',
      timestamp: Date.now(),
      transcription: ''
    }

    messages.value.push(currentStreamingMessage.value)
    nextTick(() => scrollToBottom())

    console.log('🎤 开始流式识别')
    return true

  } catch (error) {
    console.error('❌ 开始流式识别失败:', error)
    showToast('开始流式识别失败')
    return false
  }
}

// 发送流式音频数据
const sendStreamingAudio = (audioData: string) => {
  if (!wsClient.value || !wsClient.value.isConnected()) {
    console.warn('⚠️ WebSocket未连接，无法发送音频数据')
    return false
  }

  try {
    // 使用WebSocketChatClient发送流式音频数据
    return wsClient.value.sendStreamingAudio(audioData)

  } catch (error) {
    console.error('❌ 发送流式音频数据失败:', error)
    return false
  }
}



// 结束流式识别
const endStreamingRecognition = () => {
  if (!wsClient.value || !wsClient.value.isConnected()) {
    return false
  }

  try {
    // 使用WebSocketChatClient结束流式识别
    const success = wsClient.value.endStreamingRecognition()
    console.log('🛑 结束流式识别')
    return success

  } catch (error) {
    console.error('❌ 结束流式识别失败:', error)
    return false
  }
}

// 注意：流式识别模式下，音频数据通过实时流发送到后端
// 后端会自动缓冲和合并所有音频片段，无需在录音结束后重复发送完整音频

// 初始化语音录制器
const initAudioRecorder = async () => {
  try {
    console.log('🎤 开始初始化语音录制器...')
    console.log('🎤 录音模式:', recordingMode.value)

    // 根据录音模式决定初始化策略
    if (recordingMode.value === 'wechat') {
      // 微信录音模式
      if (isWechatBrowser()) {
        try {
          console.log('🔧 检测到微信环境，初始化JS-SDK...')
          await initWeChatJSSDK()
          console.log('✅ 微信JS-SDK初始化成功')
          showToast('微信JS-SDK初始化成功')
        } catch (error) {
          console.warn('⚠️ 微信JS-SDK初始化失败，切换到浏览器录音:', error)
          showToast('微信JS-SDK初始化失败，切换到浏览器录音')
          recordingMode.value = 'browser'
        }
      } else {
        console.warn('⚠️ 非微信环境，切换到浏览器录音')
        showToast('非微信环境，切换到浏览器录音')
        recordingMode.value = 'browser'
      }
    }

    // 检查录音支持情况
    const support = await UniversalRecorder.checkSupport()
    console.log('🔍 录音支持情况:', support)

    if (support.recommended === 'none') {
      console.warn('❌ 当前环境不支持录音功能')
      showToast('当前环境不支持录音功能')
      return
    }

    audioRecorder.value = new UniversalRecorder({
      maxDuration: 60,
      minDuration: 1,
      autoUploadWechat: recordingMode.value === 'wechat', // 只有微信模式才自动上传
      forceBrowser: recordingMode.value === 'browser' // 强制使用浏览器录音
    })

    // 初始化录音器
    await audioRecorder.value.initialize()

    // 设置事件回调
    audioRecorder.value.onStart(() => {
      isRecording.value = true
      recordingDuration.value = 0
      console.log('🎤 录音开始')

      // 如果是流式识别模式，开始流式识别
      if (recognitionMode.value === 'streaming') {
        startStreamingRecognition()
      }
    })

    audioRecorder.value.onStop(async (result) => {
      console.log('🎤 录音完成，结果:', result)
      console.log('🎤 录音提供者:', result.provider)
      console.log('🎤 localId:', result.localId)
      console.log('🎤 serverId:', result.serverId)

      // 立即更新录音状态
      isRecording.value = false
      recordingDuration.value = 0

      // 如果是流式识别模式，只需要结束流式识别
      // 后端会自动合并音频片段并保存完整音频
      if (recognitionMode.value === 'streaming') {
        endStreamingRecognition()
        console.log('🎤 流式识别模式：音频数据已通过实时流发送，无需重复发送')
      } else {
        // 处理语音消息
        await handleVoiceRecordingComplete(result)
      }
    })

    audioRecorder.value.onError((error) => {
      isRecording.value = false
      recordingDuration.value = 0
      console.error('❌ 录音错误:', error)
      showToast(`录音失败: ${error}`)
    })

    audioRecorder.value.onDataAvailable((duration) => {
      recordingDuration.value = duration
    })

    // 设置实时音频数据回调（仅浏览器录音支持）
    audioRecorder.value.onRealTimeData((audioData: string) => {
      console.log(`🎵 收到实时音频数据: ${audioData.length} bytes, 模式: ${recognitionMode.value}, 激活状态: ${isStreamingActive.value}`)

      // 如果是流式识别模式且流式识别已激活，发送音频数据
      if (recognitionMode.value === 'streaming' && isStreamingActive.value) {
        // 先发送一个小的测试消息
        if (audioData.length > 1000) {
          console.log(`📤 发送流式音频数据: ${audioData.length} bytes`)
          const success = sendStreamingAudio(audioData)
          console.log(`📤 发送结果: ${success}`)
        } else {
          console.log(`⏭️ 跳过小数据包: ${audioData.length} bytes`)
        }
      }
    })

    const status = audioRecorder.value.getStatus()
    console.log(`🎤 录音器初始化成功，推荐方式: ${support.recommended}，实际使用: ${status.provider}`)
    showToast(`录音器初始化成功，使用${status.provider}录音`)
    
  } catch (error) {
    console.error('❌ 录音器初始化失败:', error)
    showToast('录音功能初始化失败')
  }
}

// 处理语音录音完成
const handleVoiceRecordingComplete = async (result: any) => {
  console.log('🎯 进入 handleVoiceRecordingComplete，参数:', result)
  console.log('🎯 当前识别模式:', recognitionMode.value)

  try {
    let transcription = ''
    const isWechat = isWechatBrowser()

    // 根据选择的识别模式进行处理
    if (recognitionMode.value === 'client') {
      // 客户端识别（微信）
      if (result.provider === 'wechat' && result.localId && audioRecorder.value) {
        try {
          console.log('🔍 开始客户端语音识别，localId:', result.localId)
          showToast('正在识别语音内容...')

          transcription = await audioRecorder.value.translateVoice(result.localId)
          console.log('✅ 客户端语音识别成功:', transcription)
          showToast(`客户端识别结果: ${transcription}`)

        } catch (error: any) {
          console.warn('⚠️ 客户端语音识别失败:', error)
          showToast('客户端语音识别失败，但录音已保存')
        }
      } else {
        console.log('❌ 不满足客户端语音识别条件')
        showToast('客户端识别仅支持微信环境')
      }
    } else if (recognitionMode.value === 'server') {
      // 服务器端识别 - 使用新的语音识别模式
      console.log('🔍 使用新的语音识别模式进行服务器端识别')
      showToast('正在发送到服务器识别...')

      try {
        // 构建发送数据，支持新的语音识别模式
        const voiceData = {
          audio_data: result.provider === 'browser' ? result.audioData : (result.serverId || result.localId),
          duration: Math.round(result.duration),
          recognition_mode: 'server', // 强制使用服务器端识别
          client_recognition: undefined, // 测试页面不使用客户端识别结果
          is_wechat_env: isWechat,
          audio_format: result.provider === 'wechat' ? 'amr' : 'wav'
        }

        console.log('📤 发送语音数据到服务器:', {
          ...voiceData,
          audio_data: voiceData.audio_data.substring(0, 50) + '...' // 只显示前50个字符
        })

        // 通过WebSocket发送语音消息进行识别
        if (wsClient.value?.isConnected()) {
          wsClient.value.sendVoiceMessage(voiceData.audio_data, voiceData.duration, {
            recognition_mode: voiceData.recognition_mode,
            client_recognition: voiceData.client_recognition,
            is_wechat_env: voiceData.is_wechat_env,
            audio_format: voiceData.audio_format
          })

          transcription = '已发送到服务器进行识别，请等待结果...'
          console.log('✅ 语音数据已发送到服务器')
          showToast('语音数据已发送到服务器识别')
        } else {
          // 如果WebSocket未连接，使用模拟识别
          console.log('⚠️ WebSocket未连接，使用模拟识别')
          await new Promise(resolve => setTimeout(resolve, 1000))

          const mockResults = [
            '你好，这是服务器端识别的结果',
            '语音识别功能正常工作',
            '测试语音消息内容',
            '火山引擎识别效果很好'
          ]
          transcription = mockResults[Math.floor(Math.random() * mockResults.length)]
          console.log('✅ 模拟服务器端语音识别成功:', transcription)
          showToast(`模拟识别结果: ${transcription}`)
        }

      } catch (error: any) {
        console.error('❌ 服务器端识别失败:', error)
        showToast('服务器端识别失败')
        transcription = '识别失败'
      }
    } else if (recognitionMode.value === 'streaming') {
      // 流式识别模式，识别结果已经在流式处理中完成
      console.log('🔄 流式识别模式，跳过后处理')
      if (currentStreamingMessage.value) {
        transcription = currentStreamingMessage.value.content || ''
      }
      showToast('流式识别已完成')
    } else {
      // 不进行识别
      console.log('🔇 跳过语音识别')
      showToast('录音完成，未进行识别')
    }

    // 添加语音消息到界面
    const message: ChatMessage = {
      id: `voice-${Date.now()}`,
      role: 'user',
      content: transcription || '',
      type: 'audio',
      timestamp: Date.now(),
      audioDuration: result.duration,
      transcription: transcription || undefined,
      localId: result.localId // 保存localId用于播放
    }

    messages.value.push(message)
    console.log('📝 添加语音消息到界面:', message)
    
    nextTick(() => scrollToBottom())
    
  } catch (error) {
    console.error('❌ 处理语音录音失败:', error)
    showToast('语音处理失败')
  }
}

// 录音控制函数

// 开始录音
const startRecording = async () => {
  if (!audioRecorder.value) {
    showToast('录音功能不可用')
    return
  }

  if (isRecording.value) {
    return // 已在录音中
  }

  try {
    console.log('🎤 开始录音...')

    // 请求录音权限
    const hasPermission = await audioRecorder.value.requestPermission()
    if (!hasPermission) {
      showToast('需要录音权限才能使用语音功能')
      return
    }

    await audioRecorder.value.startRecording()
  } catch (error: any) {
    console.error('❌ 开始录音失败:', error)

    // 显示具体的错误信息
    const errorMessage = error.message || '录音启动失败'
    showToast(errorMessage)

    // 根据不同的错误类型提供相应的解决方案
    if (error.message) {
      if (error.message.includes('权限被拒绝')) {
        setTimeout(() => {
          showToast('请在微信设置中允许录音权限，或重新进入页面')
        }, 2000)
      } else if (error.message.includes('权限验证中')) {
        setTimeout(() => {
          showToast('请稍等片刻后重试，或刷新页面重新加载')
        }, 2000)
      } else if (error.message.includes('API不可用')) {
        setTimeout(() => {
          showToast('录音功能暂时不可用，请稍后重试')
        }, 2000)
      }
    }
  }
}

// 停止录音
const stopRecording = async () => {
  if (!audioRecorder.value) {
    console.log('⚠️ 录音器不可用')
    return
  }

  if (!isRecording.value) {
    console.log('⚠️ 当前未在录音中，跳过停止操作')
    return
  }

  try {
    console.log('🛑 停止录音...')
    // 先设置状态，避免重复调用
    isRecording.value = false
    await audioRecorder.value.stopRecording()
  } catch (error: any) {
    console.error('❌ 停止录音失败:', error)

    // 如果是"未在录音中"的错误，不显示错误提示
    if (error.message && error.message.includes('未在录音中')) {
      console.log('ℹ️ 录音已经停止，忽略此错误')
      return
    }

    const errorMessage = error.message || '录音保存失败'
    showToast(errorMessage)
  }
}



// 组件挂载
onMounted(async () => {
  console.log('🚀 VoiceTestPage 组件挂载')
  console.log('🔍 当前用户状态:', {
    hasToken: !!userStore.token,
    hasUserInfo: !!userStore.userInfo,
    userId: userStore.userInfo?.user_id,
    openid: userStore.userInfo?.wechat_openid
  })

  // 确保用户信息已加载
  if (!userStore.userInfo && userStore.token) {
    console.log('🔄 加载用户信息...')
    await userStore.fetchUserInfo()
  }

  // 检查用户信息，如果没有则尝试自动登录测试用户
  if (!userStore.userInfo) {
    console.log('⚠️ 没有用户信息，尝试自动登录测试用户...')
    showToast('正在自动登录测试用户...')

    try {
      // 生成测试用户授权码
      const mockCode = `mock_voice_test_${Date.now()}_new`
      const loginResult = await userStore.loginAction(mockCode)

      if (loginResult && loginResult.userInfo) {
        console.log('✅ 测试用户自动登录成功:', loginResult.userInfo)
        showToast(`登录成功: ${loginResult.userInfo.nickname}`)
      } else {
        console.error('❌ 测试用户自动登录失败')
        showToast('自动登录失败，请手动登录')
        return
      }
    } catch (error: any) {
      console.error('❌ 测试用户自动登录异常:', error)
      showToast('自动登录异常，请手动登录')
      return
    }
  }

  console.log('✅ 用户信息已加载:', {
    user_id: userStore.userInfo?.user_id,
    wechat_openid: userStore.userInfo?.wechat_openid,
    nickname: userStore.userInfo?.nickname
  })

  await initAudioRecorder()
  await checkServerRecognitionStatus()
  initWebSocket()
})

// 监听录音模式变化
watch(recordingMode, async (newMode) => {
  console.log('🔄 录音模式切换到:', newMode)

  // 如果切换到流式识别但录音模式不是浏览器，自动切换识别模式
  if (recognitionMode.value === 'streaming' && newMode !== 'browser') {
    recognitionMode.value = 'server'
    showToast('流式识别需要浏览器录音，已切换到服务器识别')
  }

  // 如果切换到客户端识别但录音模式不是微信，自动切换识别模式
  if (recognitionMode.value === 'client' && newMode !== 'wechat') {
    recognitionMode.value = 'server'
    showToast('客户端识别需要微信录音，已切换到服务器识别')
  }

  // 重新初始化录音器
  if (audioRecorder.value) {
    try {
      audioRecorder.value.cancelRecording()
    } catch (error) {
      console.log('取消当前录音时出现错误（可忽略）:', error)
    }
    audioRecorder.value = null
  }

  await initAudioRecorder()
})

// 监听识别模式变化
watch(recognitionMode, (newMode) => {
  console.log('🔄 识别模式切换到:', newMode)

  // 如果切换到流式识别但录音模式不是浏览器，自动切换录音模式
  if (newMode === 'streaming' && recordingMode.value !== 'browser') {
    recordingMode.value = 'browser'
    showToast('流式识别需要浏览器录音，已切换录音模式')
  }

  // 如果切换到客户端识别但录音模式不是微信，自动切换录音模式
  if (newMode === 'client' && recordingMode.value !== 'wechat') {
    recordingMode.value = 'wechat'
    showToast('客户端识别需要微信录音，已切换录音模式')
  }
})

// 组件卸载
onUnmounted(() => {
  console.log('🔚 VoiceTestPage 组件卸载')
  if (audioRecorder.value && isRecording.value) {
    try {
      audioRecorder.value.cancelRecording()
    } catch (error) {
      console.log('组件卸载时取消录音出现错误（可忽略）:', error)
    }
  }

  // 关闭WebSocket连接
  if (wsClient.value) {
    wsClient.value.disconnect()
    wsClient.value = null
  }
})
</script>

<style scoped>
.voice-test-page {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 简化的控制面板 */
.control-panel {
  background: white;
  border-bottom: 1px solid #e1e8ed;
  padding: 12px 16px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mode-selectors {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.mode-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d9e0;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #333;
}

.mode-select:focus {
  outline: none;
  border-color: #1da1f2;
  box-shadow: 0 0 0 2px rgba(29, 161, 242, 0.1);
}

.status-bar {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.status-item {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.status-item.ready {
  background: #e8f5e8;
  color: #2d7d32;
}

.status-item.not-ready {
  background: #ffebee;
  color: #c62828;
}

.status-item.streaming {
  background: #e3f2fd;
  color: #1565c0;
  animation: pulse 2s infinite;
}

/* 消息容器 - 优先展示 */
.message-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 16px;
  margin-bottom: 120px; /* 为录音按钮留出空间 */
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.empty-message {
  text-align: center;
  color: #657786;
  font-size: 16px;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  border: 2px dashed #e1e8ed;
}

/* 录音控制区域 */
.voice-input-area {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.voice-button {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  font-family: inherit;
}

.voice-button:hover:not(:disabled) {
  transform: scale(1.05);
}

.voice-button:disabled {
  background: #aab8c2;
  cursor: not-allowed;
  opacity: 0.6;
}

.voice-button.start {
  background: #1da1f2;
  color: white;
}

.voice-button.start:hover:not(:disabled) {
  background: #1991db;
  box-shadow: 0 6px 25px rgba(29, 161, 242, 0.4);
}

.voice-button.stop {
  background: #e0245e;
  color: white;
  box-shadow: 0 4px 20px rgba(224, 36, 94, 0.4);
}

.mic-icon {
  font-size: 28px;
  margin-bottom: 4px;
}

.button-text {
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

.recording-animation {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.pulse {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: pulseRing 1.5s infinite;
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.recording-text {
  font-size: 10px;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 2px;
}

.stop-text {
  font-size: 9px;
  opacity: 0.9;
  line-height: 1;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 登录区域样式 */
.login-section {
  margin-top: 12px;
  text-align: center;
}

.login-btn {
  background: #1da1f2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-btn:hover:not(:disabled) {
  background: #1991db;
}

.login-btn:disabled {
  background: #aab8c2;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    padding: 10px 12px;
  }

  .mode-selectors {
    flex-direction: column;
    gap: 8px;
  }

  .message-container {
    padding: 0 12px;
    margin-bottom: 100px;
  }

  .voice-button {
    width: 80px;
    height: 80px;
  }

  .mic-icon {
    font-size: 24px;
  }

  .button-text {
    font-size: 10px;
  }

  .recording-text {
    font-size: 9px;
  }

  .stop-text {
    font-size: 8px;
  }
}

/* 滚动条样式 */
.message-list::-webkit-scrollbar {
  width: 4px;
}

.message-list::-webkit-scrollbar-track {
  background: transparent;
}

.message-list::-webkit-scrollbar-thumb {
  background: #d1d9e0;
  border-radius: 2px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: #aab8c2;
}

/* 控制台日志样式 */
.console-panel {
  background: #1e1e1e;
  border-radius: 8px;
  margin: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.console-header {
  background: #2d2d2d;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #404040;
}

.console-title {
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
}

.console-controls {
  display: flex;
  gap: 8px;
}

.clear-btn, .close-btn {
  background: #404040;
  color: #ffffff;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-btn:hover, .close-btn:hover {
  background: #505050;
}

.console-logs {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  padding: 2px 0;
}

.log-timestamp {
  color: #888;
  margin-right: 8px;
  min-width: 80px;
}

.log-level {
  margin-right: 8px;
  min-width: 50px;
  font-weight: bold;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.log-log .log-level {
  color: #ffffff;
}

.log-info .log-level {
  color: #4fc3f7;
}

.log-warn .log-level {
  color: #ffb74d;
}

.log-error .log-level {
  color: #f44336;
}

.log-log .log-message {
  color: #e0e0e0;
}

.log-info .log-message {
  color: #e1f5fe;
}

.log-warn .log-message {
  color: #fff3e0;
}

.log-error .log-message {
  color: #ffebee;
}

.no-logs {
  color: #888;
  text-align: center;
  padding: 20px;
  font-style: italic;
}

.show-console-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #2d2d2d;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.show-console-btn:hover {
  background: #404040;
}
</style>
