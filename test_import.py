#!/usr/bin/env python3
"""
测试导入是否正常
"""

def test_imports():
    """测试各个模块的导入"""
    try:
        print("🔧 测试文件上传管理器导入...")
        from app.utils.file_upload import file_upload_manager
        print("✅ 文件上传管理器导入成功")
        
        print("🎵 测试音频文件助手导入...")
        from app.utils.audio_file_helper import audio_file_helper
        print("✅ 音频文件助手导入成功")
        
        print("📁 测试上传API导入...")
        from app.api.v1.airelief.upload import router
        print("✅ 上传API导入成功")
        
        print("🚀 测试配置导入...")
        from app.settings.config import settings
        print(f"✅ 配置导入成功，上传目录: {settings.UPLOAD_BASE_DIR}")
        
        print("🎯 测试目录创建...")
        import os
        os.makedirs(settings.UPLOAD_AUDIO_DIR, exist_ok=True)
        os.makedirs(settings.UPLOAD_AVATAR_DIR, exist_ok=True)
        print(f"✅ 目录创建成功:")
        print(f"   音频目录: {settings.UPLOAD_AUDIO_DIR}")
        print(f"   头像目录: {settings.UPLOAD_AVATAR_DIR}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 开始测试模块导入...")
    print("=" * 50)
    
    success = test_imports()
    
    print("=" * 50)
    if success:
        print("🎉 所有模块导入成功！文件上传功能已就绪。")
        print("💡 现在可以启动后端服务进行完整测试。")
    else:
        print("❌ 模块导入失败，请检查代码。")
