# 语音消息流程测试指南

## 测试目标
验证ChatPage.vue中的语音消息发送和播放功能是否正常工作。

## 测试步骤

### 1. 基础功能测试
1. **进入聊天页面**：访问 `/chat`
2. **切换到语音模式**：点击输入框左侧的语音图标
3. **开始录音**：长按"按住 说话"按钮
4. **验证遮罩层**：应该显示录音遮罩层，包含：
   - 录音指示器（红点动画）
   - 录音时长显示
   - 实时识别文本（如果流式识别正常）
   - "松开发送"提示

### 2. 流式识别测试
1. **说话测试**：在录音过程中说话
2. **实时识别**：观察遮罩层中是否显示实时识别文本
3. **识别状态**：检查控制台日志，确认流式识别状态变化

### 3. 录音结束测试
1. **松开按钮**：松开录音按钮
2. **遮罩层消失**：录音遮罩层应该立即消失
3. **语音消息显示**：等待后端处理，应该显示语音消息气泡
4. **消息内容**：语音消息应该包含：
   - 语音图标和时长
   - 转录文本（如果有）
   - 可点击播放功能

### 4. 语音播放测试
1. **点击语音消息**：点击语音消息气泡
2. **播放状态**：应该显示播放状态指示器
3. **音频播放**：应该能听到录制的音频

## 预期结果

### 正常流程
1. ✅ 录音开始 → 显示遮罩层
2. ✅ 流式识别启动 → 显示实时文本
3. ✅ 录音结束 → 遮罩层消失
4. ✅ 后端处理 → 收到 `voice_message_sent` 事件
5. ✅ 语音消息显示 → 包含 `audioUrl` 字段
6. ✅ 点击播放 → 音频正常播放

### 关键检查点
- **录音状态管理**：`isRecording` 状态正确切换
- **流式识别状态**：`isStreamingActive` 状态正确切换
- **WebSocket事件**：正确接收 `streaming_started`、`streaming_result`、`streaming_ended`、`voice_message_sent` 事件
- **语音消息格式**：包含 `audioUrl`、`transcription`、`audioDuration` 字段
- **音频文件格式**：后端生成的音频文件为 `.wav` 格式且可播放

## 故障排除

### 常见问题
1. **录音权限**：确保浏览器已授权麦克风权限
2. **WebSocket连接**：确保WebSocket连接正常
3. **SSL证书**：确保后端SSL证书配置正确
4. **音频格式**：确保后端PCM转WAV功能正常

### 调试信息
查看浏览器控制台，关注以下日志：
- `🎤 [ChatPage]` 开头的录音相关日志
- `🔄 [ChatPage]` 开头的流式识别日志
- `🎵 [ChatPage]` 开头的语音消息日志
- WebSocket事件接收日志

## 性能优化建议

### 用户体验优化
1. **录音反馈**：录音开始时立即显示遮罩层
2. **识别反馈**：实时显示识别文本
3. **状态提示**：清晰的状态提示信息
4. **错误处理**：友好的错误提示

### 技术优化
1. **音频质量**：使用16kHz采样率，单声道
2. **文件格式**：PCM转WAV确保兼容性
3. **缓存机制**：语音消息本地缓存
4. **网络优化**：音频数据压缩传输
