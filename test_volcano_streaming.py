#!/usr/bin/env python3
"""
火山引擎流式识别快速测试脚本
用于诊断WebSocket连接和认证问题
"""
import asyncio
import websockets
import json
import gzip
import time
import uuid
from app.settings.config import settings

# 火山引擎协议常量
PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001
CLIENT_FULL_REQUEST = 0b0001
CLIENT_AUDIO_ONLY_REQUEST = 0b0010
NO_SEQUENCE = 0b0000
JSON = 0b0001
GZIP = 0b0001

def generate_header(
    version=PROTOCOL_VERSION,
    message_type=CLIENT_FULL_REQUEST,
    message_type_specific_flags=NO_SEQUENCE,
    serial_method=JSON,
    compression_type=GZIP,
    reserved_data=0x00,
    extension_header=bytes()
):
    """生成火山引擎协议头"""
    header = bytearray()
    header_size = int(len(extension_header) / 4) + 1
    header.append((version << 4) | header_size)
    header.append((message_type << 4) | message_type_specific_flags)
    header.append((serial_method << 4) | compression_type)
    header.append(reserved_data)
    header.extend(extension_header)
    return header

def parse_response(res):
    """解析火山引擎响应"""
    try:
        protocol_version = res[0] >> 4
        header_size = res[0] & 0x0f
        message_type = res[1] >> 4
        message_type_specific_flags = res[1] & 0x0f
        serialization_method = res[2] >> 4
        message_compression = res[2] & 0x0f
        reserved = res[3]
        header_extensions = res[4:header_size * 4]
        payload = res[header_size * 4:]
        
        result = {
            'protocol_version': protocol_version,
            'message_type': message_type,
            'serialization_method': serialization_method,
            'message_compression': message_compression
        }
        
        payload_msg = None
        payload_size = 0

        if message_type == 0b1001:  # SERVER_FULL_RESPONSE
            payload_size = int.from_bytes(payload[:4], "big", signed=True)
            payload_msg = payload[4:]
        elif message_type == 0b1011:  # SERVER_ACK
            seq = int.from_bytes(payload[:4], "big", signed=True)
            result['seq'] = seq
            if len(payload) >= 8:
                payload_size = int.from_bytes(payload[4:8], "big", signed=False)
                payload_msg = payload[8:]
        elif message_type == 0b1111:  # SERVER_ERROR_RESPONSE
            code = int.from_bytes(payload[:4], "big", signed=False)
            result['code'] = code
            payload_size = int.from_bytes(payload[4:8], "big", signed=False)
            payload_msg = payload[8:]

        if payload_msg is not None:
            if message_compression == GZIP:
                payload_msg = gzip.decompress(payload_msg)

            if serialization_method == JSON:
                payload_msg = json.loads(str(payload_msg, "utf-8"))
            elif serialization_method != 0b0000:  # NO_SERIALIZATION
                payload_msg = str(payload_msg, "utf-8")

        result['payload_msg'] = payload_msg
        result['payload_size'] = payload_size
        return result
    except Exception as e:
        print(f"❌ 解析响应失败: {e}")
        return {'error': str(e)}

async def test_volcano_streaming():
    """测试火山引擎流式识别连接"""
    print("🔥 开始测试火山引擎流式识别连接...")
    
    # 获取配置
    app_id = getattr(settings, 'VOLCENGINE_APP_ID', '')
    access_key = getattr(settings, 'VOLCENGINE_ACCESS_KEY', '')
    secret_key = getattr(settings, 'VOLCENGINE_SECRET_KEY', '')
    cluster = getattr(settings, 'VOLCENGINE_CLUSTER', 'volcengine_streaming_common')
    streaming_url = "wss://openspeech.bytedance.com/api/v2/asr"
    
    print(f"📋 配置信息:")
    print(f"  app_id: {app_id[:8]}..." if app_id else "  app_id: ❌ 空")
    print(f"  access_key: {access_key[:8]}..." if access_key else "  access_key: ❌ 空")
    print(f"  secret_key: {secret_key[:8]}..." if secret_key else "  secret_key: ❌ 空")
    print(f"  cluster: {cluster}")
    print(f"  streaming_url: {streaming_url}")
    
    if not all([app_id, access_key, secret_key]):
        print("❌ 火山引擎配置不完整，无法测试")
        return False
    
    # 构建请求参数
    session_id = f"test_{int(time.time() * 1000)}"
    reqid = str(uuid.uuid4())
    
    request_params = {
        'app': {
            'appid': app_id,
            'cluster': cluster,
            'token': access_key,
        },
        'user': {
            'uid': 'test_user'
        },
        'request': {
            'reqid': reqid,
            'nbest': 1,
            'workflow': 'audio_in,resample,partition,vad,fe,decode,itn,nlu_punctuate',
            'show_language': False,
            'show_utterances': False,
            'result_type': 'full',
            "sequence": 1
        },
        'audio': {
            'format': 'wav',
            'rate': 16000,
            'language': 'zh-CN',
            'bits': 16,
            'channel': 1,
            'codec': 'raw'
        }
    }
    
    print(f"📝 请求参数: {json.dumps(request_params, indent=2, ensure_ascii=False)}")
    
    # 构建完整请求
    payload_bytes = str.encode(json.dumps(request_params))
    payload_bytes = gzip.compress(payload_bytes)
    
    full_client_request = bytearray(generate_header())
    full_client_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
    full_client_request.extend(payload_bytes)
    
    print(f"📦 完整请求大小: {len(full_client_request)} bytes")
    
    # 构建认证头
    header = {'Authorization': f'Bearer; {access_key}'}
    print(f"🔐 认证头: {header}")
    
    try:
        print("🔌 开始连接WebSocket...")
        
        # 尝试连接
        websocket = await websockets.connect(
            streaming_url,
            additional_headers=header,
            max_size=1000000000
        )
        
        print("✅ WebSocket连接成功")
        
        # 发送初始化请求
        print("📤 发送初始化请求...")
        await websocket.send(full_client_request)
        print("✅ 初始化请求已发送")
        
        # 等待响应
        print("📥 等待服务器响应...")
        res = await asyncio.wait_for(websocket.recv(), timeout=10.0)
        print(f"📨 收到响应，大小: {len(res)} bytes")
        
        # 解析响应
        result = parse_response(res)
        print(f"📋 解析响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if 'payload_msg' in result and isinstance(result['payload_msg'], dict):
            code = result['payload_msg'].get('code', -1)
            if code == 1000:
                print("🎉 火山引擎流式识别初始化成功！")
                success = True
            else:
                print(f"❌ 火山引擎返回错误码: {code}")
                print(f"错误信息: {result['payload_msg']}")
                success = False
        else:
            print("❌ 响应格式异常")
            success = False
        
        # 关闭连接
        await websocket.close()
        print("🔌 WebSocket连接已关闭")
        
        return success
        
    except asyncio.TimeoutError:
        print("⏰ 等待服务器响应超时")
        return False
    except websockets.exceptions.ConnectionClosed as e:
        print(f"🔌 WebSocket连接被关闭: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        return False

if __name__ == "__main__":
    print("🧪 火山引擎流式识别快速测试")
    print("=" * 50)
    
    result = asyncio.run(test_volcano_streaming())
    
    print("=" * 50)
    if result:
        print("✅ 测试通过：火山引擎流式识别可用")
    else:
        print("❌ 测试失败：火山引擎流式识别不可用")
        print("💡 建议检查:")
        print("  1. 网络连接是否正常")
        print("  2. 火山引擎API凭证是否正确")
        print("  3. 火山引擎服务是否正常")
