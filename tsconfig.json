{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"]}, "allowImportingTsExtensions": false, "noEmit": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/auto-imports.d.ts", "src/components.d.ts"], "exclude": ["node_modules", "dist"]}