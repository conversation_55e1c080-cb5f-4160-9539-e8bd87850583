{"root": ["./src/auto-imports.d.ts", "./src/components.d.ts", "./src/main.ts", "./src/vite-env.d.ts", "./src/apis/auth.ts", "./src/apis/chat.ts", "./src/apis/index.ts", "./src/apis/onboarding.ts", "./src/apis/payment.ts", "./src/apis/user.ts", "./src/constants/apiconstants.ts", "./src/constants/index.ts", "./src/constants/routenames.ts", "./src/constants/storagekeys.ts", "./src/router/index.ts", "./src/router/routes/auth.ts", "./src/router/routes/chat.ts", "./src/router/routes/common.ts", "./src/router/routes/index.ts", "./src/router/routes/onboarding.ts", "./src/router/routes/payment.ts", "./src/router/routes/profile.ts", "./src/stores/user.ts", "./src/types/api.d.ts", "./src/utils/mockservice.ts", "./src/utils/request.ts", "./src/app.vue", "./src/components/business/chat/chatheader.vue", "./src/components/business/chat/chatinput.vue", "./src/components/business/chat/messageitem.vue", "./src/components/business/onboarding/newuserbenefitpopup.vue", "./src/components/business/onboarding/userinfocollectorpopup.vue", "./src/components/business/profile/paymentidentitycard.vue", "./src/components/business/profile/profilesidebar.vue", "./src/components/business/profile/sidebaridentitycard.vue", "./src/components/business/profile/verificationcodeinput.vue", "./src/components/common/basepopup.vue", "./src/components/common/confirmpopup.vue", "./src/components/common/emptystate.vue", "./src/components/common/loadingindicator.vue", "./src/layouts/chatlayout.vue", "./src/layouts/defaultlayout.vue", "./src/views/auth/authcallbackpage.vue", "./src/views/auth/launchscreen.vue", "./src/views/auth/wechatcallback.vue", "./src/views/chat/chatpage.vue", "./src/views/common/notfoundpage.vue", "./src/views/common/privacypolicypage.vue", "./src/views/common/serviceagreementpage.vue", "./src/views/onboarding/onboardingchatpage.vue", "./src/views/payment/couponredeempage.vue", "./src/views/payment/paymentcallback.vue", "./src/views/payment/paymentpage.vue", "./src/views/payment/paymentresultpage.vue", "./src/views/profile/accountbind.vue", "./src/views/profile/bindphone.vue", "./src/views/profile/contactuspage.vue", "./src/views/profile/orderhistorypage.vue", "./src/views/profile/profileeditpage.vue", "./src/views/profile/profilepage.vue", "./src/views/profile/settingspage.vue", "./src/views/test/identitycardtest.vue", "./src/views/test/mocktestpage.vue", "./src/views/test/orderhistorytestpage.vue", "./src/views/test/paymenttestpage.vue", "./src/views/test/routeguardtestpage.vue", "./src/views/test/testindex.vue", "./src/views/test/vandialogtest.vue"], "errors": true, "version": "5.8.3"}