import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import { fileURLToPath, URL } from 'node:url'

// https://vite.dev/config/
export default defineConfig(({ mode }) => ({
  base: '/AIrelief/', // 设置基础路径
  plugins: [
    vue(),
    AutoImport({
      resolvers: [
        VantResolver({
          importStyle: true,
        })
      ],
      dts: 'src/auto-imports.d.ts',
      dirs: ['src/composables/**', 'src/stores/**'],
    }),
    Components({
      resolvers: [
        VantResolver({
          // 自动导入组件样式
          importStyle: true,
        })
      ],
      dts: 'src/components.d.ts',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  build: {
    // 构建输出目录
    outDir: 'dist',
    // 构建时清空输出目录
    emptyOutDir: true,
    // 代码分割配置
    rollupOptions: {
      output: {
        // 手动分割代码块
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          vant: ['vant']
        }
      }
    },
    // 复制 public 目录时的配置
    copyPublicDir: true,
    // 构建优化 - 使用 esbuild 压缩（更快）
    minify: 'esbuild',
    // 根据构建模式生成源码映射
    sourcemap: mode === 'development' ? true : false
  },
  server: {
    port: 8999,
    strictPort: true, // 如果端口被占用，直接报错而不是寻找其他端口
    host: true, // 允许外部访问
    open: false, // 不自动打开浏览器
    allowedHosts: ['www.airelief.cn', 'localhost', '127.0.0.1'], // 允许的主机列表
    proxy: {
      '/api': {
        target: 'http://localhost:8000', // 后端API服务器地址
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/AIrelief\/api/, '/api'),
      },
    },
  },
}))
