# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
# 配置文件
settings/
config.py

# 证书文件
certs/
*.pem
*.key
*.crt
*.p12
*.pfx

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
*.tmp

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
Thumbs.db

# 依赖和构建
node_modules/
dist/
build/
*.pyc
__pycache__/
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# 测试覆盖率
.coverage
htmlcov/
.tox/
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 其他
.env.local
.env.development.local
.env.test.local
.env.production.local


node_modules
.DS_Store
dist
dist-ssr
coverage
*.local
stats.html

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
