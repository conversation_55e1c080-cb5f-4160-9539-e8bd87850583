import { request } from '@/utils'

export default {
  login: (data) => request.post('/base/access_token', data, { noNeedToken: true }),
  getUserInfo: () => request.get('/base/userinfo'),
  getUserMenu: () => request.get('/base/usermenu'),
  getUserApi: () => request.get('/base/userapi'),
  // profile
  updatePassword: (data = {}) => request.post('/base/update_password', data),
  // users
  getUserList: (params = {}) => request.get('/user/list', { params }),
  getUserById: (params = {}) => request.get('/user/get', { params }),
  createUser: (data = {}) => request.post('/user/create', data),
  updateUser: (data = {}) => request.post('/user/update', data),
  deleteUser: (params = {}) => request.delete(`/user/delete`, { params }),
  resetPassword: (data = {}) => request.post(`/user/reset_password`, data),
  // role
  getRoleList: (params = {}) => request.get('/role/list', { params }),
  createRole: (data = {}) => request.post('/role/create', data),
  updateRole: (data = {}) => request.post('/role/update', data),
  deleteRole: (params = {}) => request.delete('/role/delete', { params }),
  updateRoleAuthorized: (data = {}) => request.post('/role/authorized', data),
  getRoleAuthorized: (params = {}) => request.get('/role/authorized', { params }),
  // menus
  getMenus: (params = {}) => request.get('/menu/list', { params }),
  createMenu: (data = {}) => request.post('/menu/create', data),
  updateMenu: (data = {}) => request.post('/menu/update', data),
  deleteMenu: (params = {}) => request.delete('/menu/delete', { params }),
  // apis
  getApis: (params = {}) => request.get('/api/list', { params }),
  createApi: (data = {}) => request.post('/api/create', data),
  updateApi: (data = {}) => request.post('/api/update', data),
  deleteApi: (params = {}) => request.delete('/api/delete', { params }),
  refreshApi: (data = {}) => request.post('/api/refresh', data),
  // depts
  getDepts: (params = {}) => request.get('/dept/list', { params }),
  createDept: (data = {}) => request.post('/dept/create', data),
  updateDept: (data = {}) => request.post('/dept/update', data),
  deleteDept: (params = {}) => request.delete('/dept/delete', { params }),
  // auditlog
  getAuditLogList: (params = {}) => request.get('/auditlog/list', { params }),
  // airelief users
  getAIReliefUserList: (params = {}) => request.get('/airelief/user/list', { params }),
  getAIReliefUserById: (params = {}) => request.get('/airelief/user/get', { params }),
  updateAIReliefUser: (data = {}) => request.post('/airelief/user/update', data),
  deleteAIReliefUser: (params = {}) => request.delete('/airelief/user/delete', { params }),
  
  // airelief recharge orders
  getRechargeList: (params = {}) => request.get('/airelief/payment/orders/all', { params }),
  getOrderDetail: (orderId) => request.get(`/airelief/payment/orders/${orderId}`),
  updateOrderStatus: (orderId, data) => request.put(`/airelief/payment/orders/${orderId}/status`, data),
  getOrderStatistics: (params = {}) => request.get('/airelief/payment/orders/statistics', { params }),
  
  // airelief duration packages
  getDurationPackageList: (params = {}) => request.get('/airelief/payment/packages/all', { params }),
  updateDurationPackage: (data = {}) => request.put(`/airelief/payment/packages/${data.id}`, data),
  
  // airelief agreements
  getAgreementList: (params = {}) => request.get('/airelief/agreement/list', { params, noNeedToken: true }),
  getAgreement: (agreementId) => request.get('/airelief/agreement/get', { params: { agreement_id: agreementId }, noNeedToken: true }),
  createAgreement: (data = {}) => request.post('/airelief/agreement/create', data, { noNeedToken: true }),
  updateAgreement: (data = {}) => request.post('/airelief/agreement/update', data, { noNeedToken: true }),
  deleteAgreement: (agreementId) => request.delete('/airelief/agreement/delete', { params: { agreement_id: agreementId }, noNeedToken: true }),
  getAgreementTypes: () => request.get('/airelief/agreement/types', { noNeedToken: true }),
  getAgreementStatuses: () => request.get('/airelief/agreement/statuses', { noNeedToken: true }),
  getPublicAgreement: (agreementType) => request.get(`/airelief/agreement/public/${agreementType}`, { noNeedToken: true }),
  
  // airelief chat sessions
  getChatSessionList: (params = {}) => request.get('/airelief/chat/sessions', { params }),
  getChatHistory: (params = {}) => request.get(`/airelief/chat/sessions/${params.session_id}/messages`, { params }),
  getChatStatistics: () => request.get('/airelief/chat/statistics'),
  debugMessageCount: () => request.get('/airelief/chat/debug/message-count'),
  fixSyncMessageCount: () => request.post('/airelief/chat/fix/sync-message-count'),
  exportChatHistory: (params = {}) => request.get(`/airelief/chat/sessions/${params.session_id}/export`, { params }),
  endChatSession: (params = {}) => request.put(`/airelief/chat/sessions/${params.session_id}/end`),
  deleteChatSession: (params = {}) => request.delete(`/airelief/chat/sessions/${params.session_id}`),
  
  // airelief redeem codes
  getRedeemCodeList: (params = {}) => request.get('/airelief/redeem/list', { params }),
  createRedeemCode: (data = {}) => request.post('/airelief/redeem/create', data),
  updateRedeemCode: (data = {}) => request.post('/airelief/redeem/update', data),
  deleteRedeemCode: (params = {}) => request.delete('/airelief/redeem/delete', { params }),
  
  // airelief coupons
  getCouponList: (params = {}) => request.get('/airelief/coupon/list', { params }),
  createCoupon: (data = {}) => request.post('/airelief/coupon/create', data),
  updateCoupon: (data = {}) => request.post('/airelief/coupon/update', data),
  deleteCoupon: (params = {}) => request.delete('/airelief/coupon/delete', { params: { coupon_id: params.id } }),
  batchCreateCoupon: (data = {}) => request.post('/airelief/coupon/batch', data),
  validateCoupon: (data = {}) => request.post('/airelief/coupon/validate', data),
  redeemCoupon: (data = {}) => request.post('/airelief/coupon/redeem', data),
  getCouponUsageRecords: (params = {}) => request.get('/airelief/coupon/usage-records', { params }),
}
