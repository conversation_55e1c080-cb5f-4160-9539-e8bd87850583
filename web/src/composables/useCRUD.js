import { ref, computed } from 'vue'
import { isNullOrWhitespace } from '@/utils'

const ACTIONS = {
  view: '查看',
  edit: '编辑',
  add: '新增',
}

export default function ({ name, initForm = {}, doCreate, doDelete, doUpdate, refresh }) {
  const modalVisible = ref(false)
  const modalAction = ref('')
  const modalTitle = computed(() => ACTIONS[modalAction.value] + name)
  const modalLoading = ref(false)
  const modalFormRef = ref(null)
  const modalForm = ref({ ...initForm })

  /** 新增 */
  function handleAdd() {
    try {
      console.log('执行新增操作')
      modalAction.value = 'add'
      modalVisible.value = true
      modalForm.value = { ...initForm }
    } catch (error) {
      console.error('新增操作失败:', error)
    }
  }

  /** 修改 */
  function handleEdit(row) {
    try {
      console.log('执行编辑操作，数据:', row)
      modalAction.value = 'edit'
      modalVisible.value = true
      modalForm.value = { ...row }
    } catch (error) {
      console.error('编辑操作失败:', error)
      window.$message?.error('打开编辑窗口失败')
    }
  }

  /** 查看 */
  function handleView(row) {
    try {
      console.log('执行查看操作，数据:', row)
      modalAction.value = 'view'
      modalVisible.value = true
      modalForm.value = { ...row }
    } catch (error) {
      console.error('查看操作失败:', error)
    }
  }

  /** 保存 */
  function handleSave(...callbacks) {
    if (!['edit', 'add'].includes(modalAction.value)) {
      modalVisible.value = false
      return
    }

    if (!modalFormRef.value) {
      console.error('表单引用不存在')
      return
    }

    modalFormRef.value?.validate(async (err) => {
      if (err) {
        console.log('表单验证失败:', err)
        return
      }

      const actions = {
        add: {
          api: () => doCreate(modalForm.value),
          cb: () => {
            callbacks.forEach((callback) => callback && callback())
          },
          msg: () => window.$message?.success('新增成功'),
        },
        edit: {
          api: () => doUpdate(modalForm.value),
          cb: () => {
            callbacks.forEach((callback) => callback && callback())
          },
          msg: () => window.$message?.success('编辑成功'),
        },
      }
      const action = actions[modalAction.value]

      try {
        modalLoading.value = true
        console.log(`执行${modalAction.value}操作，数据:`, modalForm.value)
        const data = await action.api()
        action.cb()
        action.msg()
        modalLoading.value = modalVisible.value = false
        data && refresh && refresh(data)
      } catch (error) {
        console.error(`${modalAction.value}操作失败:`, error)
        modalLoading.value = false
        window.$message?.error(`${modalAction.value}失败: ${error.message || '未知错误'}`)
      }
    })
  }

  /** 删除 */
  async function handleDelete(params = {}) {
    if (isNullOrWhitespace(params)) {
      console.warn('删除参数为空')
      return
    }
    
    try {
      modalLoading.value = true
      console.log('执行删除操作，参数:', params)
      const data = await doDelete(params)
      window.$message?.success('删除成功')
      modalLoading.value = false
      refresh && refresh(data)
    } catch (error) {
      console.error('删除操作失败:', error)
      modalLoading.value = false
      window.$message?.error(`删除失败: ${error.message || '未知错误'}`)
    }
  }

  return {
    modalVisible,
    modalAction,
    modalTitle,
    modalLoading,
    handleAdd,
    handleDelete,
    handleEdit,
    handleView,
    handleSave,
    modalForm,
    modalFormRef,
  }
}
