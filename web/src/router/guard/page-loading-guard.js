export function createPageLoadingGuard(router) {
  router.beforeEach(() => {
    try {
      // 确保 loadingBar 存在再调用
      if (window.$loadingBar && typeof window.$loadingBar.start === 'function') {
        window.$loadingBar.start()
      }
    } catch (error) {
      console.warn('启动加载条失败:', error)
    }
  })

  router.afterEach(() => {
    setTimeout(() => {
      try {
        // 确保 loadingBar 存在再调用
        if (window.$loadingBar && typeof window.$loadingBar.finish === 'function') {
          window.$loadingBar.finish()
        }
      } catch (error) {
        console.warn('完成加载条失败:', error)
      }
    }, 200)
  })

  router.onError(() => {
    try {
      // 确保 loadingBar 存在再调用
      if (window.$loadingBar && typeof window.$loadingBar.error === 'function') {
        window.$loadingBar.error()
      }
    } catch (error) {
      console.warn('显示加载错误失败:', error)
    }
  })
}
