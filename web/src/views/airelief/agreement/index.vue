<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NSpace,
  NTag,
  NSelect,
  NDatePicker,
  NPopconfirm,
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: 'AIRelief协议管理' })

/**
 * 根据原始需求，协议管理功能包括：
 * 1. 协议文字展示
 * 2. 协议内容编辑
 *
 * 功能说明：
 * - 管理各种协议文本（用户协议、隐私政策等）
 * - 支持富文本编辑
 * - 协议版本管理
 * - 协议生效时间控制
 */

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleEdit: originalHandleEdit,
  handleDelete,
  handleAdd: originalHandleAdd,
} = useCRUD({
  name: '协议',
  initForm: {
    type: 'user_agreement',
    status: 'draft',
    version: '1.0',
  },
  doCreate: (data) => {
    // 转换日期格式
    const saveData = { ...data }
    if (saveData.effective_date && typeof saveData.effective_date === 'number') {
      saveData.effective_date = new Date(saveData.effective_date).toISOString()
    }
    return api.createAgreement(saveData)
  },
  doUpdate: (data) => {
    // 转换日期格式
    const saveData = { ...data }
    if (saveData.effective_date && typeof saveData.effective_date === 'number') {
      saveData.effective_date = new Date(saveData.effective_date).toISOString()
    }
    return api.updateAgreement(saveData)
  },
  doDelete: (data) => {
    return api.deleteAgreement(data.id)
  },
  refresh: () => $table.value?.handleSearch(),
})

// 协议类型和状态选项
const agreementTypes = ref([])
const statusOptions = ref([])

onMounted(async () => {
  console.log('协议管理页面加载开始')
  await Promise.all([
    loadAgreementTypes(),
    loadAgreementStatuses()
  ])
  console.log('协议管理页面加载完成')
  console.log('agreementTypes:', agreementTypes.value)
  console.log('statusOptions:', statusOptions.value)

  // 手动触发协议列表加载测试
  setTimeout(() => {
    console.log('手动触发协议列表加载')
    if ($table.value) {
      $table.value.handleSearch()
    } else {
      console.log('$table.value 不存在')
    }
  }, 1000)
})

// 加载协议类型选项
const loadAgreementTypes = async () => {
  try {
    const response = await api.getAgreementTypes()
    console.log('协议类型响应:', response)
    // 后台返回格式：{ code: 200, msg: "OK", data: [...] }
    agreementTypes.value = response.data || response || []
  } catch (error) {
    console.error('加载协议类型失败:', error)
    window.$message?.error('加载协议类型失败')
  }
}

// 加载协议状态选项
const loadAgreementStatuses = async () => {
  try {
    const response = await api.getAgreementStatuses()
    console.log('协议状态响应:', response)
    console.log('协议状态响应类型:', typeof response)
    console.log('协议状态响应data:', response.data)
    // 后台返回格式：{ code: 200, msg: "OK", data: [...] }
    statusOptions.value = response.data || response || []
    console.log('设置后的statusOptions:', statusOptions.value)
  } catch (error) {
    console.error('加载协议状态失败:', error)
    console.error('错误详情:', error)
    window.$message?.error('加载协议状态失败')
  }
}

// 获取协议列表（用于CrudTable）
const getAgreementList = async (params) => {
  try {
    console.log('获取协议列表参数:', params)
    const response = await api.getAgreementList(params)
    console.log('获取协议列表响应:', response)

    // 后台返回格式：{ code: 200, msg: null, data: [...], total: 0, page: 1, page_size: 20 }
    return {
      data: response.data || [],
      total: response.total || 0
    }
  } catch (error) {
    console.error('获取协议列表失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    return { data: [], total: 0 }
  }
}

// 自定义编辑处理函数，转换日期格式
const handleEdit = (row) => {
  try {
    console.log('编辑协议，原始数据:', row)

    // 转换日期字符串为时间戳供NDatePicker使用
    const editData = { ...row }
    if (editData.effective_date) {
      editData.effective_date = new Date(editData.effective_date).getTime()
    }

    console.log('编辑协议，转换后数据:', editData)
    originalHandleEdit(editData)
  } catch (error) {
    console.error('编辑协议失败:', error)
    window.$message?.error('打开编辑窗口失败')
  }
}

// 自定义添加处理函数
const handleAdd = () => {
  try {
    console.log('添加协议')
    originalHandleAdd()
  } catch (error) {
    console.error('添加协议失败:', error)
  }
}

// 自定义保存处理函数
const handleSave = () => {
  console.log('保存协议，表单数据:', modalForm.value)
  originalHandleSave()
}

// 协议类型标签映射
const AGREEMENT_TYPE_LABELS = {
  'user_agreement': '用户协议',
  'privacy_policy': '隐私政策',
  'payment_agreement': '充值协议',
  'disclaimer': '免责声明',
}

const AGREEMENT_STATUS_LABELS = {
  'active': '生效中',
  'draft': '草稿',
  'deprecated': '已废弃',
}

// 获取协议类型标签
const getAgreementTypeLabel = (type) => {
  return AGREEMENT_TYPE_LABELS[type] || type
}

// 获取状态标签
const getStatusLabel = (status) => {
  return AGREEMENT_STATUS_LABELS[status] || status
}

// 获取状态颜色
const getStatusType = (status) => {
  const typeMap = {
    'active': 'success',
    'draft': 'warning',
    'deprecated': 'error'
  }
  return typeMap[status] || 'default'
}

// 表格列定义
const columns = [
  {
    title: '协议标题',
    key: 'title',
    width: 200,
    align: 'left',
  },
  {
    title: '协议类型',
    key: 'type',
    width: 120,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: 'info', size: 'small' },
        { default: () => getAgreementTypeLabel(row.type) }
      )
    },
  },
  {
    title: '版本号',
    key: 'version',
    width: 100,
    align: 'center',
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: getStatusType(row.status), size: 'small' },
        { default: () => getStatusLabel(row.status) }
      )
    },
  },
  {
    title: '生效时间',
    key: 'effective_date',
    width: 150,
    align: 'center',
    render(row) {
      return row.effective_date ? formatDate(row.effective_date, 'YYYY-MM-DD HH:mm') : '-'
    },
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    align: 'center',
    render(row) {
      return formatDate(row.created_at, 'YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-right: 8px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: renderIcon('material-symbols:edit', { size: 16 }),
          }
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  size: 'small',
                  type: 'error',
                },
                {
                  default: () => '删除',
                  icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                }
              ),
            default: () => h('div', {}, '确定删除该协议吗？'),
          }
        ),
      ]
    },
  },
]

// 表单验证规则
const validateForm = {
  type: [
    {
      required: true,
      message: '请选择协议类型',
      trigger: ['change', 'blur'],
    },
  ],
  title: [
    {
      required: true,
      message: '请输入协议标题',
      trigger: ['input', 'blur'],
    },
  ],
  content: [
    {
      required: true,
      message: '请输入协议内容',
      trigger: ['input', 'blur'],
    },
  ],
  version: [
    {
      required: true,
      message: '请输入版本号',
      trigger: ['input', 'blur'],
    },
  ],
  status: [
    {
      required: true,
      message: '请选择协议状态',
      trigger: ['change', 'blur'],
    },
  ],
}


</script>

<template>
  <CommonPage show-footer title="AIRelief协议管理">
    <template #action>
      <NSpace>
        <NButton type="primary" @click="handleAdd">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新增协议
        </NButton>
        <NButton @click="$table?.handleSearch()">
          <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5" />刷新
        </NButton>
      </NSpace>
    </template>

    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getAgreementList"
    >
      <template #queryBar>
        <QueryBarItem label="协议标题" :label-width="80">
          <NInput
            v-model:value="queryItems.search"
            clearable
            type="text"
            placeholder="请输入协议标题"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="协议类型" :label-width="80">
          <NSelect
            v-model:value="queryItems.type"
            :options="agreementTypes"
            placeholder="选择协议类型"
            clearable
            filterable
            size="small"
            style="width: 100%;"
          />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="80">
          <NSelect
            v-model:value="queryItems.status"
            :options="statusOptions"
            placeholder="选择状态"
            clearable
            filterable
            size="small"
            style="width: 100%;"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 编辑/新建弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
      width="800px"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :rules="validateForm"
      >
        <NFormItem label="协议类型" path="type">
          <NSelect
            v-model:value="modalForm.type"
            :options="agreementTypes"
            placeholder="请选择协议类型"
          />
        </NFormItem>
        <NFormItem label="协议标题" path="title">
          <NInput v-model:value="modalForm.title" clearable placeholder="请输入协议标题" />
        </NFormItem>
        <NFormItem label="版本号" path="version">
          <NInput v-model:value="modalForm.version" clearable placeholder="请输入版本号，如：1.0" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSelect
            v-model:value="modalForm.status"
            :options="statusOptions"
            placeholder="请选择状态"
          />
        </NFormItem>
        <NFormItem label="生效时间" path="effective_date">
          <NDatePicker
            v-model:value="modalForm.effective_date"
            type="datetime"
            clearable
            placeholder="请选择生效时间"
            style="width: 100%"
          />
        </NFormItem>
        <NFormItem label="协议内容" path="content">
          <NInput
            v-model:value="modalForm.content"
            type="textarea"
            :rows="10"
            placeholder="请输入协议内容"
          />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>