<script setup>
import { h, onMounted, ref, computed } from 'vue'
import {
  NButton,
  NInput,
  NSpace,
  NTag,
  NDrawer,
  NDrawerContent,
  NCard,
  NList,
  NListItem,
  NThing,
  NDatePicker,
  NPopconfirm,
  NDropdown,
  NStatistic,
  NGrid,
  NGridItem,
  NProgress,
  NTooltip,
  NIcon,
  NModal,
  NDataTable,
  NPagination,
  NAlert,
  NDescriptions,
  NDescriptionsItem,
  NTime,
  NBadge,
  NText,
  NEmpty
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate } from '@/utils'
import api from '@/api'

defineOptions({ name: 'AIRelief聊天数据' })

const $table = ref(null)
const queryItems = ref({})
const showChatDetail = ref(false)
const showStatistics = ref(false)
const currentChatHistory = ref([])
const currentSessionInfo = ref({})
const currentMessagePage = ref(1)
const currentMessagePageSize = ref(20)
const messageTotal = ref(0)
const loadingMessages = ref(false)
const statistics = ref({})
const loadingStats = ref(false)

onMounted(() => {
  $table.value?.handleSearch()
  loadStatistics()
})

/**
 * 完善的聊天数据管理功能：
 * 1. 搜索功能：ID、昵称、手机号搜索
 * 2. 数据展示：会话列表、消息详情、语音识别结果
 * 3. 功能增强：会话管理、导出记录、数据统计
 * 4. 分页排序：支持多种排序和分页
 *
 * 数据结构说明：
 * - session_id: 会话ID
 * - user_id: 用户ID
 * - nickname: 用户昵称
 * - phone: 手机号
 * - message_count: 消息条数
 * - duration_consumed: 会话时长（秒）
 * - last_message: 最后一条消息
 * - status: 会话状态 (active/ended/timeout)
 * - created_at: 会话创建时间
 * - updated_at: 最后更新时间
 */

// 加载统计数据
const loadStatistics = async () => {
  try {
    loadingStats.value = true
    const { data } = await api.getChatStatistics()
    statistics.value = data
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loadingStats.value = false
  }
}

// 调试消息数量统计
const debugMessageCount = async () => {
  try {
    const { data } = await api.debugMessageCount()
    console.log('=== 消息数量调试信息 ===', data)

    // 显示调试信息
    const analysis = data.analysis || {}
    const sessionDetails = data.session_details || []

    let message = `📊 统计结果:\n`
    message += `直接统计所有消息: ${data.total_messages_direct}\n`
    message += `按会话统计总数: ${data.total_messages_by_session}\n`
    message += `会话表存储总数: ${data.total_stored_count}\n`
    message += `总会话数: ${data.session_count}\n\n`

    message += `🔍 差异分析:\n`
    message += `直接统计 vs 按会话统计: ${analysis.direct_vs_session}\n`
    message += `存储数量 vs 实际数量: ${analysis.stored_vs_actual}\n\n`

    // 检查不一致的会话
    const inconsistentSessions = sessionDetails.filter(
      detail => detail.stored_message_count !== detail.actual_message_count
    )

    if (inconsistentSessions.length > 0) {
      message += `⚠️ 发现 ${inconsistentSessions.length} 个会话的消息数量不一致:\n`
      inconsistentSessions.forEach(detail => {
        const sessionId = detail.session_id.substring(0, 20) + '...'
        message += `  ${sessionId}: 存储=${detail.stored_message_count}, 实际=${detail.actual_message_count}\n`
      })
    }

    // 使用对话框显示调试信息
    window.$dialog?.info({
      title: '消息数量调试信息',
      content: message,
      style: { whiteSpace: 'pre-line' },
      positiveText: '确定'
    })

    window.$message?.info('调试信息已输出到控制台')

  } catch (error) {
    console.error('调试消息统计失败:', error)
    window.$message?.error('调试失败')
  }
}

// 修复消息计数
const fixMessageCount = async () => {
  // 显示确认对话框
  window.$dialog?.warning({
    title: '确认修复',
    content: '确定要修复会话消息计数吗？\n\n此操作将同步所有会话的message_count字段与实际消息数量。',
    positiveText: '确认修复',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const { data } = await api.fixSyncMessageCount()
        console.log('=== 修复结果 ===', data)

        const fixedCount = data.fixed_sessions_count || 0
        const fixedSessions = data.fixed_sessions || []

        if (fixedCount > 0) {
          let message = `✅ 修复完成！\n\n`
          message += `总会话数: ${data.total_sessions}\n`
          message += `修复会话数: ${fixedCount}\n\n`
          message += `修复详情:\n`

          fixedSessions.forEach(session => {
            const sessionId = session.session_id.substring(0, 20) + '...'
            message += `${sessionId}: ${session.old_count} → ${session.new_count} (+${session.difference})\n`
          })

          window.$dialog?.success({
            title: '修复成功',
            content: message,
            style: { whiteSpace: 'pre-line' },
            positiveText: '确定'
          })

          // 刷新统计数据和表格
          await loadStatistics()
          $table.value?.handleSearch()

        } else {
          window.$message?.info('所有会话的消息计数都是正确的，无需修复')
        }

      } catch (error) {
        console.error('修复消息计数失败:', error)
        window.$message?.error('修复失败')
      }
    }
  })
}

const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center',
    sorter: true,
  },
  {
    title: '会话ID',
    key: 'session_id',
    width: 180,
    align: 'center',
    ellipsis: { tooltip: true },
    render(row) {
      return h(
        NTooltip,
        { trigger: 'hover' },
        {
          trigger: () => h('span', { class: 'cursor-pointer' }, row.session_id.substring(0, 16) + '...'),
          default: () => row.session_id
        }
      )
    }
  },
  {
    title: '用户信息',
    key: 'user_info',
    width: 160,
    align: 'center',
    render(row) {
      return h(
        'div',
        { class: 'user-info-cell' },
        [
          h('div', { class: 'font-medium' }, row.nickname || '未设置'),
          h('div', { class: 'text-xs text-gray-500' }, row.user_id),
          row.phone ? h('div', { class: 'text-xs text-blue-500' }, row.phone) : null
        ]
      )
    },
  },
  {
    title: '消息统计',
    key: 'message_stats',
    width: 120,
    align: 'center',
    render(row) {
      const textCount = row.text_message_count || 0
      const audioCount = row.audio_message_count || 0
      return h(
        'div',
        { class: 'message-stats-cell' },
        [
          h('div', { class: 'font-medium' }, `${row.message_count} 条`),
          h('div', { class: 'text-xs text-gray-500' }, `文字:${textCount} 语音:${audioCount}`)
        ]
      )
    },
  },
  // 会话时长字段暂时注释，业务逻辑未完善
  // {
  //   title: '会话时长',
  //   key: 'duration_consumed',
  //   width: 100,
  //   align: 'center',
  //   sorter: true,
  //   render(row) {
  //     const duration = row.duration_consumed || 0
  //     const minutes = Math.floor(duration / 60)
  //     const seconds = duration % 60
  //     return `${minutes}:${seconds.toString().padStart(2, '0')}`
  //   },
  // },
  {
    title: '最后消息',
    key: 'last_message',
    width: 200,
    align: 'left',
    ellipsis: { tooltip: true },
    render(row) {
      if (!row.last_message) return '-'
      const isAudio = row.last_message_type === 'audio'
      return h(
        'div',
        { class: 'last-message-cell' },
        [
          isAudio ? h(NIcon, { size: 14, class: 'mr-1' }, { default: () => h(TheIcon, { icon: 'material-symbols:mic' }) }) : null,
          h('span', row.last_message.substring(0, 30) + (row.last_message.length > 30 ? '...' : ''))
        ]
      )
    },
  },
  {
    title: '会话状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      const statusMap = {
        'active': { text: '进行中', type: 'success', icon: 'material-symbols:play-circle' },
        'ended': { text: '已结束', type: 'default', icon: 'material-symbols:check-circle' },
        'timeout': { text: '超时', type: 'warning', icon: 'material-symbols:timer-off' }
      }
      const status = statusMap[row.status] || { text: '未知', type: 'default', icon: 'material-symbols:help' }
      return h(
        NBadge,
        { dot: row.status === 'active', type: status.type },
        {
          default: () => h(
            NTag,
            { type: status.type, size: 'small' },
            {
              default: () => status.text,
              icon: () => h(TheIcon, { icon: status.icon, size: 12 })
            }
          )
        }
      )
    },
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    align: 'center',
    sorter: true,
    render(row) {
      return h(NTime, { time: new Date(row.created_at), format: 'yyyy-MM-dd HH:mm' })
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 280,
    align: 'center',
    fixed: 'right',
    render(row) {
      return h(
        NSpace,
        { size: 'small' },
        [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick: () => handleViewChatHistory(row),
            },
            {
              default: () => '详情',
              icon: () => h(TheIcon, { icon: 'material-symbols:visibility' })
            }
          ),
          h(
            NButton,
            {
              size: 'small',
              onClick: () => handleExportChatHistory(row),
            },
            {
              default: () => '导出',
              icon: () => h(TheIcon, { icon: 'material-symbols:download' })
            }
          ),
          ...(row.status === 'active' ? [
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleEndSession(row)
              },
              {
                trigger: () => h(
                  NButton,
                  {
                    size: 'small',
                    type: 'warning',
                  },
                  {
                    default: () => '结束',
                    icon: () => h(TheIcon, { icon: 'material-symbols:stop-circle' })
                  }
                ),
                default: () => '确定要结束这个会话吗？结束后将无法继续对话。'
              }
            )
          ] : []),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDeleteSession(row)
            },
            {
              trigger: () => h(
                NButton,
                {
                  size: 'small',
                  type: 'error',
                },
                {
                  default: () => '删除',
                  icon: () => h(TheIcon, { icon: 'material-symbols:delete' })
                }
              ),
              default: () => '确定要删除这个会话吗？此操作将永久删除该会话及其所有聊天记录，无法恢复！'
            }
          )
        ]
      )
    },
  },
]

// 注：下拉菜单操作已改为直接按钮操作，此函数已不再使用

// 查看聊天历史详情
const handleViewChatHistory = async (session) => {
  try {
    currentSessionInfo.value = session
    currentMessagePage.value = 1
    await loadChatMessages(session.session_id)
    showChatDetail.value = true
  } catch (error) {
    console.error('获取聊天历史失败:', error)
    window.$message?.error('获取聊天历史失败')
  }
}

// 加载聊天消息
const loadChatMessages = async (sessionId, page = 1) => {
  try {
    loadingMessages.value = true
    const { data } = await api.getChatHistory({
      session_id: sessionId,
      page,
      page_size: currentMessagePageSize.value
    })

    if (page === 1) {
      currentChatHistory.value = data.messages || []
    } else {
      currentChatHistory.value.push(...(data.messages || []))
    }

    messageTotal.value = data.total || 0
    currentMessagePage.value = page
  } catch (error) {
    console.error('加载聊天消息失败:', error)
    // 使用示例数据作为后备
    if (page === 1) {
      currentChatHistory.value = [
        {
          id: 1,
          message_id: 'msg_001',
          role: 'user',
          content: '你好，我想咨询一下你们的服务',
          message_type: 'text',
          created_at: new Date().toISOString(),
        },
        {
          id: 2,
          message_id: 'msg_002',
          role: 'assistant',
          content: '您好！很高兴为您服务。请问您想了解哪方面的内容呢？',
          message_type: 'text',
          processing_time: 1.2,
          created_at: new Date().toISOString(),
        },
        {
          id: 3,
          message_id: 'msg_003',
          role: 'user',
          content: '我想了解一下AI聊天的功能',
          message_type: 'audio',
          audio_duration: 3.5,
          transcription_text: '我想了解一下AI聊天的功能',
          transcription_confidence: 0.95,
          created_at: new Date().toISOString(),
        },
        {
          id: 4,
          message_id: 'msg_004',
          role: 'assistant',
          content: '我们的AI聊天功能可以帮助您解决各种问题，支持多轮对话，具有记忆能力，还支持语音识别功能...',
          message_type: 'text',
          processing_time: 2.1,
          tokens_used: 45,
          created_at: new Date().toISOString(),
        },
      ]
      messageTotal.value = 4
    }
  } finally {
    loadingMessages.value = false
  }
}

// 导出聊天记录
const handleExportChatHistory = async (session) => {
  try {
    const { data } = await api.exportChatHistory({ session_id: session.session_id })
    // 创建下载链接
    const blob = new Blob([data], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `chat_${session.session_id}_${formatDate(new Date(), 'YYYYMMDD_HHmmss')}.json`
    link.click()
    window.URL.revokeObjectURL(url)
    window.$message?.success('导出成功')
  } catch (error) {
    console.error('导出聊天记录失败:', error)
    window.$message?.error('导出失败')
  }
}

// 结束会话
const handleEndSession = async (session) => {
  try {
    await api.endChatSession({ session_id: session.session_id })
    window.$message?.success('会话已结束')
    $table.value?.handleSearch()
  } catch (error) {
    console.error('结束会话失败:', error)
    window.$message?.error('结束会话失败')
  }
}

// 删除会话
const handleDeleteSession = async (session) => {
  try {
    await api.deleteChatSession({ session_id: session.session_id })
    window.$message?.success('删除成功')
    $table.value?.handleSearch()
  } catch (error) {
    console.error('删除会话失败:', error)
    window.$message?.error('删除失败')
  }
}

// 获取消息样式类
const getMessageClass = (role) => {
  return role === 'user' ? 'user-message' : 'assistant-message'
}

// 获取消息类型图标
const getMessageTypeIcon = (messageType) => {
  return messageType === 'audio' ? 'material-symbols:mic' : 'material-symbols:chat'
}

// 格式化处理时间
const formatProcessingTime = (time) => {
  if (!time) return '-'
  return `${time.toFixed(2)}s`
}

// 格式化置信度
const formatConfidence = (confidence) => {
  if (!confidence) return '-'
  return `${(confidence * 100).toFixed(1)}%`
}

// 获取聊天会话列表
const getChatSessionList = async (params) => {
  try {
    const { data } = await api.getChatSessionList(params)
    return {
      data: data.sessions || [],
      total: data.total || 0
    }
  } catch (error) {
    console.error('获取聊天会话列表失败:', error)
    // 返回示例数据作为后备
    return {
      data: [
        {
          id: 1,
          session_id: 'chat_user001_1_1704096000',
          user_id: 'user_001',
          nickname: '张三',
          phone: '138****8888',
          message_count: 12,
          text_message_count: 8,
          audio_message_count: 4,
          duration_consumed: 1800,
          last_message: '谢谢你的帮助，我了解了',
          last_message_type: 'text',
          status: 'ended',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: 2,
          session_id: 'chat_user002_1_1704182400',
          user_id: 'user_002',
          nickname: '李四',
          phone: '139****9999',
          message_count: 6,
          text_message_count: 4,
          audio_message_count: 2,
          duration_consumed: 900,
          last_message: '语音消息',
          last_message_type: 'audio',
          status: 'active',
          created_at: new Date(Date.now() - 3600000).toISOString(),
          updated_at: new Date(Date.now() - 300000).toISOString(),
        }
      ],
      total: 2
    }
  }
}

// 计算统计数据
const computedStats = computed(() => {
  if (!statistics.value || Object.keys(statistics.value).length === 0) {
    return {
      totalSessions: 0,
      activeSessions: 0,
      totalMessages: 0,
      audioMessages: 0,
      // avgSessionDuration: 0,  // 暂时注释，业务逻辑未完善
      avgMessagesPerSession: 0
    }
  }

  return {
    totalSessions: statistics.value.total_sessions || 0,
    activeSessions: statistics.value.active_sessions || 0,
    totalMessages: statistics.value.total_messages || 0,
    audioMessages: statistics.value.audio_messages || 0,
    // avgSessionDuration: statistics.value.avg_session_duration || 0,  // 暂时注释，业务逻辑未完善
    avgMessagesPerSession: statistics.value.avg_messages_per_session || 0
  }
})
</script>

<template>
  <CommonPage show-footer title="AIRelief聊天数据管理">
    <template #action>
      <NSpace>
        <NButton @click="showStatistics = true">
          <TheIcon icon="material-symbols:analytics" :size="18" class="mr-1" />
          数据统计
        </NButton>
        <NButton @click="debugMessageCount">
          <TheIcon icon="material-symbols:bug-report" :size="18" class="mr-1" />
          调试统计
        </NButton>
        <NButton type="warning" @click="fixMessageCount">
          <TheIcon icon="material-symbols:build" :size="18" class="mr-1" />
          修复计数
        </NButton>
        <NButton type="primary" @click="$table?.handleSearch()">
          <TheIcon icon="material-symbols:refresh" :size="18" class="mr-1" />
          刷新
        </NButton>
      </NSpace>
    </template>

    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16" class="mb-4">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总会话数" :value="computedStats.totalSessions">
            <template #prefix>
              <NIcon color="#18a058">
                <TheIcon icon="material-symbols:chat" />
              </NIcon>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="活跃会话" :value="computedStats.activeSessions">
            <template #prefix>
              <NIcon color="#2080f0">
                <TheIcon icon="material-symbols:play-circle" />
              </NIcon>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总消息数" :value="computedStats.totalMessages">
            <template #prefix>
              <NIcon color="#f0a020">
                <TheIcon icon="material-symbols:message" />
              </NIcon>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="语音消息" :value="computedStats.audioMessages">
            <template #prefix>
              <NIcon color="#d03050">
                <TheIcon icon="material-symbols:mic" />
              </NIcon>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getChatSessionList"
      :scroll-x="1500"
    >
      <template #queryBar>
        <QueryBarItem label="ID" :label-width="60">
          <NInput
            v-model:value="queryItems.id"
            clearable
            type="text"
            placeholder="请输入ID"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="用户ID" :label-width="60">
          <NInput
            v-model:value="queryItems.user_id"
            clearable
            type="text"
            placeholder="请输入用户ID"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="昵称" :label-width="60">
          <NInput
            v-model:value="queryItems.nickname"
            clearable
            type="text"
            placeholder="请输入昵称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="手机号" :label-width="60">
          <NInput
            v-model:value="queryItems.phone"
            clearable
            type="text"
            placeholder="请输入手机号"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="时间范围" :label-width="80">
          <NDatePicker
            v-model:value="queryItems.date_range"
            type="datetimerange"
            clearable
            placeholder="选择时间范围"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 聊天详情抽屉 -->
    <NDrawer
      v-model:show="showChatDetail"
      :width="800"
      placement="right"
    >
      <NDrawerContent title="聊天记录详情">
        <!-- 会话信息 -->
        <NCard size="small" class="mb-4">
          <template #header>
            <NSpace align="center">
              <TheIcon icon="material-symbols:info" />
              <span>会话信息</span>
            </NSpace>
          </template>

          <NDescriptions :column="2" bordered>
            <NDescriptionsItem label="会话ID">
              <NText code>{{ currentSessionInfo.session_id }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="用户ID">
              <NText code>{{ currentSessionInfo.user_id }}</NText>
            </NDescriptionsItem>
            <NDescriptionsItem label="用户昵称">
              {{ currentSessionInfo.nickname || '未设置' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="手机号">
              {{ currentSessionInfo.phone || '未绑定' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="消息总数">
              <NBadge :value="currentSessionInfo.message_count" type="info">
                <span>{{ currentSessionInfo.message_count }} 条</span>
              </NBadge>
            </NDescriptionsItem>
            <!-- 会话时长暂时注释，业务逻辑未完善 -->
            <!-- <NDescriptionsItem label="会话时长">
              {{ Math.floor((currentSessionInfo.duration_consumed || 0) / 60) }}:{{ ((currentSessionInfo.duration_consumed || 0) % 60).toString().padStart(2, '0') }}
            </NDescriptionsItem> -->
            <NDescriptionsItem label="会话状态">
              <NTag
                :type="currentSessionInfo.status === 'active' ? 'success' : currentSessionInfo.status === 'timeout' ? 'warning' : 'default'"
                size="small"
              >
                {{ currentSessionInfo.status === 'active' ? '进行中' : currentSessionInfo.status === 'timeout' ? '超时' : '已结束' }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="创建时间">
              <NTime :time="new Date(currentSessionInfo.created_at)" format="yyyy-MM-dd HH:mm:ss" />
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <!-- 聊天历史 -->
        <NCard size="small">
          <template #header>
            <NSpace align="center" justify="space-between">
              <NSpace align="center">
                <TheIcon icon="material-symbols:chat" />
                <span>对话历史</span>
                <NBadge :value="messageTotal" type="info" />
              </NSpace>
              <NSpace>
                <NButton size="small" @click="loadChatMessages(currentSessionInfo.session_id, 1)">
                  <TheIcon icon="material-symbols:refresh" class="mr-1" />
                  刷新
                </NButton>
              </NSpace>
            </NSpace>
          </template>

          <div v-if="loadingMessages" class="text-center py-4">
            <NIcon size="24" class="animate-spin">
              <TheIcon icon="material-symbols:progress-activity" />
            </NIcon>
            <div class="mt-2">加载中...</div>
          </div>

          <NEmpty v-else-if="currentChatHistory.length === 0" description="暂无聊天记录" />

          <NList v-else>
            <NListItem
              v-for="message in currentChatHistory"
              :key="message.id"
              :class="getMessageClass(message.role)"
            >
              <NThing>
                <template #header>
                  <NSpace align="center" justify="space-between">
                    <NSpace align="center">
                      <NTag
                        :type="message.role === 'user' ? 'info' : 'success'"
                        size="small"
                      >
                        <template #icon>
                          <TheIcon :icon="message.role === 'user' ? 'material-symbols:person' : 'material-symbols:smart-toy'" />
                        </template>
                        {{ message.role === 'user' ? '用户' : 'AI助手' }}
                      </NTag>

                      <NTag
                        v-if="message.message_type === 'audio'"
                        type="warning"
                        size="small"
                      >
                        <template #icon>
                          <TheIcon icon="material-symbols:mic" />
                        </template>
                        语音 {{ message.audio_duration }}s
                      </NTag>

                      <span class="text-sm text-gray-500">
                        <NTime :time="new Date(message.created_at)" format="MM-dd HH:mm:ss" />
                      </span>
                    </NSpace>

                    <NSpace v-if="message.role === 'assistant'" align="center">
                      <NTooltip v-if="message.processing_time">
                        <template #trigger>
                          <NTag size="tiny" type="default">
                            {{ formatProcessingTime(message.processing_time) }}
                          </NTag>
                        </template>
                        AI处理时间
                      </NTooltip>

                      <NTooltip v-if="message.tokens_used">
                        <template #trigger>
                          <NTag size="tiny" type="info">
                            {{ message.tokens_used }} tokens
                          </NTag>
                        </template>
                        消耗的Token数量
                      </NTooltip>
                    </NSpace>
                  </NSpace>
                </template>

                <div class="message-content">
                  <!-- 语音消息特殊处理 -->
                  <div v-if="message.message_type === 'audio'" class="audio-message">
                    <div v-if="message.transcription_text" class="transcription">
                      <NAlert type="info" size="small" class="mb-2">
                        <template #icon>
                          <TheIcon icon="material-symbols:transcribe" />
                        </template>
                        <template #header>
                          语音识别结果
                          <NTag v-if="message.transcription_confidence" size="tiny" :type="message.transcription_confidence > 0.8 ? 'success' : 'warning'" class="ml-2">
                            置信度: {{ formatConfidence(message.transcription_confidence) }}
                          </NTag>
                        </template>
                        {{ message.transcription_text }}
                      </NAlert>
                    </div>

                    <div v-if="message.audio_url" class="audio-player">
                      <audio controls class="w-full">
                        <source :src="message.audio_url" type="audio/wav">
                        您的浏览器不支持音频播放
                      </audio>
                    </div>
                  </div>

                  <!-- 文本消息 -->
                  <div v-else class="text-message">
                    {{ message.content }}
                  </div>
                </div>
              </NThing>
            </NListItem>
          </NList>

          <!-- 分页 -->
          <div v-if="messageTotal > currentMessagePageSize" class="mt-4 text-center">
            <NPagination
              v-model:page="currentMessagePage"
              :page-size="currentMessagePageSize"
              :item-count="messageTotal"
              show-size-picker
              :page-sizes="[10, 20, 50]"
              @update:page="loadChatMessages(currentSessionInfo.session_id, $event)"
              @update:page-size="currentMessagePageSize = $event; loadChatMessages(currentSessionInfo.session_id, 1)"
            />
          </div>
        </NCard>
      </NDrawerContent>
    </NDrawer>

    <!-- 统计数据模态框 -->
    <NModal v-model:show="showStatistics" preset="card" title="聊天数据统计" style="width: 800px">
      <NGrid :cols="2" :x-gap="16" :y-gap="16">
        <NGridItem>
          <NCard title="会话统计" size="small">
            <NSpace vertical>
              <NStatistic label="总会话数" :value="computedStats.totalSessions" />
              <NStatistic label="活跃会话" :value="computedStats.activeSessions" />
              <!-- 平均会话时长暂时注释，业务逻辑未完善 -->
              <!-- <NStatistic label="平均会话时长" :value="Math.round(computedStats.avgSessionDuration / 60)" suffix="分钟" /> -->
            </NSpace>
          </NCard>
        </NGridItem>

        <NGridItem>
          <NCard title="消息统计" size="small">
            <NSpace vertical>
              <NStatistic label="总消息数" :value="computedStats.totalMessages" />
              <NStatistic label="语音消息" :value="computedStats.audioMessages" />
              <NStatistic label="平均消息/会话" :value="computedStats.avgMessagesPerSession.toFixed(1)" />
            </NSpace>
          </NCard>
        </NGridItem>

        <NGridItem>
          <NCard title="语音消息占比" size="small">
            <NProgress
              type="circle"
              :percentage="computedStats.totalMessages > 0 ? (computedStats.audioMessages / computedStats.totalMessages * 100) : 0"
              :stroke-width="8"
            >
              {{ computedStats.totalMessages > 0 ? ((computedStats.audioMessages / computedStats.totalMessages * 100).toFixed(1) + '%') : '0%' }}
            </NProgress>
          </NCard>
        </NGridItem>

        <NGridItem>
          <NCard title="活跃会话占比" size="small">
            <NProgress
              type="circle"
              :percentage="computedStats.totalSessions > 0 ? (computedStats.activeSessions / computedStats.totalSessions * 100) : 0"
              :stroke-width="8"
              status="success"
            >
              {{ computedStats.totalSessions > 0 ? ((computedStats.activeSessions / computedStats.totalSessions * 100).toFixed(1) + '%') : '0%' }}
            </NProgress>
          </NCard>
        </NGridItem>
      </NGrid>
    </NModal>
  </CommonPage>
</template>

<style scoped>
.user-message {
  background-color: #f0f8ff;
  border-left: 3px solid #1890ff;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
}

.assistant-message {
  background-color: #f6ffed;
  border-left: 3px solid #52c41a;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
}

.message-content {
  margin-top: 8px;
  line-height: 1.6;
  word-wrap: break-word;
}

.audio-message {
  margin-top: 8px;
}

.transcription {
  margin-bottom: 8px;
}

.audio-player {
  margin-top: 8px;
}

.text-message {
  white-space: pre-wrap;
  word-break: break-word;
}

.user-info-cell {
  text-align: left;
}

.message-stats-cell {
  text-align: center;
}

.last-message-cell {
  display: flex;
  align-items: center;
  text-align: left;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-sm {
  font-size: 12px;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-xs {
  font-size: 10px;
}

.text-blue-500 {
  color: #3b82f6;
}

.font-medium {
  font-weight: 500;
}

.cursor-pointer {
  cursor: pointer;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-info-cell,
  .message-stats-cell,
  .last-message-cell {
    font-size: 12px;
  }

  .text-xs {
    font-size: 9px;
  }
}
</style>