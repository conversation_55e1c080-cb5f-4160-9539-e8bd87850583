<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives, computed, watch } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSpace,
  NSwitch,
  NTag,
  NPopconfirm,
  NSelect,
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: 'AIRelief时长管理' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 天数选择器的选项
const durationDaysOptions = [
  { label: '1天', value: 1 },
  { label: '7天', value: 7 },
  { label: '15天', value: 15 },
  { label: '30天', value: 30 },
  { label: '60天', value: 60 },
  { label: '90天', value: 90 },
  { label: '180天', value: 180 },
  { label: '365天', value: 365 },
  { label: '自定义', value: 'custom' },
]

// 当前选择的天数
const selectedDays = ref(null)
const customDays = ref(null)

// 监听天数变化，自动计算秒数
watch([selectedDays, customDays], ([days, custom]) => {
  if (!modalForm.value) return
  
  let actualDays = days
  if (days === 'custom' && custom) {
    actualDays = custom
  }
  
  if (actualDays && typeof actualDays === 'number') {
    // 计算秒数：天数 * 24小时 * 60分钟 * 60秒
    modalForm.value.duration_seconds = actualDays * 24 * 60 * 60
    // 自动更新时长描述
    modalForm.value.duration = `${actualDays}天`
  }
}, { immediate: false })

/**
 * 根据原始需求，时长管理功能包括：
 * 1. 编辑时长和价格（不支持新增）
 * 2. 显示套餐列表
 * 
 * 功能说明：
 * - 系统预设几种套餐类型（月卡、季度卡、年卡等）
 * - 管理员只能编辑现有套餐的价格和时长
 * - 不支持新增或删除套餐类型
 * - 套餐与兑换码系统对接
 */

const {
  modalVisible,
  modalTitle,  
  modalLoading,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
} = useCRUD({
  name: '时长套餐',
  initForm: {},
  doCreate: () => Promise.reject('不支持创建套餐'),
  doUpdate: (data) => {
    // 调用真实的更新套餐API
    return api.updateDurationPackage(data)
  },
  doDelete: () => Promise.reject('不支持删除套餐'),
  refresh: () => $table.value?.handleSearch(),
})

// 监听编辑弹窗打开，初始化天数选择器
watch(modalVisible, (visible) => {
  if (visible && modalForm.value) {
    // 根据当前的duration_seconds反推天数
    const seconds = modalForm.value.duration_seconds
    if (seconds) {
      const days = Math.round(seconds / (24 * 60 * 60))
      
      // 检查是否是预设的天数选项
      const presetOption = durationDaysOptions.find(option => option.value === days)
      if (presetOption) {
        selectedDays.value = days
        customDays.value = null
      } else {
        selectedDays.value = 'custom'
        customDays.value = days
      }
    } else {
      selectedDays.value = null
      customDays.value = null
    }
  }
})

onMounted(() => {
  $table.value?.handleSearch()
})

const columns = [
  {
    title: '套餐ID',
    key: 'id',
    width: 100,
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '套餐名称',
    key: 'name',
    width: 120,
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '时长',
    key: 'duration',
    width: 120,
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '价格(元)',
    key: 'price',
    width: 100,
    align: 'center',
    render(row) {
      return `¥${row.price}`
    },
  },
  {
    title: '原价(元)',
    key: 'original_price',
    width: 100,
    align: 'center',
    render(row) {
      return row.original_price ? `¥${row.original_price}` : '-'
    },
  },
  {
    title: '标签',
    key: 'tag',
    width: 80,
    align: 'center',
    render(row) {
      return row.tag ? h(
        NTag,
        { type: 'info', size: 'small' },
        { default: () => row.tag }
      ) : '-'
    },
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { 
          type: row.is_active ? 'success' : 'error',
          size: 'small'
        },
        { default: () => row.is_active ? '启用' : '禁用' }
      )
    },
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    align: 'center',
    render(row) {
      return formatDate(row.created_at, 'YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '更新时间',
    key: 'updated_at',
    width: 150,
    align: 'center',
    render(row) {
      return formatDate(row.updated_at, 'YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: renderIcon('material-symbols:edit', { size: 16 }),
          }
        ),
      ]
    },
  },
]

const validateForm = {
  name: [
    {
      required: true,
      message: '请输入套餐名称',
      trigger: ['input', 'blur'],
    },
  ],
  duration: [
    {
      required: true,
      message: '请输入时长描述',
      trigger: ['input', 'blur'],
    },
  ],
  duration_seconds: [
    {
      required: true,
      type: 'number',
      message: '请输入时长秒数',
      trigger: ['change', 'blur'],
    },
  ],
  price: [
    {
      required: true,
      type: 'number',
      message: '请输入价格',
      trigger: ['change', 'blur'],
    },
  ],
}

// 获取套餐列表，调用真实API
const getDurationPackageList = (params) => {
  return api.getDurationPackageList(params)
}
</script>

<template>
  <CommonPage show-footer title="AIRelief时长管理">
    <template #action>
      <NSpace>
        <NButton type="primary" @click="$table?.handleSearch()">
          <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5" />刷新
        </NButton>
      </NSpace>
    </template>
    
    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getDurationPackageList"
    >
    </CrudTable>

    <!-- 编辑弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :rules="validateForm"
      >
        <NFormItem label="套餐名称" path="name">
          <NInput v-model:value="modalForm.name" clearable placeholder="请输入套餐名称" />
        </NFormItem>
        <NFormItem label="时长描述" path="duration">
          <NInput v-model:value="modalForm.duration" clearable placeholder="请输入时长描述，如：30天" />
        </NFormItem>
        <NFormItem label="选择天数">
          <NSelect
            v-model:value="selectedDays"
            :options="durationDaysOptions"
            placeholder="天数选择已禁用"
            clearable
            style="width: 100%"
            disabled
          />
        </NFormItem>
        <NFormItem v-if="selectedDays === 'custom'" label="自定义天数">
          <NInputNumber
            v-model:value="customDays"
            placeholder="天数修改已禁用"
            :min="1"
            :step="1"
            style="width: 100%"
            disabled
          />
        </NFormItem>
        <NFormItem label="时长(秒)" path="duration_seconds">
          <NInputNumber
            v-model:value="modalForm.duration_seconds"
            placeholder="时长秒数修改已禁用"
            :min="0"
            :step="1"
            style="width: 100%"
            disabled
          />
        </NFormItem>
        <NFormItem label="价格(元)" path="price">
          <NInputNumber
            v-model:value="modalForm.price"
            placeholder="请输入价格"
            :min="0"
            :step="0.01"
            :precision="2"
            style="width: 100%"
          />
        </NFormItem>
        <NFormItem label="原价(元)">
          <NInputNumber
            v-model:value="modalForm.original_price"
            placeholder="请输入原价"
            :min="0"
            :step="0.01"
            :precision="2"
            style="width: 100%"
          />
        </NFormItem>
        <NFormItem label="标签">
          <NInput v-model:value="modalForm.tag" clearable placeholder="请输入标签，如：推荐、热门" />
        </NFormItem>
        <NFormItem label="状态">
          <NSwitch
            v-model:value="modalForm.is_active"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </NSwitch>
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template> 