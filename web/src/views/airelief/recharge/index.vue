<script setup>
import { h, onMounted, ref } from 'vue'
import {
  NButton,
  NTag,
  NSpace,
  NInput,
  NDatePicker,
  NModal,
  NCard,
  NDescriptions,
  NDescriptionsItem,
  NDivider,
  NStatistic,
  NGrid,
  NGridItem,
  NDropdown,
  useMessage,
  useDialog
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate } from '@/utils'
import api from '@/api'

defineOptions({ name: 'AIRelief充值管理' })

const $table = ref(null)
const queryItems = ref({})
const message = useMessage()
const dialog = useDialog()

// 订单详情弹窗
const showOrderDetail = ref(false)
const orderDetail = ref({})

// 统计数据
const statistics = ref({
  overview: {
    total_orders: 0,
    paid_orders: 0,
    pending_orders: 0,
    failed_orders: 0,
    cancelled_orders: 0,
    refunded_orders: 0,
    success_rate: 0,
    total_amount: 0,
    total_discount: 0
  }
})

onMounted(() => {
  $table.value?.handleSearch()
  loadStatistics()
})



const columns = [
  {
    title: '订单号',
    key: 'order_id',
    width: 180,
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '用户昵称',
    key: 'user_info',
    width: 120,
    align: 'center',
    render(row) {
      return row.user_info?.nickname || '-'
    },
  },
  {
    title: '用户ID',
    key: 'user_id',
    width: 120,
    align: 'center',
    render(row) {
      return row.user_info?.user_id || '-'
    },
  },
  {
    title: '套餐信息',
    key: 'package_name',
    width: 150,
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '实付金额',
    key: 'amount',
    width: 100,
    align: 'center',
    render(row) {
      return `¥${row.amount || 0}`
    },
  },
  {
    title: '原价',
    key: 'original_amount',
    width: 100,
    align: 'center',
    render(row) {
      return `¥${row.original_amount || 0}`
    },
  },
  {
    title: '优惠券',
    key: 'coupon_code',
    width: 120,
    align: 'center',
    render(row) {
      return row.coupon_code ? h(
        NTag,
        { type: 'success', size: 'small' },
        { default: () => row.coupon_code }
      ) : '-'
    },
  },
  {
    title: '支付方式',
    key: 'payment_method',
    width: 100,
    align: 'center',
    render(row) {
      const methodMap = {
        'wechat': { text: '微信支付', type: 'success' },
        'alipay': { text: '支付宝', type: 'info' }
      }
      const method = methodMap[row.payment_method] || { text: row.payment_method, type: 'default' }
      return h(
        NTag,
        { type: method.type, size: 'small' },
        { default: () => method.text }
      )
    },
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      const statusMap = {
        'pending': { text: '待支付', type: 'warning' },
        'paid': { text: '已支付', type: 'success' },
        'failed': { text: '支付失败', type: 'error' },
        'cancelled': { text: '已取消', type: 'default' },
        'refunded': { text: '已退款', type: 'info' },
        'expired': { text: '已过期', type: 'default' }
      }
      const status = statusMap[row.status] || { text: '未知', type: 'default' }
      return h(
        NTag,
        { type: status.type, size: 'small' },
        { default: () => status.text }
      )
    },
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    align: 'center',
    render(row) {
      return formatDate(row.created_at, 'YYYY-MM-DD HH:mm:ss')
    },
  },
  {
    title: '支付时间',
    key: 'pay_time',
    width: 150,
    align: 'center',
    render(row) {
      return row.pay_time ? formatDate(row.pay_time, 'YYYY-MM-DD HH:mm:ss') : '-'
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    render(row) {
      // 确保 row 存在且有 status 属性
      if (!row || typeof row !== 'object') {
        return h('span', '-')
      }

      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              ghost: true,
              onClick: () => handleAction('detail', row)
            },
            { default: () => '详情' }
          ),
          row.status === 'pending' && h(
            NButton,
            {
              size: 'small',
              type: 'warning',
              ghost: true,
              onClick: () => handleAction('cancel', row)
            },
            { default: () => '取消' }
          ),
          row.status === 'paid' && h(
            NButton,
            {
              size: 'small',
              type: 'error',
              ghost: true,
              onClick: () => handleAction('refund', row)
            },
            { default: () => '退款' }
          )
        ].filter(Boolean)
      })
    },
  },
]

// 获取订单列表
const getRechargeList = async (params) => {
  try {
    console.log('请求参数:', params)
    const response = await api.getRechargeList(params)
    console.log('API响应:', response)

    // 确保返回的数据格式正确
    if (response && response.data) {
      // 检查数据在 items 字段中（后端返回格式）
      const items = response.data.items || response.data.data || []
      const result = {
        data: Array.isArray(items) ? items : [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        size: response.data.size || 20,
        pages: response.data.pages || 1
      }
      console.log('处理后的数据:', result)
      return result
    }

    // 如果直接返回数组
    if (Array.isArray(response)) {
      return {
        data: response,
        total: response.length
      }
    }

    // 默认返回空数组
    console.log('返回默认空数据')
    return {
      data: [],
      total: 0
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    message.error('获取订单列表失败')
    return {
      data: [],
      total: 0
    }
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const res = await api.getOrderStatistics()
    if (res && (res.code === 200 || res.code === 0)) {
      // 确保统计数据格式正确
      const data = res.data || res
      statistics.value = {
        overview: {
          total_orders: data.overview?.total_orders || 0,
          paid_orders: data.overview?.paid_orders || 0,
          pending_orders: data.overview?.pending_orders || 0,
          failed_orders: data.overview?.failed_orders || 0,
          cancelled_orders: data.overview?.cancelled_orders || 0,
          refunded_orders: data.overview?.refunded_orders || 0,
          success_rate: data.overview?.success_rate || 0,
          total_amount: data.overview?.total_amount || 0,
          total_discount: data.overview?.total_discount || 0
        },
        daily_stats: Array.isArray(data.daily_stats) ? data.daily_stats : []
      }
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    message.error('加载统计数据失败')
  }
}

// 查看订单详情
const viewOrderDetail = async (orderId) => {
  try {
    const res = await api.getOrderDetail(orderId)
    if (res.code === 200) {
      orderDetail.value = res.data
      showOrderDetail.value = true
    }
  } catch (error) {
    message.error('获取订单详情失败')
  }
}

// 更新订单状态
const updateOrderStatus = async (orderId, newStatus, reason = '') => {
  try {
    const res = await api.updateOrderStatus(orderId, {
      new_status: newStatus,
      reason: reason
    })
    if (res.code === 200) {
      message.success('订单状态更新成功')
      $table.value?.handleSearch()
    }
  } catch (error) {
    message.error('更新订单状态失败')
  }
}

// 处理操作
const handleAction = (key, row) => {
  switch (key) {
    case 'detail':
      viewOrderDetail(row.order_id)
      break
    case 'cancel':
      dialog.warning({
        title: '确认取消订单',
        content: `确定要取消订单 ${row.order_id} 吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          updateOrderStatus(row.order_id, 'cancelled', '管理员取消')
        }
      })
      break
    case 'refund':
      dialog.warning({
        title: '确认申请退款',
        content: `确定要为订单 ${row.order_id} 申请退款吗？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          updateOrderStatus(row.order_id, 'refunded', '管理员退款')
        }
      })
      break
  }
}

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  if (value && value.length === 2) {
    queryItems.value.start_date = new Date(value[0]).toISOString()
    queryItems.value.end_date = new Date(value[1]).toISOString()
  } else {
    queryItems.value.start_date = ''
    queryItems.value.end_date = ''
  }
  $table.value?.handleSearch()
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'paid': 'success',
    'failed': 'error',
    'cancelled': 'default',
    'refunded': 'info',
    'expired': 'default'
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'failed': '支付失败',
    'cancelled': '已取消',
    'refunded': '已退款',
    'expired': '已过期'
  }
  return statusMap[status] || '未知'
}
</script>

<template>
  <CommonPage show-footer title="AIRelief充值管理">
    <template #action>
      <NSpace>
        <NButton type="primary" @click="$table?.handleSearch()">
          <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5" />刷新
        </NButton>
        <NButton @click="loadStatistics">
          <TheIcon icon="material-symbols:analytics" :size="18" class="mr-5" />统计
        </NButton>
      </NSpace>
    </template>

    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16" class="mb-4">
      <NGridItem>
        <NCard>
          <NStatistic label="总订单数" :value="statistics.overview.total_orders" />
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard>
          <NStatistic label="已支付订单" :value="statistics.overview.paid_orders" />
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard>
          <NStatistic label="总收入" :value="`¥${statistics.overview.total_amount}`" />
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard>
          <NStatistic label="支付成功率" :value="`${statistics.overview.success_rate}%`" />
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getRechargeList"
    >
      <template #queryBar>
        <QueryBarItem label="订单号" :label-width="80">
          <NInput
            v-model:value="queryItems.order_id"
            clearable
            type="text"
            placeholder="请输入订单号"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="用户ID" :label-width="80">
          <NInput
            v-model:value="queryItems.user_id"
            clearable
            type="text"
            placeholder="请输入用户ID"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="昵称" :label-width="80">
          <NInput
            v-model:value="queryItems.nickname"
            clearable
            type="text"
            placeholder="请输入昵称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="手机号" :label-width="80">
          <NInput
            v-model:value="queryItems.phone"
            clearable
            type="text"
            placeholder="请输入手机号"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="金额" :label-width="80">
          <NInput
            v-model:value="queryItems.amount"
            clearable
            type="text"
            placeholder="请输入金额"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="套餐" :label-width="80">
          <NInput
            v-model:value="queryItems.package_name"
            clearable
            type="text"
            placeholder="请输入套餐名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="充值时间" :label-width="80">
          <NDatePicker
            v-model:value="queryItems.dateRange"
            type="daterange"
            clearable
            placeholder="请选择充值时间范围"
            @update:value="handleDateRangeChange"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 订单详情弹窗 -->
    <NModal v-model:show="showOrderDetail" preset="card" title="订单详情" style="width: 800px">
      <NDescriptions :column="2" bordered>
        <NDescriptionsItem label="订单号">
          {{ orderDetail.order_id }}
        </NDescriptionsItem>
        <NDescriptionsItem label="订单状态">
          <NTag :type="getStatusType(orderDetail.order_details?.status)">
            {{ getStatusText(orderDetail.order_details?.status) }}
          </NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="用户昵称">
          {{ orderDetail.user_info?.nickname }}
        </NDescriptionsItem>
        <NDescriptionsItem label="用户ID">
          {{ orderDetail.user_info?.user_id }}
        </NDescriptionsItem>
        <NDescriptionsItem label="手机号">
          {{ orderDetail.user_info?.phone || '未绑定' }}
        </NDescriptionsItem>
        <NDescriptionsItem label="套餐名称">
          {{ orderDetail.package_info?.name }}
        </NDescriptionsItem>
        <NDescriptionsItem label="套餐时长">
          {{ orderDetail.package_info?.duration }}
        </NDescriptionsItem>
        <NDescriptionsItem label="原价">
          ¥{{ orderDetail.order_details?.original_amount }}
        </NDescriptionsItem>
        <NDescriptionsItem label="实付金额">
          ¥{{ orderDetail.order_details?.amount }}
        </NDescriptionsItem>
        <NDescriptionsItem label="优惠金额">
          ¥{{ orderDetail.order_details?.discount_amount || 0 }}
        </NDescriptionsItem>
        <NDescriptionsItem label="优惠券">
          {{ orderDetail.order_details?.coupon_code || '无' }}
        </NDescriptionsItem>
        <NDescriptionsItem label="支付方式">
          {{ orderDetail.order_details?.payment_method }}
        </NDescriptionsItem>
        <NDescriptionsItem label="创建时间">
          {{ formatDate(orderDetail.timestamps?.created_at, 'YYYY-MM-DD HH:mm:ss') }}
        </NDescriptionsItem>
        <NDescriptionsItem label="支付时间">
          {{ orderDetail.timestamps?.pay_time ? formatDate(orderDetail.timestamps.pay_time, 'YYYY-MM-DD HH:mm:ss') : '未支付' }}
        </NDescriptionsItem>
      </NDescriptions>

      <NDivider />

      <!-- 微信支付信息 -->
      <h3>微信支付信息</h3>
      <NDescriptions :column="2" bordered>
        <NDescriptionsItem label="微信交易号">
          {{ orderDetail.wechat_pay_info?.transaction_id || '无' }}
        </NDescriptionsItem>
        <NDescriptionsItem label="预支付ID">
          {{ orderDetail.wechat_pay_info?.prepay_id || '无' }}
        </NDescriptionsItem>
        <NDescriptionsItem label="银行类型">
          {{ orderDetail.wechat_pay_info?.bank_type || '无' }}
        </NDescriptionsItem>
        <NDescriptionsItem label="现金支付金额">
          {{ orderDetail.wechat_pay_info?.cash_fee ? `${orderDetail.wechat_pay_info.cash_fee}分` : '无' }}
        </NDescriptionsItem>
      </NDescriptions>

      <!-- 优惠券信息 -->
      <template v-if="orderDetail.coupon_info">
        <NDivider />
        <h3>优惠券信息</h3>
        <NDescriptions :column="2" bordered>
          <NDescriptionsItem label="优惠券代码">
            {{ orderDetail.coupon_info.code }}
          </NDescriptionsItem>
          <NDescriptionsItem label="优惠券类型">
            {{ orderDetail.coupon_info.type }}
          </NDescriptionsItem>
          <NDescriptionsItem label="优惠金额">
            ¥{{ orderDetail.coupon_info.value }}
          </NDescriptionsItem>
          <NDescriptionsItem label="使用时间">
            {{ formatDate(orderDetail.coupon_info.used_at, 'YYYY-MM-DD HH:mm:ss') }}
          </NDescriptionsItem>
        </NDescriptions>
      </template>
    </NModal>
  </CommonPage>
</template>