<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives, computed, nextTick } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSpace,
  NTag,
  NPopconfirm,
  NSelect,
  NDatePicker,
  NSwitch,
  NStatistic,
  NGrid,
  NGridItem,
  NCard,
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'

defineOptions({ name: 'AIRelief兑换码管理' })

// 默认有效期时长配置（毫秒）
const DEFAULT_VALIDITY_DURATION = 24 * 60 * 60 * 1000  // 24小时，可自行修改：1小时=1*60*60*1000，1天=24*60*60*1000，1年=365*24*60*60*1000

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')
const batchModalVisible = ref(false)
const batchForm = ref({
  count: 10,
  type: 'discount',
  value: 0,
  max_uses: 1,
  valid_from: null,
  valid_until: null,
  code_length: 8,
  code_prefix: '',
  use_separator: true,
})

/**
 * AI-Relief 兑换码管理功能
 * 
 * 功能说明：
 * - 兑换码列表管理（查询、筛选、分页）
 * - 创建兑换码（单个/批量）
 * - 编辑兑换码信息
 * - 删除兑换码
 * - 查看兑换码使用记录
 * - 支持折扣和时长两种类型
 */

const {
  modalVisible,
  modalTitle,
  modalAction,
  modalLoading,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleEdit: originalHandleEdit,
  handleDelete,
  handleAdd: originalHandleAdd,
} = useCRUD({
  name: '兑换码',
  initForm: {
    type: 'discount',
    value: 0,
    max_uses: 1,
    valid_from: null,
    valid_until: null,
    is_active: true,
    code: '',
    code_length: 8,
    code_prefix: '',
    use_separator: true,
  },
  doCreate: (data) => {
    try {
      // 确保日期格式正确
      const formatDate = (timestamp) => {
        if (!timestamp) return new Date().toISOString();
        const date = new Date(timestamp);
        return date.toISOString();
      };

      const payload = {
        ...data,
        valid_from: formatDate(data.valid_from),
        valid_until: formatDate(data.valid_until) || formatDate(Date.now() + DEFAULT_VALIDITY_DURATION),
      };
      
      console.log('创建兑换码数据:', payload);
      return api.createCoupon(payload);
    } catch (error) {
      console.error('创建兑换码数据处理失败:', error);
      throw error;
    }
  },
  doUpdate: (data) => {
    try {
      const payload = {
        id: data.id,
        type: data.type,
        value: data.value,
        max_uses: data.max_uses,
        valid_from: data.valid_from ? new Date(data.valid_from).toISOString() : undefined,
        valid_until: data.valid_until ? new Date(data.valid_until).toISOString() : undefined,
        is_active: data.is_active,
      }
      console.log('更新兑换码数据:', payload);
      return api.updateCoupon(payload)
    } catch (error) {
      console.error('更新兑换码数据处理失败:', error);
      throw error;
    }
  },
  doDelete: (data) => {
    return api.deleteCoupon({ id: data.id })
  },
  refresh: () => $table.value?.handleSearch(),
})

// 自定义编辑处理函数，转换日期格式
const handleEdit = (row) => {
  try {
    console.log('编辑兑换码，原始数据:', row)
    
    // 转换日期字符串为时间戳供NDatePicker使用
    const editData = { ...row }
    if (editData.valid_from) {
      editData.valid_from = new Date(editData.valid_from).getTime()
    }
    if (editData.valid_until) {
      editData.valid_until = new Date(editData.valid_until).getTime()
    }
    
    console.log('编辑兑换码，转换后数据:', editData)
    originalHandleEdit(editData)
  } catch (error) {
    console.error('编辑兑换码失败:', error)
    window.$message?.error('打开编辑窗口失败')
  }
}

// 自定义添加处理函数
const handleAdd = () => {
  try {
    console.log('添加兑换码')
    // 设置默认的日期值
    const now = Date.now()
    const defaultEndTime = now + DEFAULT_VALIDITY_DURATION
    
    originalHandleAdd()
    
    // 在下一个tick中设置默认值
    nextTick(() => {
      modalForm.value.valid_from = now
      modalForm.value.valid_until = defaultEndTime
    })
  } catch (error) {
    console.error('添加兑换码失败:', error)
  }
}

// 通用的日期验证函数
const validateDateRange = (validFrom, validUntil, isCreating = false) => {
  if (!validFrom || !validUntil) {
    window.$message?.error('请选择完整的有效期时间！')
    return false
  }
  
  // 检查结束时间是否晚于开始时间
  if (validUntil <= validFrom) {
    window.$message?.error('有效期结束时间必须晚于开始时间，请重新设置！')
    return false
  }
  
  // 检查结束时间不能是过去时间（创建时）
  if (isCreating && validUntil <= Date.now()) {
    window.$message?.error('有效期结束时间不能是过去时间，请重新设置！')
    return false
  }
  
  return true
}

// 移除高阶函数，直接使用简单的验证函数

// 自定义保存处理函数
const handleSave = () => {
  console.log('保存兑换码，表单数据:', modalForm.value)
  console.log('modalAction:', modalAction.value)
  
  // 使用封装的验证函数进行最终验证
  const { valid_from, valid_until } = modalForm.value
  const isCreating = modalAction.value === 'add'
  
  console.log('验证参数:', { valid_from, valid_until, isCreating })
  
  if (!validateDateRange(valid_from, valid_until, isCreating)) {
    console.log('验证失败，中断保存')
    return // 验证失败，中断保存
  }
  
  console.log('验证通过，继续保存')
  originalHandleSave()
}

// 监听表单中日期的变化，实现实时验证（与批量创建保持一致）
const handleValidFromChange = () => {
  console.log('单个创建 - 开始时间变化:', modalForm.value.valid_from)
  // 使用统一的验证逻辑进行实时检查
  const { valid_from, valid_until } = modalForm.value
  if (valid_from && valid_until) {
    const isCreating = modalAction.value === 'add'
    console.log('单个创建 - 验证开始时间:', { valid_from, valid_until, isCreating })
    if (!validateDateRange(valid_from, valid_until, isCreating)) {
      // 验证失败时的处理已在validateDateRange中完成
    }
  }
}

const handleValidUntilChange = () => {
  console.log('单个创建 - 结束时间变化:', modalForm.value.valid_until)
  // 使用统一的验证逻辑进行实时检查
  const { valid_from, valid_until } = modalForm.value
  if (valid_from && valid_until) {
    const isCreating = modalAction.value === 'add'
    console.log('单个创建 - 验证结束时间:', { valid_from, valid_until, isCreating })
    if (!validateDateRange(valid_from, valid_until, isCreating)) {
      // 验证失败时的处理已在validateDateRange中完成
    }
  }
}

// 检查兑换码是否可用的辅助函数
const isCouponAvailable = (coupon) => {
  if (!coupon.is_active) return false
  
  const now = new Date()
  const validFrom = new Date(coupon.valid_from)
  const validUntil = new Date(coupon.valid_until)
  
  if (now < validFrom || now > validUntil) return false
  if (coupon.used_count >= coupon.max_uses) return false
  
  return true
}

// 兑换码类型选项
const typeOptions = [
  { label: '折扣优惠', value: 'discount' },
  { label: '时长增加', value: 'duration' },
]

// 兑换码状态选项
const statusOptions = [
  { label: '可用', value: 'available' },
  { label: '已过期', value: 'expired' },
  // { label: '已禁用', value: 'disabled' },
  { label: '未生效', value: 'pending' },
  { label: '已用完', value: 'exhausted' },
]

// 批量创建兑换码
const handleBatchCreate = async () => {
  try {
    // 使用封装的验证函数进行最终验证
    const { valid_from, valid_until } = batchForm.value
    
    if (!validateDateRange(valid_from, valid_until, true)) {
      return // 验证失败，中断创建
    }
    
    const payload = {
      ...batchForm.value,
      valid_from: batchForm.value.valid_from ? new Date(batchForm.value.valid_from).toISOString() : new Date().toISOString(),
      valid_until: batchForm.value.valid_until ? new Date(batchForm.value.valid_until).toISOString() : new Date(Date.now() + DEFAULT_VALIDITY_DURATION).toISOString(),
    }
    
    console.log('批量创建兑换码数据:', payload);
    await api.batchCreateCoupon(payload)
    window.$message?.success('批量创建兑换码成功')
    batchModalVisible.value = false
    $table.value?.handleSearch()
  } catch (error) {
    console.error('批量创建兑换码失败:', error);
    console.error('错误详情:', error.response?.data || error.message);
    window.$message?.error('批量创建兑换码失败：' + (error.response?.data?.detail || error.message || '未知错误'))
  }
}

// 查看使用记录
const handleViewUsageRecords = async (row) => {
  try {
    const response = await api.getCouponUsageRecords({ coupon_id: row.id })
    // 修正：使用记录API也返回SuccessExtra格式，total在顶层
    window.$message?.success(`兑换码 ${row.code} 共有 ${response.total} 条使用记录`)
    console.log('使用记录', response)
    // TODO: 可以在这里打开一个弹窗显示详细的使用记录
  } catch (error) {
    window.$message?.error('获取使用记录失败：' + (error.message || '未知错误'))
    console.error('获取使用记录错误:', error)
  }
}

// 复制兑换码
const copyCouponCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    window.$message?.success('兑换码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    window.$message?.error('复制失败，请手动复制')
  }
}

// 计算统计信息
const statistics = computed(() => {
  return {
    total: 0,
    active: 0,
    used: 0,
    expired: 0,
  }
})

const columns = [
  {
    title: '兑换码',
    key: 'code',
    width: 180,
    align: 'center',
    render(row) {
      return h(
        'div',
        { style: 'display: flex; align-items: center; justify-content: center; gap: 8px;' },
        [
          h('span', { style: 'font-family: monospace;' }, row.code),
          h(
            NButton,
            {
              size: 'tiny',
              type: 'text',
              style: 'padding: 2px 4px; min-width: auto;',
              onClick: () => copyCouponCode(row.code),
            },
            {
              icon: renderIcon('material-symbols:content-copy', { size: 14 }),
            }
          ),
        ]
      )
    },
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    align: 'center',
    render(row) {
      const typeMap = {
        'discount': { text: '折扣优惠', type: 'success' },
        'duration': { text: '时长增加', type: 'info' },
      }
      const typeInfo = typeMap[row.type] || { text: '未知', type: 'default' }
      return h(
        NTag,
        { type: typeInfo.type, size: 'small' },
        { default: () => typeInfo.text }
      )
    },
  },
  {
    title: '优惠金额',
    key: 'value',
    width: 120,
    align: 'center',
    render(row) {
      if (row.type === 'discount') {
        return `¥${row.value}`
      } else if (row.type === 'duration') {
        return `${Math.floor(row.value / 3600)}小时`
      }
      return row.value
    },
  },
  {
    title: '使用次数',
    key: 'usage',
    width: 100,
    align: 'center',
    render(row) {
      return `${row.used_count}/${row.max_uses}`
    },
  },
  {
    title: '状态',
    key: 'is_active',
    width: 100,
    align: 'center',
    render(row) {
      const now = new Date()
      const validFrom = new Date(row.valid_from)
      const validUntil = new Date(row.valid_until)
      
      let status, statusType
      if (!row.is_active) {
        status = '已禁用'
        statusType = 'default'
      } else if (now < validFrom) {
        status = '未生效'
        statusType = 'warning'
      } else if (now > validUntil) {
        status = '已过期'
        statusType = 'error'
      } else if (row.used_count >= row.max_uses) {
        status = '已用完'
        statusType = 'default'
      } else {
        status = '可用'
        statusType = 'success'
      }
      
      return h(
        NTag,
        { type: statusType, size: 'small' },
        { default: () => status }
      )
    },
  },
  {
    title: '有效期开始',
    key: 'valid_from',
    width: 150,
    align: 'center',
    render(row) {
      return formatDate(row.valid_from, 'YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '有效期结束',
    key: 'valid_until',
    width: 150,
    align: 'center',
    render(row) {
      return formatDate(row.valid_until, 'YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 150,
    align: 'center',
    render(row) {
      return formatDate(row.created_at, 'YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'info',
            style: 'margin-right: 8px;',
            onClick: () => handleViewUsageRecords(row),
          },
          {
            default: () => '使用记录',
            icon: renderIcon('material-symbols:history', { size: 16 }),
          }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-right: 8px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: renderIcon('material-symbols:edit', { size: 16 }),
          }
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  size: 'small',
                  type: 'error',
                },
                {
                  default: () => '删除',
                  icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                }
              ),
            default: () => h('div', {}, '确定删除该兑换码吗？'),
          }
        ),
      ]
    },
  },
]

const validateForm = {
  type: [
    {
      required: true,
      message: '请选择兑换码类型',
      trigger: ['change', 'blur'],
    },
  ],
  value: [
    {
      required: true,
      type: 'number',
      message: '请输入优惠金额',
      trigger: ['input', 'blur'],
    },
    {
      validator: (rule, value) => {
        if (value == null || value === '' || value <= 0) {
          return new Error('优惠金额必须大于0')
        }
        return true
      },
      trigger: ['input', 'blur'],
    },
  ],
  max_uses: [
    {
      required: true,
      type: 'number',
      message: '请输入最大使用次数',
      trigger: ['input', 'blur'],
    },
    {
      validator: (rule, value) => {
        if (value == null || value === '' || value < 1) {
          return new Error('最大使用次数必须大于0')
        }
        return true
      },
      trigger: ['input', 'blur'],
    },
  ],
  // 日期验证移除了path属性，只依赖保存时的手动验证
}

const batchValidateForm = {
  count: [
    {
      required: true,
      type: 'number',
      message: '请输入生成数量',
      trigger: ['input', 'blur'],
    },
    {
      validator: (rule, value) => {
        if (value == null || value === '' || value < 1 || value > 1000) {
          return new Error('生成数量必须在1-1000之间')
        }
        return true
      },
      trigger: ['input', 'blur'],
    },
  ],
  type: [
    {
      required: true,
      message: '请选择兑换码类型',
      trigger: ['change', 'blur'],
    },
  ],
  value: [
    {
      required: true,
      type: 'number',
      message: '请输入优惠金额',
      trigger: ['input', 'blur'],
    },
    {
      validator: (rule, value) => {
        if (value == null || value === '' || value <= 0) {
          return new Error('优惠金额必须大于0')
        }
        return true
      },
      trigger: ['input', 'blur'],
    },
  ],
  valid_from: [
    {
      required: true,
      type: 'number',
      message: '请选择有效期开始时间',
      trigger: ['change', 'blur'],
    },
    {
      validator: (rule, value) => {
        if (!value) return true;
        const validUntil = batchForm.value.valid_until;
        if (validUntil && value >= validUntil) {
          return new Error('有效期开始时间必须早于结束时间');
        }
        return true;
      },
      trigger: ['change', 'blur'],
    },
  ],
  valid_until: [
    {
      required: true,
      type: 'number',
      message: '请选择有效期结束时间',
      trigger: ['change', 'blur'],
    },
    {
      validator: (rule, value) => {
        if (!value) return true;
        const validFrom = batchForm.value.valid_from;
        if (validFrom && value <= validFrom) {
          return new Error('有效期结束时间必须晚于开始时间');
        }
        if (value <= Date.now()) {
          return new Error('有效期结束时间不能是过去时间');
        }
        return true;
      },
      trigger: ['change', 'blur'],
    },
  ],
}

// 计算兑换码状态的函数
const calculateCouponStatus = (coupon) => {
  if (!coupon.is_active) return 'disabled'
  
  const now = new Date()
  const validFrom = new Date(coupon.valid_from)
  const validUntil = new Date(coupon.valid_until)
  
  if (now < validFrom) return 'pending'
  if (now > validUntil) return 'expired'
  if (coupon.used_count >= coupon.max_uses) return 'exhausted'
  return 'available'
}

// 获取兑换码列表
const getCouponList = async (params) => {
  try {
    console.log('获取兑换码列表参数:', params) // 添加调试日志
    
    // 提取状态筛选参数，其他参数传给后端
    const { status_filter, ...backendParams } = params
    
    const response = await api.getCouponList(backendParams)
    console.log('获取兑换码列表响应:', response) // 添加调试日志
    
    let data = response.data || []
    
    // 如果有状态筛选，在前端进行筛选
    if (status_filter) {
      data = data.filter(coupon => calculateCouponStatus(coupon) === status_filter)
    }
    
    return {
      data: data,
      total: data.length  // 筛选后的总数
    }
  } catch (error) {
    console.error('获取兑换码列表失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    return { data: [], total: 0 }
  }
}

// 批量创建表单的日期变化监听
const handleBatchValidFromChange = () => {
  // 使用统一的验证逻辑进行实时检查
  const { valid_from, valid_until } = batchForm.value
  if (valid_from && valid_until) {
    if (!validateDateRange(valid_from, valid_until, true)) {
      // 验证失败时的处理已在validateDateRange中完成
    }
  }
}

const handleBatchValidUntilChange = () => {
  // 使用统一的验证逻辑进行实时检查
  const { valid_from, valid_until } = batchForm.value
  if (valid_from && valid_until) {
    if (!validateDateRange(valid_from, valid_until, true)) {
      // 验证失败时的处理已在validateDateRange中完成
    }
  }
}

// 筛选条件变化处理函数
const handleTypeChange = (value) => {
  console.log('类型筛选变化:', value)
  console.log('typeOptions:', typeOptions)
  queryItems.value.type = value
  $table.value?.handleSearch()
}

const handleStatusChange = (value) => {
  console.log('状态筛选变化:', value)
  console.log('statusOptions:', statusOptions)
  queryItems.value.status_filter = value
  $table.value?.handleSearch()
}

// 添加页面加载时的调试信息
onMounted(() => {
  console.log('页面加载完成')
  console.log('typeOptions:', typeOptions)
  console.log('typeOptions详细:', JSON.stringify(typeOptions, null, 2))
  console.log('statusOptions:', statusOptions)
  console.log('statusOptions详细:', JSON.stringify(statusOptions, null, 2))
  console.log('queryItems:', queryItems.value)
  $table.value?.handleSearch()
})
</script>

<template>
  <CommonPage show-footer title="AIRelief兑换码管理">
    <template #action>
      <NSpace>
        <NButton type="primary" @click="handleAdd">
          <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />创建兑换码
        </NButton>
        <NButton type="info" @click="batchModalVisible = true">
          <TheIcon icon="material-symbols:library-add" :size="18" class="mr-5" />批量创建
        </NButton>
        <NButton @click="$table?.handleSearch()">
          <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5" />刷新
        </NButton>
      </NSpace>
    </template>
    
    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getCouponList"
    >
      <template #queryBar>
        <QueryBarItem label="兑换码" :label-width="80">
          <NInput
            v-model:value="queryItems.search"
            clearable
            type="text"
            placeholder="请输入兑换码"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="类型" :label-width="80">
          <NSelect
            v-model:value="queryItems.type"
            :options="[
              { label: '折扣优惠', value: 'discount' },
              { label: '时长增加', value: 'duration' }
            ]"
            placeholder="选择类型"
            clearable
            filterable
            size="small"
            style="width: 100%;"
            @update:value="handleTypeChange"
            @focus="() => console.log('类型筛选获得焦点')"
            @click="() => console.log('类型筛选被点击')"
          />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="80">
          <NSelect
            v-model:value="queryItems.status_filter"
            :options="statusOptions"
            placeholder="选择状态"
            clearable
            filterable
            size="small"
            style="width: 100%;"
            @update:value="handleStatusChange"
            @focus="() => console.log('状态筛选获得焦点')"
            @click="() => console.log('状态筛选被点击')"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 编辑/新建弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="120"
        :model="modalForm"
        :rules="validateForm"
      >
        <NFormItem v-if="modalAction === 'edit'" label="兑换码">
          <NInput :value="modalForm.code" disabled />
        </NFormItem>
        
        <NFormItem v-if="modalAction === 'add'" label="自定义兑换码">
          <NInput
            v-model:value="modalForm.code"
            placeholder="留空自动生成"
            clearable
          />
        </NFormItem>
        
        <NFormItem label="兑换码类型" path="type">
          <NSelect
            v-model:value="modalForm.type"
            :options="typeOptions"
            placeholder="请选择兑换码类型"
          />
        </NFormItem>
        
        <NFormItem label="优惠金额" path="value">
          <NInputNumber
            v-model:value="modalForm.value"
            :placeholder="modalForm.type === 'discount' ? '请输入优惠金额(元)' : '请输入时长(秒)'"
            :min="0"
            :step="modalForm.type === 'discount' ? 0.01 : 1"
            style="width: 100%"
          />
          <template #suffix>
            <span style="color: #999; font-size: 12px;">
              {{ modalForm.type === 'discount' ? '元' : '秒' }}
            </span>
          </template>
        </NFormItem>
        
        <NFormItem label="最大使用次数" path="max_uses">
          <NInputNumber
            v-model:value="modalForm.max_uses"
            placeholder="请输入最大使用次数"
            :min="1"
            :step="1"
            style="width: 100%"
          />
        </NFormItem>
        
        <NFormItem label="有效期开始">
          <NDatePicker
            v-model:value="modalForm.valid_from"
            type="datetime"
            placeholder="请选择有效期开始时间"
            clearable
            style="width: 100%"
            @update:value="handleValidFromChange"
          />
        </NFormItem>
        
        <NFormItem label="有效期结束">
          <NDatePicker
            v-model:value="modalForm.valid_until"
            type="datetime"
            placeholder="请选择有效期结束时间"
            clearable
            style="width: 100%"
            @update:value="handleValidUntilChange"
          />
        </NFormItem>
        
        <NFormItem v-if="modalAction === 'add'" label="兑换码长度">
          <NInputNumber
            v-model:value="modalForm.code_length"
            placeholder="请输入兑换码长度"
            :min="4"
            :max="16"
            :step="1"
            style="width: 100%"
          />
        </NFormItem>
        
        <NFormItem v-if="modalAction === 'add'" label="兑换码前缀">
          <NInput
            v-model:value="modalForm.code_prefix"
            placeholder="请输入兑换码前缀(可选)"
            clearable
          />
        </NFormItem>
        
        <NFormItem v-if="modalAction === 'add'" label="使用分隔符">
          <NSwitch v-model:value="modalForm.use_separator" />
        </NFormItem>
        
        <NFormItem label="启用状态">
          <NSwitch v-model:value="modalForm.is_active" />
        </NFormItem>
      </NForm>
    </CrudModal>

    <!-- 批量创建弹窗 -->
    <CrudModal
      v-model:visible="batchModalVisible"
      title="批量创建兑换码"
      :loading="false"
      @save="handleBatchCreate"
    >
      <NForm
        label-placement="left"
        label-align="left"
        :label-width="120"
        :model="batchForm"
        :rules="batchValidateForm"
      >
        <NFormItem label="生成数量" path="count">
          <NInputNumber
            v-model:value="batchForm.count"
            placeholder="请输入生成数量"
            :min="1"
            :max="1000"
            :step="1"
            style="width: 100%"
          />
        </NFormItem>
        
        <NFormItem label="兑换码类型" path="type">
          <NSelect
            v-model:value="batchForm.type"
            :options="typeOptions"
            placeholder="请选择兑换码类型"
          />
        </NFormItem>
        
        <NFormItem label="优惠金额" path="value">
          <NInputNumber
            v-model:value="batchForm.value"
            :placeholder="batchForm.type === 'discount' ? '请输入优惠金额(元)' : '请输入时长(秒)'"
            :min="0"
            :step="batchForm.type === 'discount' ? 0.01 : 1"
            style="width: 100%"
          />
        </NFormItem>
        
        <NFormItem label="最大使用次数">
          <NInputNumber
            v-model:value="batchForm.max_uses"
            placeholder="请输入最大使用次数"
            :min="1"
            :step="1"
            style="width: 100%"
          />
        </NFormItem>
        
        <NFormItem label="有效期开始">
          <NDatePicker
            v-model:value="batchForm.valid_from"
            type="datetime"
            placeholder="请选择有效期开始时间"
            clearable
            style="width: 100%"
            @update:value="handleBatchValidFromChange"
          />
        </NFormItem>
        
        <NFormItem label="有效期结束">
          <NDatePicker
            v-model:value="batchForm.valid_until"
            type="datetime"
            placeholder="请选择有效期结束时间"
            clearable
            style="width: 100%"
            @update:value="handleBatchValidUntilChange"
          />
        </NFormItem>
        
        <NFormItem label="兑换码长度">
          <NInputNumber
            v-model:value="batchForm.code_length"
            placeholder="请输入兑换码长度"
            :min="4"
            :max="16"
            :step="1"
            style="width: 100%"
          />
        </NFormItem>
        
        <NFormItem label="兑换码前缀">
          <NInput
            v-model:value="batchForm.code_prefix"
            placeholder="请输入兑换码前缀(可选)"
            clearable
          />
        </NFormItem>
        
        <NFormItem label="使用分隔符">
          <NSwitch v-model:value="batchForm.use_separator" />
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template> 