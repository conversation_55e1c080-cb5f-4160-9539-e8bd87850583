<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives, computed } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInput,
  NSpace,
  NSwitch,
  NTag,
  NPopconfirm,
  NDatePicker,
  NSelect,
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: 'AI-Relief用户管理' })

const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

const {
  modalVisible,
  modalTitle,
  modalAction,
  modalLoading,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: 'AI-Relief用户',
  initForm: {},
  doCreate: () => Promise.reject('不支持创建用户'),
  doUpdate: api.updateAIReliefUser,
  doDelete: api.deleteAIReliefUser,
  refresh: () => $table.value?.handleSearch(),
})

// 计算剩余时长的函数
const calculateRemainingDuration = (expiryDate) => {
  if (!expiryDate) return 0
  const now = new Date()
  const expiry = new Date(expiryDate)
  const diffMs = expiry.getTime() - now.getTime()
  return Math.max(0, Math.floor(diffMs / 1000)) // 返回秒数，不能为负数
}

// 格式化时长显示
const formatDuration = (seconds) => {
  if (seconds <= 0) return '0秒'
  const days = Math.floor(seconds / (24 * 3600))
  const hours = Math.floor((seconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (minutes > 0) result += `${minutes}分`
  if (secs > 0 || result === '') result += `${secs}秒`

  return result
}

// 自定义保存处理函数，转换时间格式
const handleSave = async () => {
  try {
    // 验证表单
    if (!modalFormRef.value) {
      console.error('表单引用不存在')
      return
    }

    await modalFormRef.value.validate()

    // 转换表单数据格式
    const formData = { ...modalForm.value }

    // 转换会员到期时间为ISO字符串 (后端需要ISO格式)
    if (formData.expiryDate && typeof formData.expiryDate === 'number') {
      formData.expiryDate = new Date(formData.expiryDate).toISOString()
    }

    // 移除duration字段，让后端根据到期时间自动计算
    delete formData.duration

    // 设置加载状态
    modalLoading.value = true

    try {
      // 调用更新API
      await api.updateAIReliefUser(formData)

      // 显示成功消息
      window.$message?.success('编辑成功')

      // 关闭弹窗
      modalVisible.value = false

      // 刷新表格
      $table.value?.handleSearch()

    } finally {
      modalLoading.value = false
    }

  } catch (error) {
    console.error('保存失败:', error)
    modalLoading.value = false
    window.$message?.error(`保存失败: ${error.message || '未知错误'}`)
  }
}

// 计算剩余时长显示
const computedDurationDisplay = computed(() => {
  if (!modalForm.value.expiryDate) {
    return '未设置到期时间'
  }

  const remainingSeconds = calculateRemainingDuration(modalForm.value.expiryDate)
  return formatDuration(remainingSeconds)
})

// 处理到期时间变化
const handleExpiryDateChange = (value) => {
  console.log('到期时间变化:', value)
  // 当到期时间变化时，computed会自动重新计算剩余时长显示
}

onMounted(() => {
  $table.value?.handleSearch()
})

// 性别选项
const genderOptions = [
  { label: '未知', value: 0 },
  { label: '女性', value: 1 },
  { label: '男性', value: 2 },
]

// 编辑按钮点击处理函数
const handleEditClick = (row) => {
  try {
    console.log('=== 编辑按钮被点击 ===')
    console.log('点击编辑按钮，行数据:', row)
    
    // 转换数据格式以适应表单组件
    const formData = { ...row }
    
    // 转换会员到期时间为时间戳 (NaiveUI DatePicker 需要时间戳格式)
    if (formData.expiryDate) {
      console.log('转换前的时间:', formData.expiryDate)
      formData.expiryDate = new Date(formData.expiryDate).getTime()
      console.log('转换后的时间戳:', formData.expiryDate)
    }
    
    console.log('调用 handleEdit，formData:', formData)
    handleEdit(formData)
    console.log('handleEdit 调用完成')
    
    // 显示成功消息
    window.$message?.success('编辑窗口已打开')
  } catch (error) {
    console.error('编辑按钮点击处理失败:', error)
    window.$message?.error('打开编辑窗口失败')
  }
}

// 删除按钮点击处理函数
const handleDeleteClick = (row) => {
  try {
    console.log('点击删除按钮，行数据:', row)
    return handleDelete({ user_id: row.id }, false)
  } catch (error) {
    console.error('删除按钮点击处理失败:', error)
    window.$message?.error('删除操作失败')
  }
}

const columns = [
  {
    title: '用户ID',
    key: 'user_id',
    width: 120,
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '昵称',
    key: 'nickname',
    width: 120,
    align: 'center',
    ellipsis: { tooltip: true },
  },
  {
    title: '手机号',
    key: 'phone',
    width: 120,
    align: 'center',
    ellipsis: { tooltip: true },
    render(row) {
      return row.phone || '-'
    },
  },
  {
    title: '性别',
    key: 'gender',
    width: 80,
    align: 'center',
    render(row) {
      const genderMap = { 0: '未知', 1: '女性', 2: '男性' }
      return h(
        NTag,
        {
          type: row.gender === 1 ? 'success' : row.gender === 2 ? 'info' : 'default',
          size: 'small'
        },
        { default: () => genderMap[row.gender] || '未知' }
      )
    },
  },
  {
    title: '剩余时长',
    key: 'duration',
    width: 100,
    align: 'center',
    render(row) {
      const hours = Math.floor(row.duration / 3600)
      const minutes = Math.floor((row.duration % 3600) / 60)
      return `${hours}h ${minutes}m`
    },
  },
  {
    title: '总时长',
    key: 'totalDuration',
    width: 100,
    align: 'center',
    render(row) {
      const hours = Math.floor(row.totalDuration / 3600)
      const minutes = Math.floor((row.totalDuration % 3600) / 60)
      return `${hours}h ${minutes}m`
    },
  },
  {
    title: '会员到期',
    key: 'expiryDate',
    width: 120,
    align: 'center',
    render(row) {
      if (!row.expiryDate) return '-'
      const isExpired = new Date(row.expiryDate) < new Date()
      return h(
        NTag,
        { 
          type: isExpired ? 'error' : 'success',
          size: 'small'
        },
        { default: () => formatDate(row.expiryDate, 'YYYY-MM-DD') }
      )
    },
  },
  {
    title: '注册时间',
    key: 'created_at',
    width: 120,
    align: 'center',
    render(row) {
      return formatDate(row.created_at, 'YYYY-MM-DD HH:mm')
    },
  },
  {
    title: '最后登录',
    key: 'last_login',
    width: 120,
    align: 'center',
    render(row) {
      return row.last_login ? formatDate(row.last_login, 'YYYY-MM-DD HH:mm') : '-'
    },
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { 
          type: row.is_active ? 'success' : 'error',
          size: 'small'
        },
        { default: () => row.is_active ? '正常' : '禁用' }
      )
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-right: 8px;',
            onClick: () => handleEditClick(row),
          },
          {
            default: () => '编辑',
            icon: renderIcon('material-symbols:edit', { size: 16 }),
          }
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDeleteClick(row),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              h(
                NButton,
                {
                  size: 'small',
                  type: 'error',
                },
                {
                  default: () => '删除',
                  icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                }
              ),
            default: () => h('div', {}, '确定删除该用户吗？此操作不可恢复！'),
          }
        ),
      ]
    },
  },
]

const validateForm = {
  nickname: [
    {
      required: true,
      message: '请输入昵称',
      trigger: ['input', 'blur'],
    },
  ],
  phone: [
    {
      trigger: ['blur'],
      validator: (rule, value, callback) => {
        if (value && !/^1[3-9]\d{9}$/.test(value)) {
          callback('请输入正确的手机号')
          return
        }
        callback()
      },
    },
  ],
}


</script>

<template>
  <CommonPage show-footer title="AI-Relief用户管理">
    <template #action>
      <NSpace>
        <NButton type="primary" @click="$table?.handleSearch()">
          <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5" />刷新
        </NButton>
      </NSpace>
    </template>
    
    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getAIReliefUserList"
    >
      <template #queryBar>
        <QueryBarItem label="用户ID" :label-width="60">
          <NInput
            v-model:value="queryItems.user_id"
            clearable
            type="text"
            placeholder="请输入用户ID"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="昵称" :label-width="60">
          <NInput
            v-model:value="queryItems.nickname"
            clearable
            type="text"
            placeholder="请输入昵称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="手机号" :label-width="60">
          <NInput
            v-model:value="queryItems.phone"
            clearable
            type="text"
            placeholder="请输入手机号"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 编辑弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :rules="validateForm"
      >
        <NFormItem label="用户ID">
          <NInput :value="modalForm.user_id || ''" disabled />
        </NFormItem>
        <NFormItem label="微信OpenID">
          <NInput :value="modalForm.wechat_openid || ''" disabled />
        </NFormItem>
        <NFormItem label="昵称" path="nickname">
          <NInput v-model:value="modalForm.nickname" clearable placeholder="请输入昵称" />
        </NFormItem>
        <NFormItem label="手机号" path="phone">
          <NInput v-model:value="modalForm.phone" clearable placeholder="请输入手机号" />
        </NFormItem>
        <NFormItem label="性别">
          <NSelect
            v-model:value="modalForm.gender"
            :options="genderOptions"
            placeholder="请选择性别"
            clearable
          />
        </NFormItem>
        <NFormItem label="会员到期时间">
          <NDatePicker
            v-model:value="modalForm.expiryDate"
            type="datetime"
            placeholder="请选择会员到期时间"
            clearable
            style="width: 100%"
            @update:value="handleExpiryDateChange"
          />
        </NFormItem>
        <NFormItem label="剩余时长">
          <NInput
            :value="computedDurationDisplay"
            disabled
            placeholder="根据到期时间自动计算"
            style="width: 100%"
          />
        </NFormItem>
        <NFormItem label="IP属地">
          <NInput v-model:value="modalForm.ipLocation" clearable placeholder="请输入IP属地" />
        </NFormItem>
        <NFormItem label="状态">
          <NSwitch
            v-model:value="modalForm.is_active"
            :checked-value="true"
            :unchecked-value="false"
          >
            <template #checked>正常</template>
            <template #unchecked>禁用</template>
          </NSwitch>
        </NFormItem>
      </NForm>
    </CrudModal>
  </CommonPage>
</template>
